# NA Portal Frontend Monitoring - CI Deployment Guide

## Overview
This document describes how the monitoring Docker image is built, configured, and run in the CI environment.

## CI Build Process

### Jenkins Pipeline
The monitoring image is built and deployed through a Jenkins pipeline using Maven:

**Pipeline Location**: `build/jenkins/docker/Jenkinsfile`

**Build Commands**:
- **Testing Branch**: `mvn clean deploy -T 1C -Pdocker,docker-dev -f branch/src/dist/docker/pom.xml`
- **Release Branch**: `mvn clean deploy -T 1C -Pdocker,docker-release -f branch/src/dist/docker/pom.xml`

### Build Triggers
- **Testing**: Branches starting with `trunk` or `branches`
- **Release**: Branches starting with `tags`

## Docker Image Configuration

### Image Properties
- **Image Name**: `na-portal-frontend-monitoring`
- **Base Image**: `rdocker.ptin.corppt.com/na-portal-tst/na-portal-frontend-base:100.0.0`
- **Summary**: NA Portal Frontend Monitoring image
- **OS**: CentOS 7
- **User/Group**: 2016:2016 (alabs)

### Registry Locations
- **Testing**: `rdocker.ptin.corppt.com/na-portal-tst/na-portal-frontend-monitoring`
- **Production**: `rdocker.ptin.corppt.com/na-portal/na-portal-frontend-monitoring`

## Container Runtime Configuration

### Service Configuration
- **Service Name**: `na-portal-frontend-monitoring`
- **Systemd Service**: `pdocker@na-portal-frontend-monitoring`
- **Exposed Port**: 19001 (host) → 19001 (container)
- **Environment Variable**: `NOSSIS_UI_PORT=19001`

### Volume Mounts
1. **Logs**: 
   - Host: `/var/log/alticelabs/na-portal/frontend-monitoring`
   - Container: `/var/log/alticelabs/na-portal/web/`

2. **Configuration**:
   - Host: `/opt/alticelabs/na-portal/conf/na-portal-frontend-monitoring/config/all.yml`
   - Container: `/config/all.yml` (read-only)

3. **SSL Certificates**:
   - Host: `{{ na_portal_iam_host_cert_path }}`
   - Container: `/certificates/na-portal-iam-service-public-cert.pem`

4. **Optional I18N Customization**:
   - Host: `{{ na_portal_frontend_i18n_customization_dir_path }}`
   - Container: `/opt/alticelabs/na-portal/conf/i18n/customization`

5. **Optional IAM Connection Certificate**:
   - Host: `{{ iam_source_host_cert_file }}`
   - Container: `/certificates/iam_conn.pem`

### Docker Run Command Template
```bash
docker run -d \
  -p 19001:19001 \
  -v /var/log/alticelabs/na-portal/frontend-monitoring:/var/log/alticelabs/na-portal/web/ \
  -v /opt/alticelabs/na-portal/conf/na-portal-frontend-monitoring/config/all.yml:/config/all.yml:ro \
  -v {{ na_portal_iam_host_cert_path }}:/certificates/na-portal-iam-service-public-cert.pem \
  --name na-portal-frontend-monitoring \
  rdocker.ptin.corppt.com/na-portal-tst/na-portal-frontend-monitoring:VERSION
```

## Deployment Process

### Ansible Deployment Steps
1. **Pull Image**: `docker pull {{ na_portal_frontend_monitoring_docker_image }}`
2. **Stop Service**: `systemctl stop pdocker@na-portal-frontend-monitoring`
3. **Install Configuration**: Create systemd configuration and volume directories
4. **Start Service**: `systemctl start pdocker@na-portal-frontend-monitoring`

### Configuration Files
- **Systemd Config**: `/etc/sysconfig/na-portal-frontend-monitoring`
- **Application Config**: `/opt/alticelabs/na-portal/conf/na-portal-frontend-monitoring/config/all.yml`
- **Ansible Playbook**: `automation/docker-na-portal-frontend-monitoring.yml`

### File Permissions
- **Host User/Group**: 2016:2016
- **Log Directory**: `/var/log/alticelabs/na-portal/frontend-monitoring` (mode: 0755)
- **Config Directory**: `/opt/alticelabs/na-portal/conf/na-portal-frontend-monitoring/config/`

## Service Management

### Systemd Commands
```bash
# Check service status
systemctl status pdocker@na-portal-frontend-monitoring

# Start service
systemctl start pdocker@na-portal-frontend-monitoring

# Stop service
systemctl stop pdocker@na-portal-frontend-monitoring

# Restart service
systemctl restart pdocker@na-portal-frontend-monitoring

# Enable auto-start
systemctl enable pdocker@na-portal-frontend-monitoring
```

### Docker Commands
```bash
# Pull latest image
docker pull rdocker.ptin.corppt.com/na-portal-tst/na-portal-frontend-monitoring:VERSION

# List running containers
docker ps | grep na-portal-frontend-monitoring

# View container logs
docker logs na-portal-frontend-monitoring

# Remove container
docker rm -f na-portal-frontend-monitoring
```

## Troubleshooting

### Common Issues
1. **Port Conflicts**: Ensure port 19001 is available
2. **Volume Permissions**: Check that user 2016 has access to mounted directories
3. **Certificate Issues**: Verify IAM certificate paths are correct
4. **Configuration**: Ensure `all.yml` configuration file exists and is readable

### Log Locations
- **Container Logs**: `/var/log/alticelabs/na-portal/frontend-monitoring/`
- **Systemd Logs**: `journalctl -u pdocker@na-portal-frontend-monitoring`
- **Docker Logs**: `docker logs na-portal-frontend-monitoring`

## Version Management

### Image Versioning
- **SNAPSHOT versions**: Use `-tst` registry suffix
- **Release versions**: Use production registry
- **Version format**: Follows project version (e.g., 10.3.0, 100.0.0)

### Deployment Profiles
- **docker-dev**: For development/testing branches
- **docker-release**: For release tags
- **github**: Additional deployment to GitHub registry when enabled
