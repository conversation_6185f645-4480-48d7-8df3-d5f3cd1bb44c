
package na.operationscatalog.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object js extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script type="text/javascript" src=""""),_display_(/*1.38*/na/*1.40*/.operationscatalog.controllers.routes.Application.javascriptRoutes()),format.raw/*1.108*/(""""></script>
<% for(var i=0; i < assets.js.length; i++) """),format.raw/*2.44*/("""{"""),format.raw/*2.45*/(""" """),format.raw/*2.46*/("""%>
    <script type="text/javascript" src='"""),_display_(/*3.42*/na/*3.44*/.operationscatalog.controllers.routes.Assets.versioned("<%= assets.js[i] %>")),format.raw/*3.121*/("""'></script>
<% """),format.raw/*4.4*/("""}"""),format.raw/*4.5*/(""" """),format.raw/*4.6*/("""%>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:59 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/imports/js.scala.html
                  HASH: 51d4f1a37a2cab38aef52aae38fe721a9c5cd94d
                  MATRIX: 1056->0|1119->37|1129->39|1218->107|1300->162|1328->163|1356->164|1426->208|1436->210|1534->287|1575->302|1602->303|1629->304
                  LINES: 33->1|33->1|33->1|33->1|34->2|34->2|34->2|35->3|35->3|35->3|36->4|36->4|36->4
                  -- GENERATED --
              */
          