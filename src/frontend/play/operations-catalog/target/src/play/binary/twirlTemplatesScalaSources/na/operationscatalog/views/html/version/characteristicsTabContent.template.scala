
package na.operationscatalog.views.html.version

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.operationscatalog.models.operations.Version
/*3.2*/import na.catalog.basemodule.ui.{Label, WebComponent}

object characteristicsTabContent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[Version,List[WebComponent],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(version: Version, elements: List[WebComponent]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*5.50*/("""

"""),format.raw/*7.1*/("""<!-- dynamic elements -->
    <div class="form-controls">
        """),_display_(/*9.10*/for(element <- elements) yield /*9.34*/ {_display_(Seq[Any](format.raw/*9.36*/("""
            """),format.raw/*10.13*/("""<div class="form-group">
                <div class="col-sm-2">
                    """),_display_(/*12.22*/if(element.getI18n != null)/*12.49*/ {_display_(Seq[Any](format.raw/*12.51*/("""
                        """),_display_(/*13.26*/{
                            val jMap = new java.util.HashMap[String,String]()
                            jMap.put("class", "control-label")
                            val label = new Label(element.getId, element.getI18n)
                            label.setDataAttributes(jMap)
                            label.render()
                        }),format.raw/*19.26*/("""
                    """)))}),format.raw/*20.22*/("""
                """),format.raw/*21.17*/("""</div>
                <div class="col-sm-10">
                    <p class="form-control-static">"""),_display_(/*23.53*/element/*23.60*/.render()),format.raw/*23.69*/("""</p>
                </div>
            </div>
        """)))}),format.raw/*26.10*/("""
    """),format.raw/*27.5*/("""</div>

<!-- tables tab -->
<div class="version-characteristics-tab" data-na-portal-operations-catalog-version-characteristics-tab>

    <!-- header controls -->
    <div class="version-characteristics-tab__header form-controls">

        <!-- left side buttons -->
        <div class="btn-group">
                <!-- operations button -->
            <button class="btn btn-default btn-xs active" title=""""),_display_(/*38.67*/i18n("na.portal.operationscatalog.button.operations.title")),format.raw/*38.126*/("""" type="button">
                <span id="operationsCount"></span>&nbsp;"""),_display_(/*39.58*/i18n("na.portal.operationscatalog.button.operations")),format.raw/*39.111*/("""
            """),format.raw/*40.13*/("""</button>
                <!-- response codes button -->
            <button class="btn btn-default btn-xs" title=""""),_display_(/*42.60*/i18n("na.portal.operationscatalog.button.responseCodes.title")),format.raw/*42.122*/("""" type="button">
                <span id="responseCodesCount"></span>&nbsp;"""),_display_(/*43.61*/i18n("na.portal.operationscatalog.button.responseCodes")),format.raw/*43.117*/("""
            """),format.raw/*44.13*/("""</button>
        </div>

        <!-- right side operations search input -->
        <div id="searchOperations">
            <div class="form-group fx-search-input">
                <div class="fx-pos-rel">
                    <input class="form-control input-sm" type="text" name="searchByName" id="operationsSearchInput"
                    placeholder=""""),_display_(/*52.35*/i18n("na.portal.operationscatalog.search.name.placeholder")),format.raw/*52.94*/("""">
                    <button class="glyphicon glyphicon-search" type="button" id="operationsSearchSubmit"></button>
                </div>
            </div>
        </div>

        <!-- right side response codes search input -->
        <div class="hidden" id="searchResponseCodes">
            <div class="form-group fx-search-input">
                <div class="fx-pos-rel">
                    <input class="form-control input-sm" type="text" name="searchByName" id="responseCodesSearchInput"
                    placeholder=""""),_display_(/*63.35*/i18n("na.portal.operationscatalog.search.code.placeholder")),format.raw/*63.94*/("""">
                    <button class="glyphicon glyphicon-search" type="button" id="responseCodesSearchSubmit"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- data tables -->
    <div class="version-characteristics-tab__content tab-content tab-fix">

        <!-- operations table -->
        <div class="tab-pane in active">
            <table
            class="table table-striped dataTable no-footer"
            id="version-operations-table"
            data-na-portal-table-datatable
            data-na-portal-table-load-using-ajax
            data-na-portal-operations-catalog-version-operations-data-table
            data-config-url=""""),_display_(/*81.31*/na/*81.33*/.operationscatalog.controllers.routes.VersionController.viewOperationsDataTable(version.getName)),format.raw/*81.129*/(""""
            ></table>
        </div>

        <!-- response codes table -->
        <div class="tab-pane">
            <table
            class="table dataTable no-footer"
            id="version-response-codes-table"
            data-na-portal-table-datatable
            data-na-portal-table-load-using-ajax
            data-na-portal-operations-catalog-version-response-codes-data-table
            data-config-url=""""),_display_(/*93.31*/na/*93.33*/.operationscatalog.controllers.routes.VersionController.viewResponseCodesDataTable(version.getName)),format.raw/*93.132*/(""""
            ></table>
        </div>

    </div>
</div>"""))
      }
    }
  }

  def render(version:Version,elements:List[WebComponent]): play.twirl.api.HtmlFormat.Appendable = apply(version,elements)

  def f:((Version,List[WebComponent]) => play.twirl.api.HtmlFormat.Appendable) = (version,elements) => apply(version,elements)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:59 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/version/characteristicsTabContent.scala.html
                  HASH: 9fba78675f968a7cae5878eac0f972857743f272
                  MATRIX: 680->1|726->41|787->96|1185->152|1328->200|1356->202|1449->269|1488->293|1527->295|1568->308|1680->393|1716->420|1756->422|1809->448|2181->799|2234->821|2279->838|2405->937|2421->944|2451->953|2538->1009|2570->1014|3004->1421|3085->1480|3186->1554|3261->1607|3302->1620|3445->1736|3529->1798|3633->1875|3711->1931|3752->1944|4137->2302|4217->2361|4777->2894|4857->2953|5569->3638|5580->3640|5698->3736|6147->4158|6158->4160|6279->4259
                  LINES: 24->1|25->2|26->3|31->5|36->5|38->7|40->9|40->9|40->9|41->10|43->12|43->12|43->12|44->13|50->19|51->20|52->21|54->23|54->23|54->23|57->26|58->27|69->38|69->38|70->39|70->39|71->40|73->42|73->42|74->43|74->43|75->44|83->52|83->52|94->63|94->63|112->81|112->81|112->81|124->93|124->93|124->93
                  -- GENERATED --
              */
          