
package na.operationscatalog.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object mainSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(title: String = "")(content: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*3.2*/scripts/*3.9*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*3.13*/("""
    """),_display_(/*4.6*/na/*4.8*/.operationscatalog.views.html.imports.scripts.render()),format.raw/*4.62*/("""
""")))};def /*7.2*/body/*7.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*7.10*/("""
    """),format.raw/*8.5*/("""<div id="operationscatalog" data-na-portal-page-container class="page page--operations-catalog fx-full-height inherit">
        """),_display_(/*9.10*/content),format.raw/*9.17*/("""
    """),format.raw/*10.5*/("""</div>
""")))};
Seq[Any](format.raw/*1.37*/("""

"""),format.raw/*5.2*/("""

"""),format.raw/*11.2*/("""

"""),_display_(/*13.2*/na/*13.4*/.naportalbase.views.html.skeletons.root.render(title, body, scripts)))
      }
    }
  }

  def render(title:String,content:Html): play.twirl.api.HtmlFormat.Appendable = apply(title)(content)

  def f:((String) => (Html) => play.twirl.api.HtmlFormat.Appendable) = (title) => (content) => apply(title)(content)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:59 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/skeletons/mainSkel.scala.html
                  HASH: 89da5e3c8b5f7be1c0119e47d484b47ee6efa0fc
                  MATRIX: 987->1|1100->39|1114->46|1194->50|1225->56|1234->58|1308->112|1332->117|1343->121|1423->125|1454->130|1609->259|1636->266|1668->271|1715->36|1743->114|1772->279|1801->282|1811->284
                  LINES: 28->1|32->3|32->3|34->3|35->4|35->4|35->4|36->7|36->7|38->7|39->8|40->9|40->9|41->10|43->1|45->5|47->11|49->13|49->13
                  -- GENERATED --
              */
          