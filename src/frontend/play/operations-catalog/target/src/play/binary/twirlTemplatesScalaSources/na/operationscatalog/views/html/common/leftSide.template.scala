
package na.operationscatalog.views.html.common

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.catalog.basemodule.ui.{Label, WebComponent}
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp

object leftSide extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[List[WebComponent],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(elements: List[WebComponent]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/searchButtonAttributes/*7.24*/ = {{
    val map = scala.collection.mutable.HashMap[String, String]()
    map.put("data-action", "search")
    map.put("id", "searchButton")
    map.put("type", "submit")
    map.put("value", i18n("na.buttons.search"))
    map
}};def /*16.2*/clearButtonAttributes/*16.23*/ = {{
    val map = scala.collection.mutable.HashMap[String, String]()
    map.put("data-action", "clear")
    map.put("id", "clearSearchButton")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))
    map
}};
Seq[Any](format.raw/*5.32*/("""

"""),format.raw/*14.2*/("""

"""),format.raw/*23.2*/("""

"""),format.raw/*25.1*/("""<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
        </p>
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-shadow-scroll vertical>
    """),_display_(/*37.6*/for(element <- elements) yield /*37.30*/ {_display_(Seq[Any](format.raw/*37.32*/("""
        """),format.raw/*38.9*/("""<div class="form-group">
            """),_display_(/*39.14*/if(element.getI18n != null)/*39.41*/ {_display_(Seq[Any](format.raw/*39.43*/("""
                """),_display_(/*40.18*/{new Label(element.getId, element.getI18n).render()}),format.raw/*40.70*/("""
            """)))}),format.raw/*41.14*/("""
            """),_display_(/*42.14*/element/*42.21*/.render()),format.raw/*42.30*/("""
        """),format.raw/*43.9*/("""</div>
    """)))}),format.raw/*44.6*/("""
    """),format.raw/*45.5*/("""</x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
        """),_display_(/*50.10*/leftSideFormInputButtonsComp/*50.38*/.render(searchButtonAttributes,clearButtonAttributes)),format.raw/*50.91*/("""
        """),format.raw/*51.9*/("""</div>
    </div>
</div>"""))
      }
    }
  }

  def render(elements:List[WebComponent]): play.twirl.api.HtmlFormat.Appendable = apply(elements)

  def f:((List[WebComponent]) => play.twirl.api.HtmlFormat.Appendable) = (elements) => apply(elements)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:59 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/common/leftSide.scala.html
                  HASH: 0fe9ff5eda29f6b304380a72418adff48f8e6b6b
                  MATRIX: 679->1|740->56|786->96|1191->184|1299->217|1329->239|1572->471|1602->492|1863->214|1892->468|1921->723|1950->725|2410->1159|2450->1183|2490->1185|2526->1194|2591->1232|2627->1259|2667->1261|2712->1279|2785->1331|2830->1345|2871->1359|2887->1366|2917->1375|2953->1384|2995->1396|3027->1401|3244->1591|3281->1619|3355->1672|3391->1681
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|42->16|42->16|50->5|52->14|54->23|56->25|68->37|68->37|68->37|69->38|70->39|70->39|70->39|71->40|71->40|72->41|73->42|73->42|73->42|74->43|75->44|76->45|81->50|81->50|81->50|82->51
                  -- GENERATED --
              */
          