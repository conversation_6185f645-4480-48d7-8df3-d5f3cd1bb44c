
package na.operationscatalog.views.html.version

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.operationscatalog.models.operations.Version
/*3.2*/import na.operationscatalog.settings.{AAAPIResources, OperationsCatalogConstants}
/*4.2*/import na.catalog.basemodule.ui.WebComponent
/*5.2*/import na.operationscatalog.views.html.common.saveEntityButtons
/*6.2*/import na.operationscatalog.views.html.skeletons.mainSkel
/*7.2*/import play.libs.Json
/*8.2*/import scala.collection.immutable.ListMap

object edit extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[Version,List[WebComponent],Map[String, WebComponent],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(version: Version, elements: List[WebComponent], headerComponents: Map[String, WebComponent]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*12.2*/headerActions/*12.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*12.19*/("""
    """),_display_(/*13.6*/saveEntityButtons/*13.23*/.render(
        Array(AAAPIResources.operationsCatalog.U.toString),
        ListMap("data-page-action" -> "submitEntity", "id" -> "forms_button_save"),
        ListMap("data-page-action" -> "cancel", "id" -> "forms_button_cancel")
    )),format.raw/*17.6*/("""
""")))};def /*20.2*/header/*20.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*20.12*/("""
    """),format.raw/*21.5*/("""<span class="fx-entity-header-icon">
        <i class="fuxicons fuxicons-catalogue"></i>
    </span>
    <div class="fx-entity-header-title">
        <div style="display: table">
            <span style="display: table-cell">"""),_display_(/*26.48*/headerComponents("name")/*26.72*/.render()),format.raw/*26.81*/("""</span>
            <span style="display: table-cell">&nbsp;"""),_display_(/*27.54*/headerComponents("state")/*27.79*/.render()),format.raw/*27.88*/("""</span>
        </div>
        <p class="fx-entity-header-details">
            """),_display_(/*30.14*/headerComponents("description")/*30.45*/.render()),format.raw/*30.54*/("""
        """),format.raw/*31.9*/("""</p>
    </div>
""")))};def /*35.2*/additionalHeader/*35.18*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*35.22*/("""
    """),_display_(/*36.6*/if(!version.isValid)/*36.26*/ {_display_(Seq[Any](format.raw/*36.28*/("""
        """),format.raw/*37.9*/("""<div class="alert alert-warning">
            <div class="fx-alert-icon"><i class="fuxicons fuxicons-warning"></i>
            </div>

            <div id="description" class="fx-alert-desc">
                """),_display_(/*42.18*/i18n("na.portal.operationscatalog.notification.this.version.is.invalid")),format.raw/*42.90*/("""
            """),format.raw/*43.13*/("""</div>
        </div>
    """)))}),format.raw/*45.6*/("""
""")))};def /*48.2*/tabs/*48.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*48.10*/("""
    """),_display_(/*49.6*/na/*49.8*/.naportalbase.views.html.skeletons.tabs.contentTabSkel.render/*49.69*/ {_display_(Seq[Any](format.raw/*49.71*/("""
        """),_display_(/*50.10*/na/*50.12*/.naportalbase.views.html.components.tabs.contentTabComp.render(
            isActive = true,
            tabLabel = i18n("na.portal.operationscatalog.tab.characteristics"),
            attributes = null
        )),format.raw/*54.10*/("""
    """)))}),format.raw/*55.6*/("""
""")))};
Seq[Any](format.raw/*10.95*/("""

"""),format.raw/*18.2*/("""

"""),format.raw/*33.2*/("""

"""),format.raw/*46.2*/("""

"""),format.raw/*56.2*/("""

"""),_display_(/*58.2*/mainSkel(version.getName)/*58.27*/ {_display_(Seq[Any](format.raw/*58.29*/("""
    """),format.raw/*59.5*/("""<div class="page__container">
        """),_display_(/*60.10*/na/*60.12*/.operationscatalog.views.html.common.lateralNav.render(OperationsCatalogConstants.VERSION_CONTEXT)),format.raw/*60.110*/("""
        """),format.raw/*61.9*/("""<div id="version-form" class="page__content page__content--details-view"
            data-version-name=""""),_display_(/*62.33*/version/*62.40*/.getName),format.raw/*62.48*/(""""
            data-version-id=""""),_display_(/*63.31*/version/*63.38*/.getId),format.raw/*63.44*/(""""
            data-na-portal-operations-catalog-version-edit-page
            data-na-portal-operations-catalog-details-store-version>

            <script data-initial-store-content type="application/json">"""),_display_(/*67.73*/Json/*67.77*/.toJson(version)),format.raw/*67.93*/("""</script>
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                """),_display_(/*70.18*/header),format.raw/*70.24*/("""
                """),format.raw/*71.17*/("""</div>
                <div class="fx-entity-header-actions">
                """),_display_(/*73.18*/headerActions),format.raw/*73.31*/("""
                """),format.raw/*74.17*/("""</div>
            </div>
            <div class="container--entity-content">
                <x-i18n-attr-container>
                    <x-tab-container>
                        <x-tab data-i18n-label="na.portal.operationscatalog.tab.characteristics">
                            <x-shadow-scroll flex>
                                <div class="tab-pane active">
                                    <div class="operations-catalog__details-page--form-content">
                                        <div class="form-horizontal">
                                        """),_display_(/*84.42*/characteristicsTabContent/*84.67*/.render(version, elements)),format.raw/*84.93*/("""
                                        """),format.raw/*85.41*/("""</div>
                                    </div>
                                </div>
                            </x-shadow-scroll>
                        </x-tab>
                    </x-tab-container>
                </x-i18n-attr-container>
            </div>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(version:Version,elements:List[WebComponent],headerComponents:Map[String, WebComponent]): play.twirl.api.HtmlFormat.Appendable = apply(version,elements,headerComponents)

  def f:((Version,List[WebComponent],Map[String, WebComponent]) => play.twirl.api.HtmlFormat.Appendable) = (version,elements,headerComponents) => apply(version,elements,headerComponents)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:59 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/version/edit.scala.html
                  HASH: a382ebc19a41969f04b91e1ef5858fc8a95167e4
                  MATRIX: 680->1|726->41|787->96|876->179|928->225|999->290|1064->349|1093->372|1485->416|1657->512|1679->525|1760->529|1792->535|1818->552|2075->789|2100->794|2114->800|2195->804|2227->809|2480->1035|2513->1059|2543->1068|2631->1129|2665->1154|2695->1163|2803->1244|2843->1275|2873->1284|2909->1293|2949->1313|2974->1329|3055->1333|3087->1339|3116->1359|3156->1361|3192->1370|3428->1579|3521->1651|3562->1664|3619->1691|3644->1696|3656->1700|3737->1704|3769->1710|3779->1712|3849->1773|3889->1775|3926->1785|3937->1787|4170->1999|4206->2005|4248->509|4277->791|4306->1310|4335->1693|4364->2007|4393->2010|4427->2035|4467->2037|4499->2042|4565->2081|4576->2083|4696->2181|4732->2190|4864->2295|4880->2302|4909->2310|4968->2342|4984->2349|5011->2355|5246->2563|5259->2567|5296->2583|5445->2705|5472->2711|5517->2728|5623->2807|5657->2820|5702->2837|6304->3412|6338->3437|6385->3463|6454->3504
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|36->10|40->12|40->12|42->12|43->13|43->13|47->17|48->20|48->20|50->20|51->21|56->26|56->26|56->26|57->27|57->27|57->27|60->30|60->30|60->30|61->31|63->35|63->35|65->35|66->36|66->36|66->36|67->37|72->42|72->42|73->43|75->45|76->48|76->48|78->48|79->49|79->49|79->49|79->49|80->50|80->50|84->54|85->55|87->10|89->18|91->33|93->46|95->56|97->58|97->58|97->58|98->59|99->60|99->60|99->60|100->61|101->62|101->62|101->62|102->63|102->63|102->63|106->67|106->67|106->67|109->70|109->70|110->71|112->73|112->73|113->74|123->84|123->84|123->84|124->85
                  -- GENERATED --
              */
          