
package na.operationscatalog.views.html.operations

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.operationscatalog.models.operations.Operation
/*3.2*/import na.catalog.basemodule.ui.{Label, WebComponent}
/*4.2*/import na.operationscatalog.views.html.common.saveEntityButtons
/*5.2*/import na.operationscatalog.views.html.skeletons.mainSkel
/*6.2*/import play.libs.Json
/*7.2*/import pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action
/*8.2*/import scala.collection.immutable.ListMap

object createOrEdit extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template9[String,String,String,List[WebComponent],Map[String, WebComponent],String,Action,Long,Operation,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(
        iconClass: String,
        operationContext: String,
        pageDirective: String,
        elements: List[WebComponent],
        headerComponents: Map[String, WebComponent],
        permission: String,
        action: Action,
        typeId: Long,
        operation: Operation
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*20.2*/("""


"""),_display_(/*23.2*/mainSkel()/*23.12*/ {_display_(Seq[Any](format.raw/*23.14*/("""
"""),format.raw/*24.1*/("""<x-i18n-attr-container>
    <div class="page__container">
        """),_display_(/*26.10*/na/*26.12*/.operationscatalog.views.html.common.lateralNav.render(operationContext)),format.raw/*26.84*/("""
        """),format.raw/*27.9*/("""<div
            class="page__content page__content--details-edit"
            id="operation-form"
            """),_display_(/*30.14*/pageDirective),format.raw/*30.27*/("""
            """),format.raw/*31.13*/("""data-na-portal-operations-catalog-details-store-operation
            data-na-portal-operations-catalog-details-entities-store-operation
            data-operation-type=""""),_display_(/*33.35*/typeId),format.raw/*33.41*/(""""
            data-operation-action=""""),_display_(/*34.37*/action),format.raw/*34.43*/(""""
            data-na-portal-operations-catalog-operation-instance
        >
            <!-- Operation Json -->
            <script data-initial-store-content type="application/json">"""),_display_(/*38.73*/Json/*38.77*/.toJson(operation)),format.raw/*38.95*/("""</script>
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class=""""),_display_(/*42.36*/iconClass),format.raw/*42.45*/(""""></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <x-input-container>
                            <div class="form-inline">
                                """),_display_(/*47.34*/headerComponents("name")/*47.58*/.render()),format.raw/*47.67*/("""
                                """),format.raw/*48.33*/("""<span style="padding: 0 6px">"""),_display_(/*48.63*/headerComponents("entityName")/*48.93*/.render()),format.raw/*48.102*/("""</span>
                                <span> - """),_display_(/*49.43*/headerComponents("state")/*49.68*/.render()),format.raw/*49.77*/("""</span>
                            </div>
                        </x-input-container>
                        <p class="fx-entity-header-details">"""),_display_(/*52.62*/headerComponents("description")/*52.93*/.render()),format.raw/*52.102*/("""</p>
                    </div>
                </div>
                """),_display_(/*55.18*/if(action == Action.EDIT && !operation.isValid)/*55.65*/ {_display_(Seq[Any](format.raw/*55.67*/("""
                    """),format.raw/*56.21*/("""<div class="alert alert-warning">
                        <div class="fx-alert-icon">
                            <i class="fuxicons fuxicons-warning"></i>
                        </div>
                        <div id="description" class="fx-alert-desc">
                            <x-i18n key="na.portal.operationscatalog.notification.this.operation.is.invalid"></x-i18n>
                        </div>
                    </div>
                """)))}),format.raw/*64.18*/("""
                """),format.raw/*65.17*/("""<div class="fx-entity-header-actions">
                """),_display_(/*66.18*/saveEntityButtons/*66.35*/.render(
                    Array(permission),
                    ListMap("data-page-action" -> "submitEntity", "id" -> "forms_button_save"),
                    ListMap("data-page-action" -> "cancel", "id" -> "forms_button_cancel")
                )),format.raw/*70.18*/("""
                """),format.raw/*71.17*/("""</div>
            </div>
            <div class="container--entity-content">
                <x-tab-container>
                    <x-tab data-i18n-label="na.portal.operationscatalog.tab.characteristics">
                        <x-shadow-scroll flex>
                            <div class="tab-pane fx-form-vertical">
                                <x-collapsible class="fx-section" data-i18n-header="na.portal.operationscatalog.tab.section.general.characteristics">
                                    <div class="fx-form-content fx-expandable-form-blocks">
                                        <div class="form-horizontal fx-toggle-form-vertical">
                                            <div class="fx-form-block-controls">
                                            """),_display_(/*82.46*/for(element <- elements) yield /*82.70*/ {_display_(Seq[Any](format.raw/*82.72*/("""
                                                """),format.raw/*83.49*/("""<div class="form-group">
                                                    """),_display_(/*84.54*/if(element.getI18n != null)/*84.81*/ {_display_(Seq[Any](format.raw/*84.83*/("""
                                                        """),_display_(/*85.58*/{
                                                            val jMap = new java.util.HashMap[String,String]()
                                                            jMap.put("class", "col-sm-1 control-label")
                                                            val label = new Label(element.getId, element.getI18n)
                                                            label.setDataAttributes(jMap)
                                                            label.render()
                                                        }),format.raw/*91.58*/("""
                                                    """)))}),format.raw/*92.54*/("""
                                                """),format.raw/*93.49*/("""<div class="col-sm-9 col-sm-9 col-md-9 col-lg-9">
                                                    <p class="form-control-static">"""),_display_(/*94.85*/element/*94.92*/.render()),format.raw/*94.101*/("""</p>
                                                </div>
                                                </div>
                                            """)))}),format.raw/*97.46*/("""
                                            """),format.raw/*98.45*/("""</div>
                                        </div>
                                    </div>
                                </x-collapsible>
                                <x-collapsible class="fx-section" data-i18n-header="na.portal.operationscatalog.tab.section.input.attributes">
                                    <div class="fx-form-content fx-expandable-form-blocks">
                                        <div class="fx-form-block-controls">
                                            <div class="pull-right fx-tab-overflow-add-action">
                                                <button id="new-input-attribute-button" class="btn btn-primary btn-sx" disabled type="button" data-na-portal-operations-catalog-open-add-input-attributes-modal>
                                                    <i class="glyphicon glyphicon-plus"></i>
                                                    <x-i18n key="na.portal.operationscatalog.button.createAttribute"></x-i18n>
                                                </button>
                                            </div>
                                            <table id="operation-input-attributes-table"
                                            class="table table-striped table-hover no-footer"
                                            data-na-portal-table-datatable
                                            data-na-portal-operations-catalog-editable-input-attributes-datatable
                                            data-config-url=""""),_display_(/*115.63*/na/*115.65*/.operationscatalog.controllers.routes.OperationController.dataTableEditableInputAttributes()),format.raw/*115.157*/("""">
                                            </table>
                                        </div>
                                    </div>
                                </x-collapsible>
                                <x-collapsible class="fx-section" data-i18n-header="na.portal.operationscatalog.tab.section.execution.attributes">
                                    <div class="fx-form-content fx-expandable-form-blocks">
                                        <div class="fx-form-block-controls">
                                            <table id="operation-execution-attributes-table"
                                            class="table table-striped table-hover no-footer"
                                            data-na-portal-table-datatable
                                            data-na-portal-operations-catalog-execution-attributes-datatable
                                            data-config-url=""""),_display_(/*127.63*/na/*127.65*/.operationscatalog.controllers.routes.OperationController.dataTableViewExecutionAttributes()),format.raw/*127.157*/(""""
                                            ></table>
                                        </div>
                                    </div>
                                </x-collapsible>
                                <x-collapsible class="fx-section" data-i18n-header="na.portal.operationscatalog.tab.section.output.attributes">
                                    <div class="fx-form-content fx-expandable-form-blocks">
                                        <div class="fx-form-block-controls">
                                            <table id="operation-output-attributes-table"
                                            class="table table-striped table-hover no-footer"
                                            data-na-portal-table-datatable
                                            data-na-portal-operations-catalog-editable-output-attributes-datatable
                                            data-config-url=""""),_display_(/*139.63*/na/*139.65*/.operationscatalog.controllers.routes.OperationController.dataTableCreateOrEditOutputAttributes()),format.raw/*139.162*/(""""
                                            >
                                                <thead>
                                                    <tr>
                                                        <th><x-i18n key="na.portal.operationscatalog.datatables.column.label.attributes_entities"></x-i18n></th>
                                                        <th><x-i18n key="na.portal.operationscatalog.datatables.column.label.actions"></x-i18n></th>
                                                    </tr>
                                                    <tr class="edit-row">
                                                        <th>
                                                            <x-opercat-output-attr-select full-width name="entities"
                                                                class="select-box--entity-attributes" id="outputAttributeEntities"
                                                                data-i18n-placeholder="na.portal.operationscatalog.selectbox.placeholder"
                                                                data-output-attribute-select></x-opercat-output-attr-select>
                                                        </th>
                                                        <th>
                                                            <span class="fx-table-actions">
                                                                <a id="new-output-attribute-button" class="btn btn-link btn-link-in-table" data-action="create"><i class="glyphicon glyphicon-ok"></i></a>
                                                            </span>
                                                        </th>
                                                    </tr>
                                                </thead>
                                            </table>
                                        </div>
                                    </div>
                                </x-collapsible>
                            </div>
                        </x-shadow-scroll>
                    </x-tab>
                </x-tab-container>
            </div>
        </div>
    </div>
</x-i18n-attr-container>
""")))}))
      }
    }
  }

  def render(iconClass:String,operationContext:String,pageDirective:String,elements:List[WebComponent],headerComponents:Map[String, WebComponent],permission:String,action:Action,typeId:Long,operation:Operation): play.twirl.api.HtmlFormat.Appendable = apply(iconClass,operationContext,pageDirective,elements,headerComponents,permission,action,typeId,operation)

  def f:((String,String,String,List[WebComponent],Map[String, WebComponent],String,Action,Long,Operation) => play.twirl.api.HtmlFormat.Appendable) = (iconClass,operationContext,pageDirective,elements,headerComponents,permission,action,typeId,operation) => apply(iconClass,operationContext,pageDirective,elements,headerComponents,permission,action,typeId,operation)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:59 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/operations/createOrEdit.scala.html
                  HASH: 47d72903a4c4edb268c4326291b64833676ed420
                  MATRIX: 683->1|729->41|792->98|853->153|924->218|989->277|1018->300|1098->374|1540->418|1924->707|1954->711|1973->721|2013->723|2041->724|2135->791|2146->793|2239->865|2275->874|2414->986|2448->999|2489->1012|2687->1183|2714->1189|2779->1227|2806->1233|3018->1418|3031->1422|3070->1440|3294->1637|3324->1646|3574->1869|3607->1893|3637->1902|3698->1935|3755->1965|3794->1995|3825->2004|3902->2054|3936->2079|3966->2088|4142->2237|4182->2268|4213->2277|4312->2349|4368->2396|4408->2398|4457->2419|4938->2869|4983->2886|5066->2942|5092->2959|5365->3211|5410->3228|6220->4011|6260->4035|6300->4037|6377->4086|6482->4164|6518->4191|6558->4193|6643->4251|7216->4803|7301->4857|7378->4906|7539->5040|7555->5047|7586->5056|7777->5216|7850->5261|9404->6787|9416->6789|9531->6881|10503->7825|10515->7827|10630->7919|11602->8863|11614->8865|11734->8962
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|36->10|51->20|54->23|54->23|54->23|55->24|57->26|57->26|57->26|58->27|61->30|61->30|62->31|64->33|64->33|65->34|65->34|69->38|69->38|69->38|73->42|73->42|78->47|78->47|78->47|79->48|79->48|79->48|79->48|80->49|80->49|80->49|83->52|83->52|83->52|86->55|86->55|86->55|87->56|95->64|96->65|97->66|97->66|101->70|102->71|113->82|113->82|113->82|114->83|115->84|115->84|115->84|116->85|122->91|123->92|124->93|125->94|125->94|125->94|128->97|129->98|146->115|146->115|146->115|158->127|158->127|158->127|170->139|170->139|170->139
                  -- GENERATED --
              */
          