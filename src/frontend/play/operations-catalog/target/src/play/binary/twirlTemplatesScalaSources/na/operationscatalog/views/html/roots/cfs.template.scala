
package na.operationscatalog.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.catalog.basemodule.ui.WebComponent
/*2.2*/import na.operationscatalog.settings.OperationsCatalogConstants

object cfs extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[java.util.List[WebComponent],WebComponent,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*4.2*/(elements: java.util.List[WebComponent], versionComponent: WebComponent):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*4.74*/("""

"""),_display_(/*6.2*/na/*6.4*/.operationscatalog.views.html.roots.operation.render(
    headerTitleI18n = "na.portal.operationscatalog.nav.cfs",
    iconClass = "fuxicons fuxicons-client-services",
    searchContext = OperationsCatalogConstants.CFS_CONTEXT,
    tableId = "cfs-search-table",
    searchPageDirective = "data-na-portal-operations-catalog-cfs-search-page",
    resultsTableDirective = "data-na-portal-operations-catalog-cfs-datatable",
    dataTableConfigUrl = na.operationscatalog.controllers.routes.CfsController.cfsDataTableSearch(),
    elements = elements,
    versionComponent = versionComponent
)))
      }
    }
  }

  def render(elements:java.util.List[WebComponent],versionComponent:WebComponent): play.twirl.api.HtmlFormat.Appendable = apply(elements,versionComponent)

  def f:((java.util.List[WebComponent],WebComponent) => play.twirl.api.HtmlFormat.Appendable) = (elements,versionComponent) => apply(elements,versionComponent)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:59 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/operations-catalog/target/TwirlSource/na/operationscatalog/views/roots/cfs.scala.html
                  HASH: 1de0adbd3b4760e9c21dd38846b13eae659d1f19
                  MATRIX: 678->1|730->47|1131->113|1298->185|1326->188|1335->190
                  LINES: 24->1|25->2|30->4|35->4|37->6|37->6
                  -- GENERATED --
              */
          