// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/router/conf/healthcheck.routes
// @DATE:Wed Jul 02 14:15:24 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:5
package na.router.controllers {

  // @LINE:5
  class ReverseRouterController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:5
    def healthCheck(): Call = {
      
      Call("GET", _prefix)
    }
  
  }


}
