// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/conf/mpt.routes
// @DATE:Wed Jul 02 14:15:38 WEST 2025

package mpt

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:2
  Application_6: na.mpt.controllers.Application,
  // @LINE:14
  Assets_0: na.mpt.controllers.Assets,
  // @LINE:21
  CreateOperation_1: na.mpt.controllers.CreateOperation,
  // @LINE:33
  Bulks_3: na.mpt.controllers.Bulks,
  // @LINE:41
  BulkDetails_8: na.mpt.controllers.BulkDetails,
  // @LINE:48
  BulkResults_7: na.mpt.controllers.BulkResults,
  // @LINE:49
  ResultDetails_5: na.mpt.controllers.ResultDetails,
  // @LINE:53
  Filters_4: na.mpt.controllers.Filters,
  // @LINE:63
  TableConfigs_2: na.mpt.controllers.TableConfigs,
  // @LINE:69
  naportalbase_Routes_0: naportalbase.Routes,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:2
    Application_6: na.mpt.controllers.Application,
    // @LINE:14
    Assets_0: na.mpt.controllers.Assets,
    // @LINE:21
    CreateOperation_1: na.mpt.controllers.CreateOperation,
    // @LINE:33
    Bulks_3: na.mpt.controllers.Bulks,
    // @LINE:41
    BulkDetails_8: na.mpt.controllers.BulkDetails,
    // @LINE:48
    BulkResults_7: na.mpt.controllers.BulkResults,
    // @LINE:49
    ResultDetails_5: na.mpt.controllers.ResultDetails,
    // @LINE:53
    Filters_4: na.mpt.controllers.Filters,
    // @LINE:63
    TableConfigs_2: na.mpt.controllers.TableConfigs,
    // @LINE:69
    naportalbase_Routes_0: naportalbase.Routes
  ) = this(errorHandler, Application_6, Assets_0, CreateOperation_1, Bulks_3, BulkDetails_8, BulkResults_7, ResultDetails_5, Filters_4, TableConfigs_2, naportalbase_Routes_0, "/")

  def withPrefix(prefix: String): Routes = {
    mpt.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, Application_6, Assets_0, CreateOperation_1, Bulks_3, BulkDetails_8, BulkResults_7, ResultDetails_5, Filters_4, TableConfigs_2, naportalbase_Routes_0, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/home""", """na.mpt.controllers.Application.index()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/resume""", """na.mpt.controllers.Application.resume()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/assets/javascripts/routes""", """na.mpt.controllers.Application.javascriptRoutes()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/imports/jsscripts""", """na.mpt.controllers.Application.getMptModuleAngularScripts()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/assets/""" + "$" + """file<.+>""", """na.mpt.controllers.Assets.versioned(path:String = "/public", file:Asset)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/settings""", """na.mpt.controllers.Application.getSettings()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/create""", """na.mpt.controllers.CreateOperation.loadCreateOperation()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/create/files""", """na.mpt.controllers.CreateOperation.loadFilesTab(context:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/create/characteristics""", """na.mpt.controllers.CreateOperation.loadCharacteristicsTab()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/create/uploadfiles""", """na.mpt.controllers.CreateOperation.createNormalOperation()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/create/validate""", """na.mpt.controllers.CreateOperation.validateSchedules()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/create/schedule""", """na.mpt.controllers.CreateOperation.loadScheduleTab()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/create/families/""" + "$" + """familyId<[^/]+>""", """na.mpt.controllers.CreateOperation.loadOperations(familyId:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/create/modals/createschedule""", """na.mpt.controllers.CreateOperation.getCreateScheduleModal()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/create/operation/""" + "$" + """id<[^/]+>/headers""", """na.mpt.controllers.CreateOperation.loadOperationHeaders(id:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/operation/edit/modals/editschedule""", """na.mpt.controllers.CreateOperation.getEditScheduleModal()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/tabledata""", """na.mpt.controllers.Bulks.BulksDatatableData()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/launch/""" + "$" + """id<[^/]+>""", """na.mpt.controllers.Bulks.launchBulk(id:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/stop/""" + "$" + """id<[^/]+>""", """na.mpt.controllers.Bulks.stopBulk(id:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/suspend/""" + "$" + """id<[^/]+>""", """na.mpt.controllers.Bulks.suspendBulk(id:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/edit/""" + "$" + """bulkId<[^/]+>""", """na.mpt.controllers.Bulks.bulkEdit(bulkId:Long)"""),
    ("""PUT""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/update""", """na.mpt.controllers.Bulks.updateBulk()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/details/""" + "$" + """bulkId<[^/]+>/""" + "$" + """tab<[^/]+>""", """na.mpt.controllers.BulkDetails.getBulkDetails(bulkId:Long, tab:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/details/results/tab/""" + "$" + """operationId<[^/]+>""", """na.mpt.controllers.BulkDetails.getBulkDetailsResultsTab(operationId:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/details/bulk""", """na.mpt.controllers.BulkDetails.getBulkDetailsCharacteristicsTab()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/export/results/""" + "$" + """bulkId<[^/]+>""", """na.mpt.controllers.BulkDetails.getBulkResultFile(bulkId:Long)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/bulks/export/original/""" + "$" + """bulkId<[^/]+>""", """na.mpt.controllers.BulkDetails.getBulkOriginalFile(bulkId:Long)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/results/""" + "$" + """bulkId<[^/]+>/tabledata""", """na.mpt.controllers.BulkResults.resultsTableData(bulkId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/results/details/""" + "$" + """operationId<[^/]+>""", """na.mpt.controllers.ResultDetails.getResultDetails(operationId:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/filter/save""", """na.mpt.controllers.Filters.getSaveFilterModal()"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/filter/persist""", """na.mpt.controllers.Filters.saveFilter()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/filter/edit/""" + "$" + """filterName<[^/]+>""", """na.mpt.controllers.Filters.getEditFilterModal(filterName:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/filter/update/""" + "$" + """filterName<[^/]+>""", """na.mpt.controllers.Filters.editFilter(filterName:String)"""),
    ("""POST""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/filter/delete/""" + "$" + """filterName<[^/]+>""", """na.mpt.controllers.Filters.deleteFilter(filterName:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/filter/delete/""" + "$" + """filterName<[^/]+>""", """na.mpt.controllers.Filters.getDeleteFilterModal(filterName:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/filter""", """na.mpt.controllers.Filters.getFilterNamesForSelect()"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/filter/""" + "$" + """filterName<[^/]+>""", """na.mpt.controllers.Filters.getFieldsForFiltername(filterName:String)"""),
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """mpt/table-configs/bulks-search""", """na.mpt.controllers.TableConfigs.bulkSearch()"""),
    prefixed_naportalbase_Routes_0_38.router.documentation,
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:2
  private[this] lazy val na_mpt_controllers_Application_index0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/home")))
  )
  private[this] lazy val na_mpt_controllers_Application_index0_invoker = createInvoker(
    Application_6.index(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Application",
      "index",
      Nil,
      "GET",
      this.prefix + """mpt/home""",
      """ Home page""",
      Seq()
    )
  )

  // @LINE:5
  private[this] lazy val na_mpt_controllers_Application_resume1_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/resume")))
  )
  private[this] lazy val na_mpt_controllers_Application_resume1_invoker = createInvoker(
    Application_6.resume(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Application",
      "resume",
      Nil,
      "GET",
      this.prefix + """mpt/resume""",
      """Resume""",
      Seq()
    )
  )

  // @LINE:8
  private[this] lazy val na_mpt_controllers_Application_javascriptRoutes2_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/assets/javascripts/routes")))
  )
  private[this] lazy val na_mpt_controllers_Application_javascriptRoutes2_invoker = createInvoker(
    Application_6.javascriptRoutes(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Application",
      "javascriptRoutes",
      Nil,
      "GET",
      this.prefix + """mpt/assets/javascripts/routes""",
      """Javascript Routing""",
      Seq()
    )
  )

  // @LINE:11
  private[this] lazy val na_mpt_controllers_Application_getMptModuleAngularScripts3_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/imports/jsscripts")))
  )
  private[this] lazy val na_mpt_controllers_Application_getMptModuleAngularScripts3_invoker = createInvoker(
    Application_6.getMptModuleAngularScripts(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Application",
      "getMptModuleAngularScripts",
      Nil,
      "GET",
      this.prefix + """mpt/imports/jsscripts""",
      """Angular Scripts""",
      Seq()
    )
  )

  // @LINE:14
  private[this] lazy val na_mpt_controllers_Assets_versioned4_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/assets/"), DynamicPart("file", """.+""",false)))
  )
  private[this] lazy val na_mpt_controllers_Assets_versioned4_invoker = createInvoker(
    Assets_0.versioned(fakeValue[String], fakeValue[Asset]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Assets",
      "versioned",
      Seq(classOf[String], classOf[Asset]),
      "GET",
      this.prefix + """mpt/assets/""" + "$" + """file<.+>""",
      """ Map static resources from the /public folder to the /assets URL path""",
      Seq()
    )
  )

  // @LINE:17
  private[this] lazy val na_mpt_controllers_Application_getSettings5_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/settings")))
  )
  private[this] lazy val na_mpt_controllers_Application_getSettings5_invoker = createInvoker(
    Application_6.getSettings(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Application",
      "getSettings",
      Nil,
      "GET",
      this.prefix + """mpt/settings""",
      """Settings""",
      Seq()
    )
  )

  // @LINE:21
  private[this] lazy val na_mpt_controllers_CreateOperation_loadCreateOperation6_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/create")))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_loadCreateOperation6_invoker = createInvoker(
    CreateOperation_1.loadCreateOperation(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "loadCreateOperation",
      Nil,
      "GET",
      this.prefix + """mpt/operation/create""",
      """Create Operation""",
      Seq()
    )
  )

  // @LINE:22
  private[this] lazy val na_mpt_controllers_CreateOperation_loadFilesTab7_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/create/files")))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_loadFilesTab7_invoker = createInvoker(
    CreateOperation_1.loadFilesTab(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "loadFilesTab",
      Seq(classOf[String]),
      "GET",
      this.prefix + """mpt/operation/create/files""",
      """""",
      Seq()
    )
  )

  // @LINE:23
  private[this] lazy val na_mpt_controllers_CreateOperation_loadCharacteristicsTab8_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/create/characteristics")))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_loadCharacteristicsTab8_invoker = createInvoker(
    CreateOperation_1.loadCharacteristicsTab(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "loadCharacteristicsTab",
      Nil,
      "GET",
      this.prefix + """mpt/operation/create/characteristics""",
      """""",
      Seq()
    )
  )

  // @LINE:24
  private[this] lazy val na_mpt_controllers_CreateOperation_createNormalOperation9_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/create/uploadfiles")))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_createNormalOperation9_invoker = createInvoker(
    CreateOperation_1.createNormalOperation(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "createNormalOperation",
      Nil,
      "POST",
      this.prefix + """mpt/operation/create/uploadfiles""",
      """""",
      Seq()
    )
  )

  // @LINE:25
  private[this] lazy val na_mpt_controllers_CreateOperation_validateSchedules10_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/create/validate")))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_validateSchedules10_invoker = createInvoker(
    CreateOperation_1.validateSchedules(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "validateSchedules",
      Nil,
      "POST",
      this.prefix + """mpt/operation/create/validate""",
      """""",
      Seq()
    )
  )

  // @LINE:26
  private[this] lazy val na_mpt_controllers_CreateOperation_loadScheduleTab11_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/create/schedule")))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_loadScheduleTab11_invoker = createInvoker(
    CreateOperation_1.loadScheduleTab(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "loadScheduleTab",
      Nil,
      "GET",
      this.prefix + """mpt/operation/create/schedule""",
      """""",
      Seq()
    )
  )

  // @LINE:27
  private[this] lazy val na_mpt_controllers_CreateOperation_loadOperations12_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/create/families/"), DynamicPart("familyId", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_loadOperations12_invoker = createInvoker(
    CreateOperation_1.loadOperations(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "loadOperations",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """mpt/operation/create/families/""" + "$" + """familyId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:28
  private[this] lazy val na_mpt_controllers_CreateOperation_getCreateScheduleModal13_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/create/modals/createschedule")))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_getCreateScheduleModal13_invoker = createInvoker(
    CreateOperation_1.getCreateScheduleModal(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "getCreateScheduleModal",
      Nil,
      "GET",
      this.prefix + """mpt/operation/create/modals/createschedule""",
      """""",
      Seq()
    )
  )

  // @LINE:29
  private[this] lazy val na_mpt_controllers_CreateOperation_loadOperationHeaders14_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/create/operation/"), DynamicPart("id", """[^/]+""",true), StaticPart("/headers")))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_loadOperationHeaders14_invoker = createInvoker(
    CreateOperation_1.loadOperationHeaders(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "loadOperationHeaders",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """mpt/operation/create/operation/""" + "$" + """id<[^/]+>/headers""",
      """""",
      Seq()
    )
  )

  // @LINE:30
  private[this] lazy val na_mpt_controllers_CreateOperation_getEditScheduleModal15_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/operation/edit/modals/editschedule")))
  )
  private[this] lazy val na_mpt_controllers_CreateOperation_getEditScheduleModal15_invoker = createInvoker(
    CreateOperation_1.getEditScheduleModal(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.CreateOperation",
      "getEditScheduleModal",
      Nil,
      "GET",
      this.prefix + """mpt/operation/edit/modals/editschedule""",
      """""",
      Seq()
    )
  )

  // @LINE:33
  private[this] lazy val na_mpt_controllers_Bulks_BulksDatatableData16_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/tabledata")))
  )
  private[this] lazy val na_mpt_controllers_Bulks_BulksDatatableData16_invoker = createInvoker(
    Bulks_3.BulksDatatableData(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Bulks",
      "BulksDatatableData",
      Nil,
      "POST",
      this.prefix + """mpt/bulks/tabledata""",
      """Bulks""",
      Seq()
    )
  )

  // @LINE:34
  private[this] lazy val na_mpt_controllers_Bulks_launchBulk17_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/launch/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_Bulks_launchBulk17_invoker = createInvoker(
    Bulks_3.launchBulk(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Bulks",
      "launchBulk",
      Seq(classOf[String]),
      "GET",
      this.prefix + """mpt/bulks/launch/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:35
  private[this] lazy val na_mpt_controllers_Bulks_stopBulk18_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/stop/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_Bulks_stopBulk18_invoker = createInvoker(
    Bulks_3.stopBulk(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Bulks",
      "stopBulk",
      Seq(classOf[String]),
      "GET",
      this.prefix + """mpt/bulks/stop/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:36
  private[this] lazy val na_mpt_controllers_Bulks_suspendBulk19_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/suspend/"), DynamicPart("id", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_Bulks_suspendBulk19_invoker = createInvoker(
    Bulks_3.suspendBulk(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Bulks",
      "suspendBulk",
      Seq(classOf[String]),
      "GET",
      this.prefix + """mpt/bulks/suspend/""" + "$" + """id<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:37
  private[this] lazy val na_mpt_controllers_Bulks_bulkEdit20_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/edit/"), DynamicPart("bulkId", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_Bulks_bulkEdit20_invoker = createInvoker(
    Bulks_3.bulkEdit(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Bulks",
      "bulkEdit",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """mpt/bulks/edit/""" + "$" + """bulkId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:38
  private[this] lazy val na_mpt_controllers_Bulks_updateBulk21_route = Route("PUT",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/update")))
  )
  private[this] lazy val na_mpt_controllers_Bulks_updateBulk21_invoker = createInvoker(
    Bulks_3.updateBulk(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Bulks",
      "updateBulk",
      Nil,
      "PUT",
      this.prefix + """mpt/bulks/update""",
      """""",
      Seq()
    )
  )

  // @LINE:41
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkDetails22_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/details/"), DynamicPart("bulkId", """[^/]+""",true), StaticPart("/"), DynamicPart("tab", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkDetails22_invoker = createInvoker(
    BulkDetails_8.getBulkDetails(fakeValue[Long], fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.BulkDetails",
      "getBulkDetails",
      Seq(classOf[Long], classOf[String]),
      "GET",
      this.prefix + """mpt/bulks/details/""" + "$" + """bulkId<[^/]+>/""" + "$" + """tab<[^/]+>""",
      """BulksDetails""",
      Seq()
    )
  )

  // @LINE:42
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkDetailsResultsTab23_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/details/results/tab/"), DynamicPart("operationId", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkDetailsResultsTab23_invoker = createInvoker(
    BulkDetails_8.getBulkDetailsResultsTab(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.BulkDetails",
      "getBulkDetailsResultsTab",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """mpt/bulks/details/results/tab/""" + "$" + """operationId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:43
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkDetailsCharacteristicsTab24_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/details/bulk")))
  )
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkDetailsCharacteristicsTab24_invoker = createInvoker(
    BulkDetails_8.getBulkDetailsCharacteristicsTab(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.BulkDetails",
      "getBulkDetailsCharacteristicsTab",
      Nil,
      "GET",
      this.prefix + """mpt/bulks/details/bulk""",
      """""",
      Seq()
    )
  )

  // @LINE:44
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkResultFile25_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/export/results/"), DynamicPart("bulkId", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkResultFile25_invoker = createInvoker(
    BulkDetails_8.getBulkResultFile(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.BulkDetails",
      "getBulkResultFile",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """mpt/bulks/export/results/""" + "$" + """bulkId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:45
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkOriginalFile26_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/bulks/export/original/"), DynamicPart("bulkId", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_BulkDetails_getBulkOriginalFile26_invoker = createInvoker(
    BulkDetails_8.getBulkOriginalFile(fakeValue[Long]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.BulkDetails",
      "getBulkOriginalFile",
      Seq(classOf[Long]),
      "GET",
      this.prefix + """mpt/bulks/export/original/""" + "$" + """bulkId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:48
  private[this] lazy val na_mpt_controllers_BulkResults_resultsTableData27_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/results/"), DynamicPart("bulkId", """[^/]+""",true), StaticPart("/tabledata")))
  )
  private[this] lazy val na_mpt_controllers_BulkResults_resultsTableData27_invoker = createInvoker(
    BulkResults_7.resultsTableData(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.BulkResults",
      "resultsTableData",
      Seq(classOf[String]),
      "POST",
      this.prefix + """mpt/results/""" + "$" + """bulkId<[^/]+>/tabledata""",
      """Bulk Results""",
      Seq()
    )
  )

  // @LINE:49
  private[this] lazy val na_mpt_controllers_ResultDetails_getResultDetails28_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/results/details/"), DynamicPart("operationId", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_ResultDetails_getResultDetails28_invoker = createInvoker(
    ResultDetails_5.getResultDetails(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.ResultDetails",
      "getResultDetails",
      Seq(classOf[String]),
      "GET",
      this.prefix + """mpt/results/details/""" + "$" + """operationId<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:53
  private[this] lazy val na_mpt_controllers_Filters_getSaveFilterModal29_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/filter/save")))
  )
  private[this] lazy val na_mpt_controllers_Filters_getSaveFilterModal29_invoker = createInvoker(
    Filters_4.getSaveFilterModal(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Filters",
      "getSaveFilterModal",
      Nil,
      "GET",
      this.prefix + """mpt/filter/save""",
      """ Search Filters""",
      Seq()
    )
  )

  // @LINE:54
  private[this] lazy val na_mpt_controllers_Filters_saveFilter30_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/filter/persist")))
  )
  private[this] lazy val na_mpt_controllers_Filters_saveFilter30_invoker = createInvoker(
    Filters_4.saveFilter(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Filters",
      "saveFilter",
      Nil,
      "POST",
      this.prefix + """mpt/filter/persist""",
      """""",
      Seq()
    )
  )

  // @LINE:55
  private[this] lazy val na_mpt_controllers_Filters_getEditFilterModal31_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/filter/edit/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_Filters_getEditFilterModal31_invoker = createInvoker(
    Filters_4.getEditFilterModal(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Filters",
      "getEditFilterModal",
      Seq(classOf[String]),
      "GET",
      this.prefix + """mpt/filter/edit/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:56
  private[this] lazy val na_mpt_controllers_Filters_editFilter32_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/filter/update/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_Filters_editFilter32_invoker = createInvoker(
    Filters_4.editFilter(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Filters",
      "editFilter",
      Seq(classOf[String]),
      "POST",
      this.prefix + """mpt/filter/update/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:57
  private[this] lazy val na_mpt_controllers_Filters_deleteFilter33_route = Route("POST",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/filter/delete/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_Filters_deleteFilter33_invoker = createInvoker(
    Filters_4.deleteFilter(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Filters",
      "deleteFilter",
      Seq(classOf[String]),
      "POST",
      this.prefix + """mpt/filter/delete/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:58
  private[this] lazy val na_mpt_controllers_Filters_getDeleteFilterModal34_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/filter/delete/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_Filters_getDeleteFilterModal34_invoker = createInvoker(
    Filters_4.getDeleteFilterModal(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Filters",
      "getDeleteFilterModal",
      Seq(classOf[String]),
      "GET",
      this.prefix + """mpt/filter/delete/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:59
  private[this] lazy val na_mpt_controllers_Filters_getFilterNamesForSelect35_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/filter")))
  )
  private[this] lazy val na_mpt_controllers_Filters_getFilterNamesForSelect35_invoker = createInvoker(
    Filters_4.getFilterNamesForSelect(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Filters",
      "getFilterNamesForSelect",
      Nil,
      "GET",
      this.prefix + """mpt/filter""",
      """""",
      Seq()
    )
  )

  // @LINE:60
  private[this] lazy val na_mpt_controllers_Filters_getFieldsForFiltername36_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/filter/"), DynamicPart("filterName", """[^/]+""",true)))
  )
  private[this] lazy val na_mpt_controllers_Filters_getFieldsForFiltername36_invoker = createInvoker(
    Filters_4.getFieldsForFiltername(fakeValue[String]),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.Filters",
      "getFieldsForFiltername",
      Seq(classOf[String]),
      "GET",
      this.prefix + """mpt/filter/""" + "$" + """filterName<[^/]+>""",
      """""",
      Seq()
    )
  )

  // @LINE:63
  private[this] lazy val na_mpt_controllers_TableConfigs_bulkSearch37_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("mpt/table-configs/bulks-search")))
  )
  private[this] lazy val na_mpt_controllers_TableConfigs_bulkSearch37_invoker = createInvoker(
    TableConfigs_2.bulkSearch(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "mpt",
      "na.mpt.controllers.TableConfigs",
      "bulkSearch",
      Nil,
      "GET",
      this.prefix + """mpt/table-configs/bulks-search""",
      """ table configs""",
      Seq()
    )
  )

  // @LINE:69
  private[this] val prefixed_naportalbase_Routes_0_38 = Include(naportalbase_Routes_0.withPrefix(this.prefix + (if (this.prefix.endsWith("/")) "" else "/") + "mpt"))


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:2
    case na_mpt_controllers_Application_index0_route(params@_) =>
      call { 
        na_mpt_controllers_Application_index0_invoker.call(Application_6.index())
      }
  
    // @LINE:5
    case na_mpt_controllers_Application_resume1_route(params@_) =>
      call { 
        na_mpt_controllers_Application_resume1_invoker.call(Application_6.resume())
      }
  
    // @LINE:8
    case na_mpt_controllers_Application_javascriptRoutes2_route(params@_) =>
      call { 
        na_mpt_controllers_Application_javascriptRoutes2_invoker.call(Application_6.javascriptRoutes())
      }
  
    // @LINE:11
    case na_mpt_controllers_Application_getMptModuleAngularScripts3_route(params@_) =>
      call { 
        na_mpt_controllers_Application_getMptModuleAngularScripts3_invoker.call(Application_6.getMptModuleAngularScripts())
      }
  
    // @LINE:14
    case na_mpt_controllers_Assets_versioned4_route(params@_) =>
      call(Param[String]("path", Right("/public")), params.fromPath[Asset]("file", None)) { (path, file) =>
        na_mpt_controllers_Assets_versioned4_invoker.call(Assets_0.versioned(path, file))
      }
  
    // @LINE:17
    case na_mpt_controllers_Application_getSettings5_route(params@_) =>
      call { 
        na_mpt_controllers_Application_getSettings5_invoker.call(Application_6.getSettings())
      }
  
    // @LINE:21
    case na_mpt_controllers_CreateOperation_loadCreateOperation6_route(params@_) =>
      call { 
        na_mpt_controllers_CreateOperation_loadCreateOperation6_invoker.call(CreateOperation_1.loadCreateOperation())
      }
  
    // @LINE:22
    case na_mpt_controllers_CreateOperation_loadFilesTab7_route(params@_) =>
      call(params.fromQuery[String]("context", None)) { (context) =>
        na_mpt_controllers_CreateOperation_loadFilesTab7_invoker.call(CreateOperation_1.loadFilesTab(context))
      }
  
    // @LINE:23
    case na_mpt_controllers_CreateOperation_loadCharacteristicsTab8_route(params@_) =>
      call { 
        na_mpt_controllers_CreateOperation_loadCharacteristicsTab8_invoker.call(CreateOperation_1.loadCharacteristicsTab())
      }
  
    // @LINE:24
    case na_mpt_controllers_CreateOperation_createNormalOperation9_route(params@_) =>
      call { 
        na_mpt_controllers_CreateOperation_createNormalOperation9_invoker.call(CreateOperation_1.createNormalOperation())
      }
  
    // @LINE:25
    case na_mpt_controllers_CreateOperation_validateSchedules10_route(params@_) =>
      call { 
        na_mpt_controllers_CreateOperation_validateSchedules10_invoker.call(CreateOperation_1.validateSchedules())
      }
  
    // @LINE:26
    case na_mpt_controllers_CreateOperation_loadScheduleTab11_route(params@_) =>
      call { 
        na_mpt_controllers_CreateOperation_loadScheduleTab11_invoker.call(CreateOperation_1.loadScheduleTab())
      }
  
    // @LINE:27
    case na_mpt_controllers_CreateOperation_loadOperations12_route(params@_) =>
      call(params.fromPath[Long]("familyId", None)) { (familyId) =>
        na_mpt_controllers_CreateOperation_loadOperations12_invoker.call(CreateOperation_1.loadOperations(familyId))
      }
  
    // @LINE:28
    case na_mpt_controllers_CreateOperation_getCreateScheduleModal13_route(params@_) =>
      call { 
        na_mpt_controllers_CreateOperation_getCreateScheduleModal13_invoker.call(CreateOperation_1.getCreateScheduleModal())
      }
  
    // @LINE:29
    case na_mpt_controllers_CreateOperation_loadOperationHeaders14_route(params@_) =>
      call(params.fromPath[Long]("id", None)) { (id) =>
        na_mpt_controllers_CreateOperation_loadOperationHeaders14_invoker.call(CreateOperation_1.loadOperationHeaders(id))
      }
  
    // @LINE:30
    case na_mpt_controllers_CreateOperation_getEditScheduleModal15_route(params@_) =>
      call { 
        na_mpt_controllers_CreateOperation_getEditScheduleModal15_invoker.call(CreateOperation_1.getEditScheduleModal())
      }
  
    // @LINE:33
    case na_mpt_controllers_Bulks_BulksDatatableData16_route(params@_) =>
      call { 
        na_mpt_controllers_Bulks_BulksDatatableData16_invoker.call(Bulks_3.BulksDatatableData())
      }
  
    // @LINE:34
    case na_mpt_controllers_Bulks_launchBulk17_route(params@_) =>
      call(params.fromPath[String]("id", None)) { (id) =>
        na_mpt_controllers_Bulks_launchBulk17_invoker.call(Bulks_3.launchBulk(id))
      }
  
    // @LINE:35
    case na_mpt_controllers_Bulks_stopBulk18_route(params@_) =>
      call(params.fromPath[String]("id", None)) { (id) =>
        na_mpt_controllers_Bulks_stopBulk18_invoker.call(Bulks_3.stopBulk(id))
      }
  
    // @LINE:36
    case na_mpt_controllers_Bulks_suspendBulk19_route(params@_) =>
      call(params.fromPath[String]("id", None)) { (id) =>
        na_mpt_controllers_Bulks_suspendBulk19_invoker.call(Bulks_3.suspendBulk(id))
      }
  
    // @LINE:37
    case na_mpt_controllers_Bulks_bulkEdit20_route(params@_) =>
      call(params.fromPath[Long]("bulkId", None)) { (bulkId) =>
        na_mpt_controllers_Bulks_bulkEdit20_invoker.call(Bulks_3.bulkEdit(bulkId))
      }
  
    // @LINE:38
    case na_mpt_controllers_Bulks_updateBulk21_route(params@_) =>
      call { 
        na_mpt_controllers_Bulks_updateBulk21_invoker.call(Bulks_3.updateBulk())
      }
  
    // @LINE:41
    case na_mpt_controllers_BulkDetails_getBulkDetails22_route(params@_) =>
      call(params.fromPath[Long]("bulkId", None), params.fromPath[String]("tab", None)) { (bulkId, tab) =>
        na_mpt_controllers_BulkDetails_getBulkDetails22_invoker.call(BulkDetails_8.getBulkDetails(bulkId, tab))
      }
  
    // @LINE:42
    case na_mpt_controllers_BulkDetails_getBulkDetailsResultsTab23_route(params@_) =>
      call(params.fromPath[Long]("operationId", None)) { (operationId) =>
        na_mpt_controllers_BulkDetails_getBulkDetailsResultsTab23_invoker.call(BulkDetails_8.getBulkDetailsResultsTab(operationId))
      }
  
    // @LINE:43
    case na_mpt_controllers_BulkDetails_getBulkDetailsCharacteristicsTab24_route(params@_) =>
      call { 
        na_mpt_controllers_BulkDetails_getBulkDetailsCharacteristicsTab24_invoker.call(BulkDetails_8.getBulkDetailsCharacteristicsTab())
      }
  
    // @LINE:44
    case na_mpt_controllers_BulkDetails_getBulkResultFile25_route(params@_) =>
      call(params.fromPath[Long]("bulkId", None)) { (bulkId) =>
        na_mpt_controllers_BulkDetails_getBulkResultFile25_invoker.call(BulkDetails_8.getBulkResultFile(bulkId))
      }
  
    // @LINE:45
    case na_mpt_controllers_BulkDetails_getBulkOriginalFile26_route(params@_) =>
      call(params.fromPath[Long]("bulkId", None)) { (bulkId) =>
        na_mpt_controllers_BulkDetails_getBulkOriginalFile26_invoker.call(BulkDetails_8.getBulkOriginalFile(bulkId))
      }
  
    // @LINE:48
    case na_mpt_controllers_BulkResults_resultsTableData27_route(params@_) =>
      call(params.fromPath[String]("bulkId", None)) { (bulkId) =>
        na_mpt_controllers_BulkResults_resultsTableData27_invoker.call(BulkResults_7.resultsTableData(bulkId))
      }
  
    // @LINE:49
    case na_mpt_controllers_ResultDetails_getResultDetails28_route(params@_) =>
      call(params.fromPath[String]("operationId", None)) { (operationId) =>
        na_mpt_controllers_ResultDetails_getResultDetails28_invoker.call(ResultDetails_5.getResultDetails(operationId))
      }
  
    // @LINE:53
    case na_mpt_controllers_Filters_getSaveFilterModal29_route(params@_) =>
      call { 
        na_mpt_controllers_Filters_getSaveFilterModal29_invoker.call(Filters_4.getSaveFilterModal())
      }
  
    // @LINE:54
    case na_mpt_controllers_Filters_saveFilter30_route(params@_) =>
      call { 
        na_mpt_controllers_Filters_saveFilter30_invoker.call(Filters_4.saveFilter())
      }
  
    // @LINE:55
    case na_mpt_controllers_Filters_getEditFilterModal31_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_mpt_controllers_Filters_getEditFilterModal31_invoker.call(Filters_4.getEditFilterModal(filterName))
      }
  
    // @LINE:56
    case na_mpt_controllers_Filters_editFilter32_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_mpt_controllers_Filters_editFilter32_invoker.call(Filters_4.editFilter(filterName))
      }
  
    // @LINE:57
    case na_mpt_controllers_Filters_deleteFilter33_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_mpt_controllers_Filters_deleteFilter33_invoker.call(Filters_4.deleteFilter(filterName))
      }
  
    // @LINE:58
    case na_mpt_controllers_Filters_getDeleteFilterModal34_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_mpt_controllers_Filters_getDeleteFilterModal34_invoker.call(Filters_4.getDeleteFilterModal(filterName))
      }
  
    // @LINE:59
    case na_mpt_controllers_Filters_getFilterNamesForSelect35_route(params@_) =>
      call { 
        na_mpt_controllers_Filters_getFilterNamesForSelect35_invoker.call(Filters_4.getFilterNamesForSelect())
      }
  
    // @LINE:60
    case na_mpt_controllers_Filters_getFieldsForFiltername36_route(params@_) =>
      call(params.fromPath[String]("filterName", None)) { (filterName) =>
        na_mpt_controllers_Filters_getFieldsForFiltername36_invoker.call(Filters_4.getFieldsForFiltername(filterName))
      }
  
    // @LINE:63
    case na_mpt_controllers_TableConfigs_bulkSearch37_route(params@_) =>
      call { 
        na_mpt_controllers_TableConfigs_bulkSearch37_invoker.call(TableConfigs_2.bulkSearch())
      }
  
    // @LINE:69
    case prefixed_naportalbase_Routes_0_38(handler) => handler
  }
}
