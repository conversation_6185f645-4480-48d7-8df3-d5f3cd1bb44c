
package na.mpt.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.html.components.tabs.contentTabComp
/*2.2*/import na.naportalbase.views.html.skeletons.tabs.contentTabSkel
/*3.2*/import scala.collection.immutable
/*4.2*/import na.mpt.views.html.components.bulkedit.operationWizard
/*5.2*/import java.util.List
/*6.2*/import na.naportalbase.views.tags.i18n
/*7.2*/import na.mpt.views.html.skeletons.mainSkel
/*8.2*/import pt.ptinovacao.na.portal.webui.restful.mpt.commons.model.FamilyEntry
/*10.2*/import na.naportalbase.views.html.skeletons.pageHeaderSkel

object bulkEdit extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[List[FamilyEntry],String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(allFamilies : List[FamilyEntry], jsonEditData : String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*11.58*/("""


"""),_display_(/*14.2*/mainSkel()/*14.12*/ {_display_(Seq[Any](format.raw/*14.14*/("""
    """),format.raw/*15.5*/("""<div class="fx-main-content-wrapper mpt-wizard-page">
        <div class="fx-entity-header" data-na-portal-mpt-create-operation-breadcrumb>
            <div class="fx-entity-header-info">
                <span class="fx-entity-header-icon">
                    <i class="fuxicons fuxicons-mpt"></i>
                </span>
                <div class="fx-entity-header-title">
                    <h1>
                    """),_display_(/*23.22*/i18n("na.portal.mpt.editbulk.label.title")),format.raw/*23.64*/("""
                    """),format.raw/*24.21*/("""</h1>
                </div>
            </div>
        </div>
        <div class="fx-entity-info">
            <x-tab-container>
                <x-tab label=""""),_display_(/*30.32*/i18n("na.portal.mpt.editbulk.tab.label")),format.raw/*30.72*/("""">
                    <div class="tab-content fx-main-tab-content fx-full-height"
                    data-template-loader
                    data-na-portal-mpt-set-edit-bulk-context
                    data-na-portal-mpt-edit-bulk-breadcrumb
                    data-edit-data=""""),_display_(/*35.38*/jsonEditData),format.raw/*35.50*/("""">
                    """),_display_(/*36.22*/{
                        operationWizard.render(allFamilies)
                    }),format.raw/*38.22*/("""
                    """),format.raw/*39.21*/("""</div>
                </x-tab>
            </x-tab-container>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(allFamilies:List[FamilyEntry],jsonEditData:String): play.twirl.api.HtmlFormat.Appendable = apply(allFamilies,jsonEditData)

  def f:((List[FamilyEntry],String) => play.twirl.api.HtmlFormat.Appendable) = (allFamilies,jsonEditData) => apply(allFamilies,jsonEditData)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/roots/bulkEdit.scala.html
                  HASH: f95f9039274e6dd32c11a00e222d6db34a05d000
                  MATRIX: 664->1|736->67|807->132|848->167|916->229|945->252|991->292|1042->337|1125->414|1510->474|1662->530|1692->534|1711->544|1751->546|1783->551|2232->973|2295->1015|2344->1036|2532->1197|2593->1237|2902->1519|2935->1531|2986->1555|3090->1638|3139->1659
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->10|37->11|42->11|45->14|45->14|45->14|46->15|54->23|54->23|55->24|61->30|61->30|66->35|66->35|67->36|69->38|70->39
                  -- GENERATED --
              */
          