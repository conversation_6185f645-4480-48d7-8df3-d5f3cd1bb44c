
package na.mpt.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.settings.MptServiceSettings
/*2.2*/import na.mpt.views.html.skeletons.mainSkel
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.mpt.models.wrappers.FamilyWrapper
/*5.2*/import scala.collection.mutable
/*6.2*/import na.mpt.views.html.components.home.leftSideForm
/*7.2*/import na.mpt.controllers.routes.TableConfigs
/*8.2*/import na.naportalbase.views.html.components.lateralNavComp.lateralNavComp
/*9.2*/import na.mpt.settings.AAAPIResources
/*10.2*/import pt.alticelabs.nossis.security.views.html.authorized

object home extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[List[String],FamilyWrapper,MptServiceSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*12.2*/(states : List[String], families: FamilyWrapper, mptServiceSettings: MptServiceSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*30.2*/header/*30.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*30.12*/("""
    """),format.raw/*31.5*/("""<div class="fx-constraint two-rows">
        <div class="pull-left" data-na-portal-mpt-initial-breadcrumb>
            <span class="fx-info-icon">
                <i class="fuxicons fuxicons-mpt"></i>
            </span>
        </div>
        <div class="pull-left">
            <h1>
                """),_display_(/*39.18*/i18n( "na.portal.mpt.home.label.title")),format.raw/*39.57*/("""
            """),format.raw/*40.13*/("""</h1>
            <p class="fx-entity-header-details">
                """),_display_(/*42.18*/i18n( "na.portal.mpt.home.label.subtitle")),format.raw/*42.60*/("""
            """),format.raw/*43.13*/("""</p>
        </div>
    </div>
""")))};def /*72.2*/leftSide/*72.10*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*72.14*/("""
    """),_display_(/*73.6*/leftSideForm/*73.18*/.render(states,families, mptServiceSettings)),format.raw/*73.62*/("""
""")))};def /*94.2*/rightSide/*94.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*94.15*/("""
    """),format.raw/*95.5*/("""<div class="fx-entity-header">
        <div class="fx-entity-header-info">
            <span class="fx-entity-header-icon">
                <i class="fuxicons fuxicons-mpt"></i>
            </span>
            <div class="fx-entity-header-title">
                <h1>
                """),_display_(/*102.18*/i18n( "na.portal.mpt.home.label.title")),format.raw/*102.57*/("""
                """),format.raw/*103.17*/("""</h1>
                <p class="fx-entity-header-details">
                """),_display_(/*105.18*/i18n( "na.portal.mpt.home.label.subtitle")),format.raw/*105.60*/("""
                """),format.raw/*106.17*/("""</p>
            </div>
        </div>
        """),_display_(/*109.10*/authorized(AAAPIResources.operations.X.toString())/*109.60*/ {_display_(Seq[Any](format.raw/*109.62*/("""
            """),format.raw/*110.13*/("""<div class="fx-entity-header-actions">
                <button id="create-button" class="btn btn-primary fx-call-to-action" data-page-action="create-operation">
                    <i class="glyphicon glyphicon-plus"></i>
                    """),_display_(/*113.22*/{
                        i18n("na.portal.mpt.buttons.createoperation")
                    }),format.raw/*115.22*/("""
                """),format.raw/*116.17*/("""</button>
            </div>
        """)))}),format.raw/*118.10*/("""
    """),format.raw/*119.5*/("""</div>

    <x-shadow-scroll flex>
        <div id="entity-content" class="fx-entity-info">
            <div id="datatableMptBulk"
            data-na-portal-table-datatable
            data-na-portal-table-load-using-ajax
            data-na-portal-table-config-url=""""),_display_(/*126.47*/TableConfigs/*126.59*/.bulkSearch()),format.raw/*126.72*/(""""
            data-na-portal-mpt-bulks-search-table>
                <div data-na-portal-table-has-advanced-search></div>
                <div data-na-portal-table-has-column-filter></div>
            </div>
        </div>
    </x-shadow-scroll>

""")))};def /*48.2*/suspendButtonAttributes/*48.25*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "headerCtrl.adminSuspendAll()")
    map.put("id", "adminSuspendButton")
    map.put("class","btn btn-default fx-call-to-action")
    map.put("role","button")
    map.put("style","margin-left:5px;")

    map
}};def /*59.2*/suspendButtonValue/*59.20*/ = {{i18n( "na.portal.mpt.buttons.suspend")}};def /*61.2*/suspendButtoniAttributes/*61.26*/ = {{
    var map = mutable.HashMap[String,String]()
    map
}};def /*66.2*/navElements/*66.13*/ = {{
    var map = mutable.LinkedHashMap[String, mutable.HashMap[String,String]]()

    map
}};def /*76.2*/leftSideBarAttributes/*76.23*/ = {{
    var map = mutable.HashMap[String, String]()

    map.put("data-na-portal-mpt-bulks-search-page", "")

    map
}};def /*84.2*/mpttableattributes/*84.20*/ = {{

    var map = mutable.HashMap[String,String]()
    map.put("id","datatableMpt")
    map.put("data-module","mpt")
    map.put("data-context","search")

    map
}};
Seq[Any](format.raw/*12.90*/("""

"""),format.raw/*14.1*/("""<style>
#datatableMpt td>.progress """),format.raw/*15.28*/("""{"""),format.raw/*15.29*/("""
    """),format.raw/*16.5*/("""height: 12px;
    margin-top: 4px;
    margin-bottom: 0;
    background-color: #676566;
    -webkit-box-shadow: inset -1px 1px 5px -1px rgba(0,0,0,.25);
    -moz-box-shadow: inset -1px 1px 5px -1px rgba(0,0,0,.25);
    box-shadow: inset -1px 1px 5px -1px rgba(0,0,0,.25);
"""),format.raw/*23.1*/("""}"""),format.raw/*23.2*/("""

"""),format.raw/*25.1*/("""#datatableMpt .fx-table-actions>.btn.disabled>i """),format.raw/*25.49*/("""{"""),format.raw/*25.50*/("""
"""),format.raw/*26.1*/("""color: #bbb;
"""),format.raw/*27.1*/("""}"""),format.raw/*27.2*/("""
"""),format.raw/*28.1*/("""</style>

"""),format.raw/*46.2*/("""

"""),format.raw/*57.2*/("""

"""),format.raw/*59.64*/("""

"""),format.raw/*64.2*/("""

"""),format.raw/*70.2*/("""

"""),format.raw/*74.2*/("""

"""),format.raw/*82.2*/("""

"""),format.raw/*92.2*/("""

"""),format.raw/*134.2*/("""

"""),_display_(/*136.2*/mainSkel()/*136.12*/ {_display_(Seq[Any](format.raw/*136.14*/("""
    """),format.raw/*137.5*/("""<div class="fx-push-footer .page--mpt-search-page fx-full-height">
        """),_display_(/*138.10*/if(navElements.size>1)/*138.32*/ {_display_(Seq[Any](format.raw/*138.34*/("""
            """),_display_(/*139.14*/lateralNavComp/*139.28*/.render(navElements, "NaPortalMptNavigationController")),format.raw/*139.83*/("""
        """)))}),format.raw/*140.10*/("""
        """),format.raw/*141.144*/("""
        """),format.raw/*142.9*/("""<div data-na-portal-mpt-bulks-dir data-ng-controller="NaPortalMptCommonController" class="fx-main-content-wrapper """),_display_(/*142.124*/{("fx-with-main-nav").when(navElements.size>1)}),format.raw/*142.171*/("""">
            <x-splitter class="splitter splitter--mpt-search">
                <form slot="left" class="search-sidebar" data-na-portal-mpt-bulks-search-page>
                """),_display_(/*145.18*/leftSide),format.raw/*145.26*/("""
                """),format.raw/*146.17*/("""</form>
                <div id="fx-splitter-content" slot="right">
                """),_display_(/*148.18*/rightSide),format.raw/*148.27*/("""
                """),format.raw/*149.17*/("""</div>
                <div slot="collapsed-left">
                    <div class="fx-info-sidebar-collapsed left">
                        <div class="fx-sidebar-header search-sidebar__header">
                            <span class="fx-entity-header-icon">
                                <i class="fa fa-search"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </x-splitter>
        </div>
    </div>
""")))}),format.raw/*162.2*/("""

"""))
      }
    }
  }

  def render(states:List[String],families:FamilyWrapper,mptServiceSettings:MptServiceSettings): play.twirl.api.HtmlFormat.Appendable = apply(states,families,mptServiceSettings)

  def f:((List[String],FamilyWrapper,MptServiceSettings) => play.twirl.api.HtmlFormat.Appendable) = (states,families,mptServiceSettings) => apply(states,families,mptServiceSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/roots/home.scala.html
                  HASH: 56b679dbb7e3dc4bcc1f8d2fcd0400ce78c7503c
                  MATRIX: 664->1|713->44|764->89|810->129|861->174|900->207|961->262|1014->309|1096->385|1142->424|1544->485|1711->967|1725->973|1806->977|1838->982|2167->1284|2227->1323|2268->1336|2367->1408|2430->1450|2471->1463|2526->2077|2543->2085|2624->2089|2656->2095|2677->2107|2742->2151|2767->2489|2785->2498|2866->2502|2898->2507|3211->2792|3272->2831|3318->2848|3422->2924|3486->2966|3532->2983|3608->3031|3668->3081|3709->3083|3751->3096|4022->3339|4137->3432|4183->3449|4253->3487|4286->3492|4583->3761|4605->3773|4640->3786|4911->1498|4943->1521|5248->1815|5275->1833|5333->1880|5366->1904|5442->1969|5462->1980|5570->2156|5600->2177|5735->2301|5762->2319|5960->573|5989->575|6052->610|6081->611|6113->616|6412->888|6440->889|6469->891|6545->939|6574->940|6602->941|6642->954|6670->955|6698->956|6735->1495|6764->1812|6794->1877|6823->1966|6852->2074|6881->2153|6910->2298|6939->2486|6969->4034|6999->4037|7019->4047|7060->4049|7093->4054|7197->4130|7229->4152|7270->4154|7312->4168|7336->4182|7413->4237|7455->4247|7494->4391|7531->4400|7675->4515|7745->4562|7951->4740|7981->4748|8027->4765|8140->4850|8171->4859|8217->4876|8739->5367
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|38->12|42->30|42->30|44->30|45->31|53->39|53->39|54->40|56->42|56->42|57->43|60->72|60->72|62->72|63->73|63->73|63->73|64->94|64->94|66->94|67->95|74->102|74->102|75->103|77->105|77->105|78->106|81->109|81->109|81->109|82->110|85->113|87->115|88->116|90->118|91->119|98->126|98->126|98->126|106->48|106->48|115->59|115->59|115->61|115->61|118->66|118->66|122->76|122->76|128->84|128->84|137->12|139->14|140->15|140->15|141->16|148->23|148->23|150->25|150->25|150->25|151->26|152->27|152->27|153->28|155->46|157->57|159->59|161->64|163->70|165->74|167->82|169->92|171->134|173->136|173->136|173->136|174->137|175->138|175->138|175->138|176->139|176->139|176->139|177->140|178->141|179->142|179->142|179->142|182->145|182->145|183->146|185->148|185->148|186->149|199->162
                  -- GENERATED --
              */
          