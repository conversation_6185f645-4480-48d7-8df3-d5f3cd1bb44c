
package na.mpt.views.html.components.bulkWizard.modals.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.mpt.settings.MptConstants

object schedulePeriod extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*4.1*/("""<div class="modal-schedulers__period btn-toolbar" role="toolbar">
    <strong>"""),_display_(/*5.14*/i18n("na.portal.mpt.createoperation.modals.scheduling.period")),format.raw/*5.76*/(""":</strong><br/>
    <div class="btn-group">
        <button type="button" class="btn btn-default all_button disabled modal-schedulers__all-weekdays-button" id="weekdays-all" value=""""),_display_(/*7.139*/MptConstants/*7.151*/.WeekDays.ALL),format.raw/*7.164*/("""">"""),_display_(/*7.167*/i18n("na.portal.mpt.weekdays.all")),format.raw/*7.201*/("""</button>
    </div>
    <div class="btn-group weekdays modal-schedulers__weekdays-button-group" style="margin-left: 25%;">
        <button type="button" class="btn btn-default disabled" id="weekdays-monday" value=""""),_display_(/*10.93*/MptConstants/*10.105*/.WeekDays.MONDAY),format.raw/*10.121*/("""">"""),_display_(/*10.124*/i18n("na.portal.mpt.weekdays.monday")),format.raw/*10.161*/("""</button>
        <button type="button" class="btn btn-default disabled" id="weekdays-tuesday" value=""""),_display_(/*11.94*/MptConstants/*11.106*/.WeekDays.TUESDAY),format.raw/*11.123*/("""">"""),_display_(/*11.126*/i18n("na.portal.mpt.weekdays.tuesday")),format.raw/*11.164*/("""</button>
        <button type="button" class="btn btn-default disabled" id="weekdays-wednesday" value=""""),_display_(/*12.96*/MptConstants/*12.108*/.WeekDays.WEDNESDAY),format.raw/*12.127*/("""">"""),_display_(/*12.130*/i18n("na.portal.mpt.weekdays.wednesday")),format.raw/*12.170*/("""</button>
        <button type="button" class="btn btn-default disabled" id="weekdays-thursday"  value=""""),_display_(/*13.96*/MptConstants/*13.108*/.WeekDays.THURSDAY),format.raw/*13.126*/("""">"""),_display_(/*13.129*/i18n("na.portal.mpt.weekdays.thursday")),format.raw/*13.168*/("""</button>
        <button type="button" class="btn btn-default disabled" id="weekdays-friday"  value=""""),_display_(/*14.94*/MptConstants/*14.106*/.WeekDays.FRIDAY),format.raw/*14.122*/("""">"""),_display_(/*14.125*/i18n("na.portal.mpt.weekdays.friday")),format.raw/*14.162*/("""</button>
        <button type="button" class="btn btn-default disabled" id="weekdays-saturday" value=""""),_display_(/*15.95*/MptConstants/*15.107*/.WeekDays.SATURDAY),format.raw/*15.125*/("""">"""),_display_(/*15.128*/i18n("na.portal.mpt.weekdays.saturday")),format.raw/*15.167*/("""</button>
        <button type="button" class="btn btn-default disabled" id="weekdays-sunday" value=""""),_display_(/*16.93*/MptConstants/*16.105*/.WeekDays.SUNDAY),format.raw/*16.121*/("""">"""),_display_(/*16.124*/i18n("na.portal.mpt.weekdays.sunday")),format.raw/*16.161*/("""</button>
    </div>
</div>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkWizard/modals/components/schedulePeriod.scala.html
                  HASH: 3169c328078222826cceb123115c84b9535476ca
                  MATRIX: 698->1|744->41|1175->78|1280->157|1362->219|1571->401|1592->413|1626->426|1656->429|1711->463|1954->679|1976->691|2014->707|2045->710|2104->747|2234->850|2256->862|2295->879|2326->882|2386->920|2518->1025|2540->1037|2581->1056|2612->1059|2674->1099|2806->1204|2828->1216|2868->1234|2899->1237|2960->1276|3090->1379|3112->1391|3150->1407|3181->1410|3240->1447|3371->1551|3393->1563|3433->1581|3464->1584|3525->1623|3654->1725|3676->1737|3714->1753|3745->1756|3804->1793
                  LINES: 24->1|25->2|35->4|36->5|36->5|38->7|38->7|38->7|38->7|38->7|41->10|41->10|41->10|41->10|41->10|42->11|42->11|42->11|42->11|42->11|43->12|43->12|43->12|43->12|43->12|44->13|44->13|44->13|44->13|44->13|45->14|45->14|45->14|45->14|45->14|46->15|46->15|46->15|46->15|46->15|47->16|47->16|47->16|47->16|47->16
                  -- GENERATED --
              */
          