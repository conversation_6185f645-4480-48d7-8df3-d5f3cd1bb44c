
package na.mpt.views.html.components.home

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.mpt.settings.MptServiceSettings
/*2.2*/import na.mpt.models.wrappers.FamilyWrapper
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.mpt.settings.MptConstants
/*5.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*6.2*/import java.util
/*7.2*/import scala.collection.mutable
/*8.2*/import scala.collection.immutable
/*9.2*/import na.naportalbase.views.html.components.inputsComp.modalInputTextComp
/*10.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*11.2*/import na.naportalbase.views.html.skeletons.search.horizontalFormSkel

object modalSaveFilter extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[util.List[String],FamilyWrapper,MptServiceSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*13.2*/(states : util.List[String], families: FamilyWrapper, mptServiceSettings: MptServiceSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*178.2*/form/*178.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*178.10*/("""

"""),format.raw/*180.1*/("""<div>

    <div class="form-group" data-na-portal-field-notification>
        """),_display_(/*183.10*/modalInputTextComp/*183.28*/.render(filterFilterNameLabel, "filterName",filterFilterNamePlaceHolder,filterFilterNameInputAttributes)),format.raw/*183.132*/("""
    """),format.raw/*184.5*/("""</div>

    <div class="fx-expand">
        """),_display_(/*187.10*/i18n("na.modals.filters.options")),format.raw/*187.43*/("""
    """),format.raw/*188.5*/("""</div>
    <div class="clearfix">
        <div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
            <label class="control-label col-sm-3" for="filterStartDatePicker">"""),_display_(/*191.80*/{i18n("na.portal.mpt.field.startdate.label")}),format.raw/*191.125*/("""</label>
            <x-date-time-picker class="col-sm-9" data-field="startDate" format="short"></x-date-time-picker>
        </div>

        <div class="form-group" data-na-portal-field-notification data-na-portal-button-notification>
            <label class="control-label col-sm-3" for="filterEndDatePicker">"""),_display_(/*196.78*/{i18n("na.portal.mpt.field.enddate.label")}),format.raw/*196.121*/("""</label>
            <x-date-time-picker class="col-sm-9" data-field="endDate" format="short"></x-date-time-picker>
        </div>

        <div class="form-group">
            """),_display_(/*201.14*/modalInputTextComp/*201.32*/.render(filterUsernameLabel, "modalUsername",filterUsernamePlaceHolder,filterUsernameInputAttributes)),format.raw/*201.133*/("""
        """),format.raw/*202.9*/("""</div>

        <div class="form-group">
            <label for="modalFamily" class="control-label col-sm-3">"""),_display_(/*205.70*/i18n( "na.portal.mpt.field.family.label")),format.raw/*205.111*/("""</label>
            <div class="col-sm-9">
                """),_display_(/*207.18*/selectComp/*207.28*/.render(familySelectAttributes, null, null, familySelectData)),format.raw/*207.89*/("""
            """),format.raw/*208.13*/("""</div>
        </div>

        <div class="form-group">
            """),_display_(/*212.14*/modalInputTextComp/*212.32*/.render(filterOperationLabel, "modalOperation",filterOperationPlaceHolder,filterOperationInputAttributes)),format.raw/*212.137*/("""
        """),format.raw/*213.9*/("""</div>

        <div class="form-group">
            """),_display_(/*216.14*/modalInputTextComp/*216.32*/.render(filterFileLabel,"modalFileName",filterFilePlaceHolder,filterFileInputAttributes)),format.raw/*216.120*/("""
        """),format.raw/*217.9*/("""</div>

        <div class="form-group">
            <label for="modalStates" class="control-label col-sm-3">"""),_display_(/*220.70*/i18n( "na.portal.mpt.field.state.label")),format.raw/*220.110*/("""</label>
            <div class="col-sm-9">
            """),_display_(/*222.14*/selectComp/*222.24*/.render(stateSelectAttributes, null, null, selectData)),format.raw/*222.78*/("""
            """),format.raw/*223.13*/("""</div>
        </div>

    </div>
</div>
""")))};def /*20.2*/filterStartDateLabel/*20.22*/ = {{i18n("na.portal.mpt.field.startdate.label")}};def /*21.2*/datePickerAttributes/*21.22*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "filterStartDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}};def /*31.2*/inputAttributes/*31.17*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "saveFilterCtrl.tempSearchFormItems.startDate")
    map.put("data-datapickerfield", null)
    map.put("name", "modalStartDate")
    map.put("id", "modalStartDate")
    map
}};def /*41.2*/filterEndDateLabel/*41.20*/ = {{i18n("na.portal.mpt.field.enddate.label")}};def /*42.2*/endDatePickerAttributes/*42.25*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("id", "filterEndDatePicker")
    map.put("data-date-time-picker", null)
    map.put("data-date-time-picker-input", null)
    map.put("data-max-date","today")

    map
}};def /*51.2*/endDateinputAttributes/*51.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "saveFilterCtrl.tempSearchFormItems.endDate")
    map.put("data-datapickerfield", null)
    map.put("name", "modalEndDate")
    map.put("id", "modalEndDate")

    map
}};def /*62.2*/filterUsernameLabel/*62.21*/ = {{i18n( "na.portal.mpt.field.user.label")}};def /*63.2*/filterUsernamePlaceHolder/*63.27*/ = {{i18n( "na.portal.mpt.field.user.placeholder")}};def /*64.2*/filterUsernameInputAttributes/*64.31*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-model", "saveFilterCtrl.tempSearchFormItems.username")
    map.put("name", "modalUsername")
    map.put("id", "modalUsername")

    map
}};def /*75.2*/filterOperationLabel/*75.22*/ = {{i18n( "na.portal.mpt.field.operation.label")}};def /*76.2*/filterOperationPlaceHolder/*76.28*/ = {{i18n( "na.portal.mpt.field.operation.label")}};def /*77.2*/filterOperationInputAttributes/*77.32*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "saveFilterCtrl.tempSearchFormItems.operationName")
    map.put("name", "modalOperation")
    map.put("id", "modalOperation")
    map
}};def /*86.2*/filterFilterNameLabel/*86.23*/ = {{i18n( "na.portal.mpt.field.filtername.label")}};def /*87.2*/filterFilterNamePlaceHolder/*87.29*/ = {{i18n( "na.portal.mpt.field.filtername.placeholder")}};def /*88.2*/filterFilterNameInputAttributes/*88.33*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-na-portal-required",null)
    map.put("data-ng-model", "saveFilterCtrl.tempSearchFormItems.filterName")
    map.put("name", "modalFilterName")
    map.put("id", "modalFilterName")
    map.put("maxlength", "64")

    map
}};def /*100.2*/filterFileLabel/*100.17*/ = {{i18n( "na.portal.mpt.field.file.label")}};def /*101.2*/filterFilePlaceHolder/*101.23*/ = {{i18n( "na.portal.mpt.field.file.placeholder")}};def /*102.2*/filterFileInputAttributes/*102.27*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "saveFilterCtrl.tempSearchFormItems.filename")
    map.put("name", "modalFileName")
    map.put("id", "modalFileName")
    map
}};def /*111.2*/familySelectAttributes/*111.24*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "saveFilterCtrl.tempSearchFormItems.familyName",
        "name" -> "modalFamily",
        "data-na-portal-select-box" -> null,
        "placeholder" -> i18n("na.portal.mpt.selects.option.allfamilies"),
        "id" -> "modalFamily",
        "class" -> "form-control input-sm",
        "style" -> "width: 100%",
        "data-context" -> MptConstants.SELECT_FAMILIES_CONTEXT,
        "allow-clear" -> null
    )
    map
}};def /*126.2*/familySelectData/*126.18*/ = {{
    var stringBuilder = new StringBuilder
    for(family <- families.getEntries){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> family.getName,
            "text" -> family.getName,
            "value" -> family.getName
        )
        stringBuilder.append(dynamicSelectOptionComp.render(family.getName,optionAttributes))
    }
    Html(stringBuilder.toString())
}};def /*140.2*/stateSelectAttributes/*140.23*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> "saveFilterCtrl.tempSearchFormItems.state",
        "name" -> "modalStates",
        "data-na-portal-select-box" -> null,
        "placeholder" -> i18n("na.portal.mpt.selects.states.option.allstates"),
        "id" -> "modalStates",
        "class" -> "form-control input-sm",
        "style" -> "width: 100%",
        "allow-clear" -> null
    )
    map
}};def /*154.2*/selectData/*154.12*/ = {{

    var stringBuilder = new StringBuilder

    for(entry <- states){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> entry,
            "text" -> i18n("na.portal.mpt.bulks.states."+mptServiceSettings.getBulkState(entry)),
            "value" -> entry
        )
        stringBuilder.append(dynamicSelectOptionComp.render(i18n("na.portal.mpt.bulks.states."+mptServiceSettings.getBulkState(entry)),optionAttributes))
    }

    Html(stringBuilder.toString())
}};def /*170.2*/formAttributes/*170.16*/ = {{
    var map = mutable.HashMap[String,String]()

    map.put("data-ng-controller","NaPortalMptBulksSaveFilterModalController as saveFilterCtrl")

    map
}};
Seq[Any](format.raw/*13.95*/("""

"""),format.raw/*15.1*/("""<script>
    $.fn.modal.Constructor.prototype.enforceFocus = function() """),format.raw/*16.64*/("""{"""),format.raw/*16.65*/("""}"""),format.raw/*16.66*/(""";
</script>

<!-- Filter Start Date -->
"""),format.raw/*20.71*/("""
"""),format.raw/*29.2*/("""

"""),format.raw/*38.2*/("""

"""),format.raw/*40.1*/("""<!-- Filter End Date -->
"""),format.raw/*41.67*/("""
"""),format.raw/*50.2*/("""
"""),format.raw/*59.2*/("""

"""),format.raw/*61.1*/("""<!-- USERNAME FIELD -->
"""),format.raw/*62.66*/("""
"""),format.raw/*63.78*/("""
"""),format.raw/*71.2*/("""


"""),format.raw/*74.1*/("""<!-- OPERATION FIELD -->
"""),format.raw/*75.72*/("""
"""),format.raw/*76.78*/("""
"""),format.raw/*83.2*/("""

"""),format.raw/*85.1*/("""<!-- FILTERNAME FIELD -->
"""),format.raw/*86.74*/("""
"""),format.raw/*87.86*/("""
"""),format.raw/*97.2*/("""

"""),format.raw/*99.1*/("""<!-- FILENAME FIELD -->
"""),format.raw/*100.62*/("""
"""),format.raw/*101.74*/("""
"""),format.raw/*108.2*/("""

    """),format.raw/*110.5*/("""<!-- FAMILIES SELECT BOX -->
"""),format.raw/*124.2*/("""

"""),format.raw/*137.2*/("""

"""),format.raw/*139.1*/("""<!-- STATES KEY FIELD -->
"""),format.raw/*152.2*/("""

"""),format.raw/*168.2*/("""

"""),format.raw/*176.2*/("""

"""),format.raw/*228.2*/("""

"""),_display_(/*230.2*/horizontalFormSkel/*230.20*/.render(formAttributes,form)))
      }
    }
  }

  def render(states:util.List[String],families:FamilyWrapper,mptServiceSettings:MptServiceSettings): play.twirl.api.HtmlFormat.Appendable = apply(states,families,mptServiceSettings)

  def f:((util.List[String],FamilyWrapper,MptServiceSettings) => play.twirl.api.HtmlFormat.Appendable) = (states,families,mptServiceSettings) => apply(states,families,mptServiceSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/home/<USER>
                  HASH: 7510ebac9494d0bccd35cf5cb629ebedb685a258
                  MATRIX: 674->1|723->44|774->89|820->129|863->166|951->248|975->266|1014->299|1055->334|1138->410|1214->479|1643->551|1816->5910|1829->5914|1911->5918|1941->5920|2048->5999|2076->6017|2203->6121|2236->6126|2309->6171|2364->6204|2397->6209|2640->6424|2708->6469|3049->6782|3115->6825|3321->7003|3349->7021|3473->7122|3510->7131|3648->7241|3712->7282|3801->7343|3821->7353|3904->7414|3946->7427|4043->7496|4071->7514|4199->7619|4236->7628|4318->7682|4346->7700|4457->7788|4494->7797|4632->7907|4695->7947|4780->8004|4800->8014|4876->8068|4918->8081|4983->761|5012->781|5075->832|5104->852|5354->1091|5378->1106|5648->1390|5675->1408|5736->1457|5768->1480|6016->1716|6047->1738|6312->2016|6340->2035|6399->2082|6433->2107|6498->2160|6536->2189|6762->2430|6791->2450|6855->2502|6890->2528|6954->2580|6993->2610|7224->2856|7254->2877|7319->2930|7355->2957|7426->3016|7466->3047|7773->3366|7798->3381|7858->3428|7889->3449|7955->3502|7990->3527|8215->3773|8247->3795|8764->4300|8790->4316|9216->4756|9247->4777|9700->5218|9720->5228|10237->5733|10261->5747|10452->644|10481->646|10581->718|10610->719|10639->720|10707->830|10735->1088|10764->1362|10793->1364|10846->1455|10874->1714|10902->1989|10931->1991|10983->2080|11012->2158|11040->2401|11070->2404|11123->2500|11152->2578|11180->2827|11209->2829|11263->2928|11292->3014|11320->3339|11349->3341|11402->3426|11432->3500|11461->3737|11495->3743|11552->4297|11582->4727|11612->4729|11666->5215|11696->5730|11726->5907|11756->8123|11786->8126|11814->8144
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|34->11|39->13|43->178|43->178|45->178|47->180|50->183|50->183|50->183|51->184|54->187|54->187|55->188|58->191|58->191|63->196|63->196|68->201|68->201|68->201|69->202|72->205|72->205|74->207|74->207|74->207|75->208|79->212|79->212|79->212|80->213|83->216|83->216|83->216|84->217|87->220|87->220|89->222|89->222|89->222|90->223|95->20|95->20|95->21|95->21|103->31|103->31|110->41|110->41|110->42|110->42|118->51|118->51|126->62|126->62|126->63|126->63|126->64|126->64|133->75|133->75|133->76|133->76|133->77|133->77|139->86|139->86|139->87|139->87|139->88|139->88|148->100|148->100|148->101|148->101|148->102|148->102|154->111|154->111|167->126|167->126|178->140|178->140|190->154|190->154|204->170|204->170|211->13|213->15|214->16|214->16|214->16|218->20|219->29|221->38|223->40|224->41|225->50|226->59|228->61|229->62|230->63|231->71|234->74|235->75|236->76|237->83|239->85|240->86|241->87|242->97|244->99|245->100|246->101|247->108|249->110|250->124|252->137|254->139|255->152|257->168|259->176|261->228|263->230|263->230
                  -- GENERATED --
              */
          