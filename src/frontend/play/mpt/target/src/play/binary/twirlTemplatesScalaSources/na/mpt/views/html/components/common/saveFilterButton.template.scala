
package na.mpt.views.html.components.common

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.naportalbase.views.html.components.buttonsComp.buttonComp
/*3.2*/import scala.collection.mutable

object saveFilterButton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(ngClick: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/saveFilterButtonAttributes/*7.28*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", ngClick)
    map.put("id", "openSaveFilter")
    map.put("class","btn btn-default btn-sm")
    map.put("type","button")
    map
}};def /*15.2*/saveFilterButtoniAttributes/*15.29*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("class","fuxicons fuxicons-floppy-disk")

    map
}};def /*21.2*/saveFilterButtonValue/*21.23*/ = {{i18n("na.modal.button.savefilter")}};
Seq[Any](format.raw/*5.19*/("""

"""),format.raw/*14.2*/("""
"""),format.raw/*20.2*/("""
"""),format.raw/*21.63*/("""
"""),_display_(/*22.2*/buttonComp/*22.12*/.render(saveFilterButtonValue,saveFilterButtonAttributes,saveFilterButtoniAttributes)),format.raw/*22.97*/("""

"""))
      }
    }
  }

  def render(ngClick:String): play.twirl.api.HtmlFormat.Appendable = apply(ngClick)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (ngClick) => apply(ngClick)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/common/saveFilterButton.scala.html
                  HASH: 4b2ab1f3c4b03799336cbd377f10b301d5c38e42
                  MATRIX: 676->1|722->41|797->110|1144->144|1239->164|1273->190|1499->404|1535->431|1665->549|1695->570|1765->161|1794->402|1822->547|1851->610|1879->612|1898->622|2004->707
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|42->15|42->15|47->21|47->21|48->5|50->14|51->20|52->21|53->22|53->22|53->22
                  -- GENERATED --
              */
          