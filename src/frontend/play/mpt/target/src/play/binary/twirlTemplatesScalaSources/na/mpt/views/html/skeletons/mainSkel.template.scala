
package na.mpt.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.html.skeletons.root
/*2.2*/import na.mpt.views.html.imports

object mainSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*3.2*/(title: String = "")(content: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*5.2*/scripts/*5.9*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*5.13*/("""
"""),_display_(/*6.2*/imports/*6.9*/.scripts.render()),format.raw/*6.26*/("""
""")))};def /*9.2*/body/*9.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*9.10*/("""
"""),format.raw/*10.1*/("""<div id="mpt" class="page page--mpt fx-full-height inherit" data-na-portal-page-container>
    """),_display_(/*11.6*/content),format.raw/*11.13*/("""
"""),format.raw/*12.1*/("""</div>
""")))};
Seq[Any](format.raw/*3.37*/("""

"""),format.raw/*7.2*/("""

"""),format.raw/*13.2*/("""

"""),_display_(/*15.2*/root/*15.6*/.render(title, body, scripts)),format.raw/*15.35*/("""
"""))
      }
    }
  }

  def render(title:String,content:Html): play.twirl.api.HtmlFormat.Appendable = apply(title)(content)

  def f:((String) => (Html) => play.twirl.api.HtmlFormat.Appendable) = (title) => (content) => apply(title)(content)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/skeletons/mainSkel.scala.html
                  HASH: 9c7c5f5b8c68b9f0ee41e9394659835c64426d83
                  MATRIX: 668->1|724->51|1069->85|1182->123|1196->130|1276->134|1303->136|1317->143|1354->160|1378->165|1389->169|1469->173|1497->174|1619->270|1647->277|1675->278|1722->120|1750->162|1779->286|1808->289|1820->293|1870->322
                  LINES: 24->1|25->2|30->3|34->5|34->5|36->5|37->6|37->6|37->6|38->9|38->9|40->9|41->10|42->11|42->11|43->12|45->3|47->7|49->13|51->15|51->15|51->15
                  -- GENERATED --
              */
          