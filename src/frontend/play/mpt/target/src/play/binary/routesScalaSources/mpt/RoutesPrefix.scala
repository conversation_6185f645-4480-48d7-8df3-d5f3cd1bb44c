// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/conf/mpt.routes
// @DATE:Wed Jul 02 14:15:38 WEST 2025


package mpt {
  object RoutesPrefix {
    private var _prefix: String = "/"
    def setPrefix(p: String): Unit = {
      _prefix = p
    }
    def prefix: String = _prefix
    val byNamePrefix: Function0[String] = { () => prefix }
  }
}
