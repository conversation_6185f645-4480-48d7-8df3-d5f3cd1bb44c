
package na.mpt.views.html.components.bulkdetails

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n

object characteristicsTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*3.2*/descriptionBlock/*3.18*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*3.22*/("""
    """),format.raw/*4.5*/("""<div class="collapsible-list">
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*6.52*/i18n("na.portal.mpt.bulk.details.characteristics.description.filename")),format.raw/*6.123*/("""</label>
            <div class="col-sm-10">
                <p id="bulkDescriptionFilename" class="form-control-static" data-ng-bind="detailsCtrl.bulkDetails.fileName">
                </p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*13.52*/i18n("na.portal.mpt.bulk.details.characteristics.description.familyname")),format.raw/*13.125*/("""</label>
            <div class="col-sm-10">
                <p id="bulkDescriptionFamilyName" class="form-control-static" data-ng-bind="detailsCtrl.bulkDetails.familyName">
                </p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*20.52*/i18n("na.portal.mpt.bulk.details.characteristics.description.operationname")),format.raw/*20.128*/("""</label>
            <div class="col-sm-10">
                <p id="bulkDescriptionOperationName" class="form-control-static" data-ng-bind="detailsCtrl.bulkDetails.operationName">
                </p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*27.52*/i18n("na.portal.mpt.bulk.details.characteristics.description.description")),format.raw/*27.126*/("""</label>
            <div class="col-sm-10">
                <p id="bulkDescription" class="form-control-static" data-ng-bind="detailsCtrl.bulkDetails.description">
                </p>
            </div>
        </div>
    </div>
""")))};def /*36.2*/scheduleBlock/*36.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*36.19*/("""
    """),format.raw/*37.5*/("""<div class="form-group">
        <div data-ng-controller="NaPortalMptBulkDetailsSchedulesDatatableController">
            <div data-na-portal-datatables-toolbar class="fx-bulk-actions fx-table-actions clearfix"></div>
            <div data-na-portal-toolbar-advanced-search data-table-id="datatableBulkDetailsSchedules"></div>
            <div id="loadingWrapper_datatableBulkDetailsSchedules">
                <div data-ng-controller="NaPortalDatatablesController">
                    <table class="table table-striped table-hover"
                        id="datatableBulkDetailsSchedules"
                        data-na-portal-datatable
                        data-dt-options="dtOptions"
                        data-dt-columns="dtColumns"></table>
                </div>
            </div>
        </div>
    </div>
""")))};def /*54.2*/transitionsBlock/*54.18*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*54.22*/("""
    """),format.raw/*55.5*/("""<div class="form-group">
        <div data-ng-controller="NaPortalMptBulkDetailsTransitionsDatatableController">
            <div data-na-portal-datatables-toolbar class="fx-bulk-actions fx-table-actions clearfix"></div>
            <div data-na-portal-toolbar-advanced-search data-table-id="datatableBulkDetailsTransitions"></div>
            <div id="loadingWrapper_datatableBulkDetailsTransitions">
                <div data-ng-controller="NaPortalDatatablesController">
                    <table class="table table-striped table-hover"
                        id="datatableBulkDetailsTransitions"
                        data-na-portal-datatable
                        data-dt-options="dtOptions"
                        data-dt-columns="dtColumns"></table>
                </div>
            </div>
        </div>
    </div>
""")))};
Seq[Any](format.raw/*34.2*/("""

"""),format.raw/*52.2*/("""

"""),format.raw/*70.2*/("""

"""),format.raw/*72.1*/("""<x-collapsible class="fx-section" header=""""),_display_(/*72.44*/{i18n("na.portal.mpt.result.details.characteristics.description")}),format.raw/*72.110*/("""">
    """),_display_(/*73.6*/descriptionBlock),format.raw/*73.22*/("""
"""),format.raw/*74.1*/("""</x-collapsible>

<x-collapsible class="fx-section" header=""""),_display_(/*76.44*/{i18n("na.portal.mpt.bulk.details.characteristics.schedule")}),format.raw/*76.105*/("""">
    """),_display_(/*77.6*/scheduleBlock),format.raw/*77.19*/("""
"""),format.raw/*78.1*/("""</x-collapsible>

<x-collapsible class="fx-section" header=""""),_display_(/*80.44*/{i18n("na.portal.mpt.bulk.details.characteristics.transitions")}),format.raw/*80.108*/("""">
    """),_display_(/*81.6*/transitionsBlock),format.raw/*81.22*/("""
"""),format.raw/*82.1*/("""</x-collapsible>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkdetails/characteristicsTab.scala.html
                  HASH: 60501f31b66532be1b60d0dd7742e4bc0f78a707
                  MATRIX: 681->1|1103->42|1127->58|1207->62|1238->67|1379->182|1471->253|1807->562|1902->635|2242->948|2340->1024|2686->1343|2782->1417|3037->1652|3059->1665|3140->1669|3172->1674|4020->2502|4045->2518|4126->2522|4158->2527|5030->1649|5059->2499|5088->3360|5117->3362|5187->3405|5275->3471|5309->3479|5346->3495|5374->3496|5462->3557|5545->3618|5579->3626|5613->3639|5641->3640|5729->3701|5815->3765|5849->3773|5886->3789|5914->3790
                  LINES: 24->1|33->3|33->3|35->3|36->4|38->6|38->6|45->13|45->13|52->20|52->20|59->27|59->27|66->36|66->36|68->36|69->37|84->54|84->54|86->54|87->55|103->34|105->52|107->70|109->72|109->72|109->72|110->73|110->73|111->74|113->76|113->76|114->77|114->77|115->78|117->80|117->80|118->81|118->81|119->82
                  -- GENERATED --
              */
          