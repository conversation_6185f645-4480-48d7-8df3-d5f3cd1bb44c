
package na.mpt.views.html.components.resultsdetails

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n

object characteristicsTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*3.2*/descriptionBlock/*3.18*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*3.22*/("""
    """),format.raw/*4.5*/("""<div class="collapsible-list">
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*6.52*/i18n("na.portal.mpt.result.details.characteristics.description.filename")),format.raw/*6.125*/("""</label>
            <div class="col-sm-10">
                <p id="bulkResultDescriptionFilename" class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.fileName">
                </p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*13.52*/i18n("na.portal.mpt.result.details.characteristics.description.familyname")),format.raw/*13.127*/("""</label>
            <div class="col-sm-10">
                <p id="bulkResultDescriptionFamily" class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.familyName">
                </p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*20.52*/i18n("na.portal.mpt.result.details.characteristics.description.operationname")),format.raw/*20.130*/("""</label>
            <div class="col-sm-10">
                <p id="bulkResultDescriptionOperation" class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.operationName">
                </p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*27.52*/i18n("na.portal.mpt.result.details.characteristics.description.requestdate")),format.raw/*27.128*/("""</label>
            <div class="col-sm-10">
                <p class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.requestDate">
                </p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*34.52*/i18n("na.portal.mpt.result.details.characteristics.description.testduration")),format.raw/*34.129*/("""</label>
            <div class="col-sm-10">
                <p class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.testDuration">
                </p>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*41.52*/i18n("na.portal.mpt.result.details.characteristics.description.lineNum")),format.raw/*41.124*/("""</label>
            <div class="col-sm-10">
                <p id="bulkResultDescriptionLine" class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.lineNum">
                </p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*49.52*/i18n("na.portal.mpt.result.details.characteristics.description.username")),format.raw/*49.125*/("""</label>
            <div class="col-sm-10">
                <p id="bulkResultDescriptionUsername" class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.userName">
                </p>
            </div>
        </div>
    </div>
""")))};def /*58.2*/statusBlock/*58.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*58.17*/("""
    """),format.raw/*59.5*/("""<div class="collapsible-list">
        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*61.52*/i18n("na.portal.mpt.result.details.characteristics.state.starttime")),format.raw/*61.120*/("""</label>
            <div class="col-sm-10">
                <p class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.startTime">
                </p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*69.52*/i18n("na.portal.mpt.result.details.characteristics.state.endtime")),format.raw/*69.118*/("""</label>
            <div class="col-sm-10">
                <p class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.endTime">
                </p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*77.52*/i18n("na.portal.mpt.result.details.characteristics.state.status")),format.raw/*77.117*/("""</label>
            <div class="col-sm-10">
                <p id="bulkResultDescriptionStatus" class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.status">
                </p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">"""),_display_(/*85.52*/i18n("na.portal.mpt.result.details.characteristics.state.description")),format.raw/*85.122*/("""</label>
            <div class="col-sm-10">
                <p class="form-control-static" data-ng-bind="detailsCtrl.resultDetails.description">
                </p>
            </div>
        </div>
    </div>
""")))};def /*94.2*/payloadBlock/*94.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*94.18*/("""
    """),format.raw/*95.5*/("""<x-collapsible class="fx-section" header=""""),_display_(/*95.48*/i18n("na.portal.mpt.result.details.label.request")),format.raw/*95.98*/("""">
        <div class="form-group">
            <div data-na-portal-mpt-result-details-request-datatable>
                <div data-ng-controller="NaPortalDatatablesController">
                    <table id="datatableResultDetailsRequest" class="table table-striped table-hover no-footer"
                        data-na-portal-datatable
                        data-dt-options="dtOptions"
                        data-dt-columns="dtColumns"
                        dt-disable-deep-watchers="true"></table>
                </div>
            </div>
        </div>
    </x-collapsible>

    <x-collapsible class="fx-section" header=""""),_display_(/*109.48*/i18n("na.portal.mpt.result.details.label.response")),format.raw/*109.99*/("""">
        <div class="form-group">
            <div data-na-portal-mpt-result-details-response-datatable>
                <div data-ng-controller="NaPortalDatatablesController">
                    <table id="datatableResultDetailsResponse" class="table table-striped table-hover no-footer"
                        data-na-portal-datatable
                        data-dt-options="dtOptions"
                        data-dt-columns="dtColumns"
                        dt-disable-deep-watchers="true"></table>
                </div>
            </div>
        </div>
    </x-collapsible>
""")))};
Seq[Any](format.raw/*56.2*/("""

"""),format.raw/*92.2*/("""

"""),format.raw/*122.2*/("""

"""),format.raw/*124.1*/("""<x-collapsible class="fx-section" header=""""),_display_(/*124.44*/{i18n("na.portal.mpt.result.details.characteristics.description")}),format.raw/*124.110*/("""">
    """),_display_(/*125.6*/descriptionBlock),format.raw/*125.22*/("""
"""),format.raw/*126.1*/("""</x-collapsible>

<x-collapsible class="fx-section" header=""""),_display_(/*128.44*/{i18n("na.portal.mpt.result.details.characteristics.state")}),format.raw/*128.104*/("""">
    """),_display_(/*129.6*/statusBlock),format.raw/*129.17*/("""
"""),format.raw/*130.1*/("""</x-collapsible>

<x-collapsible class="fx-section" header=""""),_display_(/*132.44*/{i18n("na.portal.mpt.result.details.characteristics.payload")}),format.raw/*132.106*/("""">
    """),_display_(/*133.6*/payloadBlock),format.raw/*133.18*/("""
"""),format.raw/*134.1*/("""</x-collapsible>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/resultsdetails/characteristicsTab.scala.html
                  HASH: 3b10b0bce888e299f215a2d50a810b42bf991fd1
                  MATRIX: 684->1|1106->42|1130->58|1210->62|1241->67|1382->182|1476->255|1820->572|1917->647|2261->964|2361->1042|2711->1365|2809->1441|3121->1726|3220->1803|3533->2089|3627->2161|3967->2474|4062->2547|4330->2795|4350->2806|4431->2810|4463->2815|4605->2930|4695->2998|5006->3282|5094->3348|5403->3630|5490->3695|5831->4009|5923->4079|6159->4295|6180->4307|6261->4311|6293->4316|6363->4359|6434->4409|7096->5043|7169->5094|7797->2792|7826->4292|7856->5683|7886->5685|7957->5728|8046->5794|8081->5802|8119->5818|8148->5819|8237->5880|8320->5940|8355->5948|8388->5959|8417->5960|8506->6021|8591->6083|8626->6091|8660->6103|8689->6104
                  LINES: 24->1|33->3|33->3|35->3|36->4|38->6|38->6|45->13|45->13|52->20|52->20|59->27|59->27|66->34|66->34|73->41|73->41|81->49|81->49|88->58|88->58|90->58|91->59|93->61|93->61|101->69|101->69|109->77|109->77|117->85|117->85|124->94|124->94|126->94|127->95|127->95|127->95|141->109|141->109|155->56|157->92|159->122|161->124|161->124|161->124|162->125|162->125|163->126|165->128|165->128|166->129|166->129|167->130|169->132|169->132|170->133|170->133|171->134
                  -- GENERATED --
              */
          