
package na.mpt.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.mpt.controllers.routes.Application.javascriptRoutes),format.raw/*1.69*/(""""></script><script src='"""),_display_(/*1.94*/na/*1.96*/.mpt.controllers.routes.Assets.versioned("basemodule/runtime.caec9c6b7f6f9521368d.min.js")),format.raw/*1.186*/("""'></script><script src='"""),_display_(/*1.211*/na/*1.213*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-fuxi.44e71963e917fd2be2ca.min.js")),format.raw/*1.322*/("""'></script><script src='"""),_display_(/*1.347*/na/*1.349*/.mpt.controllers.routes.Assets.versioned("na-portal-assets/base-styles.9e693254edb056a100ad.min.js")),format.raw/*1.449*/("""'></script><script src='"""),_display_(/*1.474*/na/*1.476*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.6f86c32db7bf4ccaf010.min.js")),format.raw/*1.590*/("""'></script><script src='"""),_display_(/*1.615*/na/*1.617*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.c845a1da27d2bbccf773.min.js")),format.raw/*1.729*/("""'></script><script src='"""),_display_(/*1.754*/na/*1.756*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.ab8ed0c92f51f5eb905e.min.js")),format.raw/*1.870*/("""'></script><script src='"""),_display_(/*1.895*/na/*1.897*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.bc173ec447d65947d31a.min.js")),format.raw/*1.1008*/("""'></script><script src='"""),_display_(/*1.1033*/na/*1.1035*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.c69a6ab040df2ef632e6.min.js")),format.raw/*1.1146*/("""'></script><script src='"""),_display_(/*1.1171*/na/*1.1173*/.mpt.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.a53001b54dbc3b115cc4.min.js")),format.raw/*1.1276*/("""'></script><script src='"""),_display_(/*1.1301*/na/*1.1303*/.mpt.controllers.routes.Assets.versioned("utils/utils.88e56ee512f1a3af972d.min.js")),format.raw/*1.1386*/("""'></script><script src='"""),_display_(/*1.1411*/na/*1.1413*/.mpt.controllers.routes.Assets.versioned("basemodule/basemodule.823a1b10758c828389cd.min.js")),format.raw/*1.1506*/("""'></script><script src='"""),_display_(/*1.1531*/na/*1.1533*/.mpt.controllers.routes.Assets.versioned("mpt/mpt.b05f1f0cf93b23476bbd.min.js")),format.raw/*1.1612*/("""'></script>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/imports/scripts.scala.html
                  HASH: eee282deae7eed32c6a4c49fded8d2b942eeb953
                  MATRIX: 1047->0|1087->14|1097->16|1169->68|1220->93|1230->95|1341->185|1393->210|1404->212|1534->321|1586->346|1597->348|1718->448|1770->473|1781->475|1916->589|1968->614|1979->616|2112->728|2164->753|2175->755|2310->869|2362->894|2373->896|2506->1007|2559->1032|2571->1034|2704->1145|2757->1170|2769->1172|2894->1275|2947->1300|2959->1302|3064->1385|3117->1410|3129->1412|3244->1505|3297->1530|3309->1532|3410->1611
                  LINES: 33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1
                  -- GENERATED --
              */
          