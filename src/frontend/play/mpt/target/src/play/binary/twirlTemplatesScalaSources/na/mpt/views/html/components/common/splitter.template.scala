
package na.mpt.views.html.components.common

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable

object splitter extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[Html,Html,mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*2.2*/(leftSide: Html, rightSide: Html, leftSideAttributes: mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*4.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*2.88*/("""

"""),format.raw/*5.1*/("""
"""),format.raw/*6.1*/("""<x-splitter class="splitter splitter--mpt-admin-search">
    <form class="search-sidebar"
        slot="left"
        data-na-portal-mpt-bulks-search-page
        """),_display_(/*10.10*/TemplateUtils/*10.23*/.dynamicElementsAttributes(leftSideAttributes)),format.raw/*10.69*/("""
    """),format.raw/*11.5*/(""">
    """),_display_(/*12.6*/leftSide),format.raw/*12.14*/("""
    """),format.raw/*13.5*/("""</form>
    <div class="fx-splitter-content-inner" slot="right">
    """),_display_(/*15.6*/rightSide),format.raw/*15.15*/("""
    """),format.raw/*16.5*/("""</div>
    <div slot="collapsed-left">
        <div class="fx-info-sidebar-collapsed left">
            <span class="fx-info-icon">
                <i class="glyphicon glyphicon-search"></i>
            </span>
        </div>
    </div>
</x-splitter>
"""))
      }
    }
  }

  def render(leftSide:Html,rightSide:Html,leftSideAttributes:mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(leftSide,rightSide,leftSideAttributes)

  def f:((Html,Html,mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (leftSide,rightSide,leftSideAttributes) => apply(leftSide,rightSide,leftSideAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/common/splitter.scala.html
                  HASH: d1f7cd000c1f6c7c698528b748c1c19393cc1b62
                  MATRIX: 676->1|1050->34|1209->123|1281->120|1309->166|1336->167|1527->331|1549->344|1616->390|1648->395|1681->402|1710->410|1742->415|1838->485|1868->494|1900->499
                  LINES: 24->1|29->2|32->4|35->2|37->5|38->6|42->10|42->10|42->10|43->11|44->12|44->12|45->13|47->15|47->15|48->16
                  -- GENERATED --
              */
          