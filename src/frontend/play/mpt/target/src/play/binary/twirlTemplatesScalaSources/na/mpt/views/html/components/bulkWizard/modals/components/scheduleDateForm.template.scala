
package na.mpt.views.html.components.bulkWizard.modals.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.mpt.settings.MptConstants

object scheduleDateForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*4.1*/("""<div class="form-group mpt-wizard-modal__date-form">

    <div class="col-sm-6">
        <label class="control-label date-form__label col-sm-2 fx-required" for="startDate">
        """),_display_(/*8.10*/i18n("na.portal.mpt.createoperation.modals.scheduling.startDate")),format.raw/*8.75*/("""<abbr title=""""),_display_(/*8.89*/i18n("na.basemodule.validations.mandatory")),format.raw/*8.132*/(""""></abbr>
        </label>
        <div class="col-sm-10">
                <x-date-time-picker
                class="schedule-date-form__date-time-picker"
                auto-select="start day"
                id="startDate"
                name="startDate"></x-date-time-picker>
        </div>
    </div>

    <div class="col-sm-6">
        <label id="schMod_end_label" class="control-label date-form__label col-sm-2 fx-required">
        """),_display_(/*21.10*/i18n("na.portal.mpt.createoperation.modals.scheduling.endDate")),format.raw/*21.73*/("""<abbr title=""""),_display_(/*21.87*/i18n("na.basemodule.validations.mandatory")),format.raw/*21.130*/(""""></abbr>
        </label>
        <div id="schedule-term-type" class="col-sm-10">
            <div class="mpt-wizard-modal__date-input-option">
                <input
                id="term-type-"""),_display_(/*26.32*/MptConstants/*26.44*/.SCHEDULING_TYPE_SCHEDULING_INTERVALS),format.raw/*26.81*/(""""
                type="radio"
                name="term-type"
                checked
                value=""""),_display_(/*30.25*/MptConstants/*30.37*/.SCHEDULING_TYPE_SCHEDULING_INTERVALS),format.raw/*30.74*/("""">
                <label style="font-weight: normal" for="term-type-"""),_display_(/*31.68*/MptConstants/*31.80*/.SCHEDULING_TYPE_SCHEDULING_INTERVALS),format.raw/*31.117*/("""">"""),_display_(/*31.120*/i18n("na.portal.mpt.createoperation.modals.scheduling.endBy")),format.raw/*31.181*/("""</label>
                <x-date-time-picker
                class="schedule-date-form__date-time-picker"
                id="endDate"
                name="endDate"
                auto-select="end day"></x-date-time-picker>
            </div>
            <div class="mpt-wizard-modal__date-input-option">
                <input id="term-type-"""),_display_(/*39.39*/MptConstants/*39.51*/.SCHEDULING_TYPE_SCHEDULING_NOLIMIT),format.raw/*39.86*/(""""
                type="radio"
                name="term-type"
                value=""""),_display_(/*42.25*/MptConstants/*42.37*/.SCHEDULING_TYPE_SCHEDULING_NOLIMIT),format.raw/*42.72*/("""">
                <label id="noLimitLabel" for="term-type-"""),_display_(/*43.58*/MptConstants/*43.70*/.SCHEDULING_TYPE_SCHEDULING_NOLIMIT),format.raw/*43.105*/("""">"""),_display_(/*43.108*/i18n("na.portal.mpt.createoperation.modals.scheduling.noLimit")),format.raw/*43.171*/("""</label>
            </div>
        </div>
    </div>
</div>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:38 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/mpt/target/TwirlSource/na/mpt/views/components/bulkWizard/modals/components/scheduleDateForm.scala.html
                  HASH: ab938be26bbc5308f2464b2e0a3e3bdce3dea4d7
                  MATRIX: 698->1|744->41|1177->78|1385->260|1470->325|1510->339|1574->382|2044->825|2128->888|2169->902|2234->945|2460->1144|2481->1156|2539->1193|2678->1305|2699->1317|2757->1354|2854->1424|2875->1436|2934->1473|2965->1476|3048->1537|3420->1882|3441->1894|3497->1929|3612->2017|3633->2029|3689->2064|3776->2124|3797->2136|3854->2171|3885->2174|3970->2237
                  LINES: 24->1|25->2|35->4|39->8|39->8|39->8|39->8|52->21|52->21|52->21|52->21|57->26|57->26|57->26|61->30|61->30|61->30|62->31|62->31|62->31|62->31|62->31|70->39|70->39|70->39|73->42|73->42|73->42|74->43|74->43|74->43|74->43|74->43
                  -- GENERATED --
              */
          