
package na.catalog.basemodule.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputText extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.catalog.basemodule.ui.InputText,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(element: na.catalog.basemodule.ui.InputText):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.catalog.basemodule.ui.Action
/*4.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.47*/("""

"""),format.raw/*5.1*/("""
"""),_display_(/*6.2*/if(Action.VIEW.equals(element.getAction))/*6.43*/ {_display_(Seq[Any](format.raw/*6.45*/("""
    """),format.raw/*7.5*/("""<span id=""""),_display_(/*7.16*/element/*7.23*/.getId),format.raw/*7.29*/("""">"""),_display_(/*7.32*/element/*7.39*/.getValue),format.raw/*7.48*/("""</span>
""")))}/*8.3*/else/*8.8*/{_display_(Seq[Any](format.raw/*8.9*/("""
    """),format.raw/*9.5*/("""<input """),_display_(/*9.13*/TemplateUtils/*9.26*/.dynamicElementsAttributes(element.getDOMAttributes)),format.raw/*9.78*/(""">
""")))}))
      }
    }
  }

  def render(element:na.catalog.basemodule.ui.InputText): play.twirl.api.HtmlFormat.Appendable = apply(element)

  def f:((na.catalog.basemodule.ui.InputText) => play.twirl.api.HtmlFormat.Appendable) = (element) => apply(element)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/catalog-basemodule/target/TwirlSource/na/catalog/basemodule/views/components/inputText.scala.html
                  HASH: 1ad5d2a529d0774bfe08dd941859be7d7bfaede9
                  MATRIX: 1013->1|1131->49|1177->89|1249->46|1277->132|1304->134|1353->175|1392->177|1423->182|1460->193|1475->200|1501->206|1530->209|1545->216|1574->225|1600->235|1611->240|1648->241|1679->246|1713->254|1734->267|1806->319
                  LINES: 28->1|31->3|32->4|35->1|37->5|38->6|38->6|38->6|39->7|39->7|39->7|39->7|39->7|39->7|39->7|40->8|40->8|40->8|41->9|41->9|41->9|41->9
                  -- GENERATED --
              */
          