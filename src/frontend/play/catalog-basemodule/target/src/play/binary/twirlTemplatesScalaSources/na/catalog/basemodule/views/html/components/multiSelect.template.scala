
package na.catalog.basemodule.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object multiSelect extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.catalog.basemodule.ui.MultiSelect,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*2.2*/(element: na.catalog.basemodule.ui.MultiSelect):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*4.2*/import na.catalog.basemodule.ui.Action
/*5.2*/import na.naportalbase.utils.TemplateUtils
/*6.2*/import na.naportalbase.views.tags.i18n

def /*16.2*/selected/*16.10*/(value: String):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*16.29*/("""
    """),_display_(/*17.6*/if(value != null && value.equals(element.getValue))/*17.57*/ {_display_(Seq[Any](format.raw/*17.59*/("""
        """),format.raw/*18.9*/("""selected
    """)))}),format.raw/*19.6*/("""
""")))};def /*8.2*/viewValue/*8.11*/() = {{
    var value: String = element.getValue.asInstanceOf[String]
    if(value.isEmpty) {
        value = "--"
    }
    value
}};
Seq[Any](format.raw/*2.49*/("""

"""),format.raw/*7.1*/("""
"""),format.raw/*14.2*/("""

"""),format.raw/*20.2*/("""

"""),_display_(/*22.2*/if(Action.VIEW.equals(element.getAction))/*22.43*/ {_display_(Seq[Any](format.raw/*22.45*/("""
    """),format.raw/*23.5*/("""<span id=""""),_display_(/*23.16*/element/*23.23*/.getId),format.raw/*23.29*/("""">"""),_display_(/*23.32*/viewValue),format.raw/*23.41*/("""</span>
""")))}/*24.3*/else/*24.8*/{_display_(Seq[Any](format.raw/*24.9*/("""
    """),format.raw/*25.5*/("""<"""),_display_(/*25.7*/element/*25.14*/.getTagName),format.raw/*25.25*/(""" """),_display_(/*25.27*/TemplateUtils/*25.40*/.dynamicElementsAttributes(element.getDOMAttributes)),format.raw/*25.92*/(""">
    """),_display_(/*26.6*/for(option <- element.getOptions) yield /*26.39*/ {_display_(Seq[Any](format.raw/*26.41*/("""
        """),format.raw/*27.9*/("""<option value=""""),_display_(/*27.25*/option/*27.31*/.value),format.raw/*27.37*/("""" """),_display_(/*27.40*/selected(option.value)),format.raw/*27.62*/(""">"""),_display_(/*27.64*/option/*27.70*/.text),format.raw/*27.75*/("""</option>
    """)))}),format.raw/*28.6*/("""
    """),format.raw/*29.5*/("""</"""),_display_(/*29.8*/element/*29.15*/.getTagName),format.raw/*29.26*/(""">
""")))}))
      }
    }
  }

  def render(element:na.catalog.basemodule.ui.MultiSelect): play.twirl.api.HtmlFormat.Appendable = apply(element)

  def f:((na.catalog.basemodule.ui.MultiSelect) => play.twirl.api.HtmlFormat.Appendable) = (element) => apply(element)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/catalog-basemodule/target/TwirlSource/na/catalog/basemodule/views/components/multiSelect.scala.html
                  HASH: 6ffd3a22ecfdc12526d84e9b60ce4046d56794d9
                  MATRIX: 1017->2|1137->52|1183->92|1233->136|1285->321|1302->329|1398->348|1430->354|1490->405|1530->407|1566->416|1610->430|1634->177|1651->186|1813->49|1841->175|1869->318|1898->432|1927->435|1977->476|2017->478|2049->483|2087->494|2103->501|2130->507|2160->510|2190->519|2217->529|2229->534|2267->535|2299->540|2327->542|2343->549|2375->560|2404->562|2426->575|2499->627|2532->634|2581->667|2621->669|2657->678|2700->694|2715->700|2742->706|2772->709|2815->731|2844->733|2859->739|2885->744|2930->759|2962->764|2991->767|3007->774|3039->785
                  LINES: 28->2|31->4|32->5|33->6|35->16|35->16|37->16|38->17|38->17|38->17|39->18|40->19|41->8|41->8|48->2|50->7|51->14|53->20|55->22|55->22|55->22|56->23|56->23|56->23|56->23|56->23|56->23|57->24|57->24|57->24|58->25|58->25|58->25|58->25|58->25|58->25|58->25|59->26|59->26|59->26|60->27|60->27|60->27|60->27|60->27|60->27|60->27|60->27|60->27|61->28|62->29|62->29|62->29|62->29
                  -- GENERATED --
              */
          