
package na.catalog.basemodule.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object tags extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.catalog.basemodule.ui.MultiSelect,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(element: na.catalog.basemodule.ui.MultiSelect):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.catalog.basemodule.ui.Action
/*4.2*/import na.naportalbase.utils.TemplateUtils
/*5.2*/import na.naportalbase.views.tags.i18n

def /*7.2*/placeholder/*7.13*/ = {{
    if(element.getPlaceholder != null) {
        element.getPlaceholder.getTranslation
    }
}};def /*13.2*/viewValue/*13.11*/() = {{
    element.getValue.asInstanceOf[String]
}};
Seq[Any](format.raw/*1.49*/("""

"""),format.raw/*6.1*/("""
"""),format.raw/*11.2*/("""

"""),format.raw/*15.2*/("""

"""),_display_(/*17.2*/if(Action.VIEW.equals(element.getAction) && viewValue.isEmpty)/*17.64*/ {_display_(Seq[Any](format.raw/*17.66*/("""
    """),format.raw/*18.5*/("""<span id=""""),_display_(/*18.16*/element/*18.23*/.getId),format.raw/*18.29*/("""">--</span>

""")))}/*20.3*/else if(Action.VIEW.equals(element.getAction))/*20.49*/ {_display_(Seq[Any](format.raw/*20.51*/("""
    """),format.raw/*21.5*/("""<x-tag-container id=""""),_display_(/*21.27*/element/*21.34*/.getId),format.raw/*21.40*/("""">
    """),_display_(/*22.6*/for(tagName <- viewValue.split(",")) yield /*22.42*/ {_display_(Seq[Any](format.raw/*22.44*/("""
        """),format.raw/*23.9*/("""<x-tag>"""),_display_(/*23.17*/tagName),format.raw/*23.24*/("""</x-tag>
    """)))}),format.raw/*24.6*/("""
    """),format.raw/*25.5*/("""</x-tag-container>
""")))}/*26.3*/else if(Action.SEARCH.equals(element.getAction))/*26.51*/{_display_(Seq[Any](format.raw/*26.52*/("""
    """),format.raw/*27.5*/("""<x-multiselect """),_display_(/*27.21*/TemplateUtils/*27.34*/.dynamicElementsAttributes(element.getDOMAttributes)),format.raw/*27.86*/("""></x-multiselect>
""")))}/*28.3*/else/*28.8*/{_display_(Seq[Any](format.raw/*28.9*/("""
    """),format.raw/*29.5*/("""<x-multiselect """),_display_(/*29.21*/TemplateUtils/*29.34*/.dynamicElementsAttributes(element.getDOMAttributes)),format.raw/*29.86*/(""">
    """),_display_(/*30.6*/if(viewValue != null && !viewValue.isEmpty)/*30.49*/ {_display_(Seq[Any](format.raw/*30.51*/("""
        """),_display_(/*31.10*/for(tagName <- viewValue.split(",")) yield /*31.46*/ {_display_(Seq[Any](format.raw/*31.48*/("""
            """),format.raw/*32.13*/("""<option value=""""),_display_(/*32.29*/tagName),format.raw/*32.36*/("""" selected>"""),_display_(/*32.48*/tagName),format.raw/*32.55*/("""</option>
        """)))}),format.raw/*33.10*/("""
    """)))}),format.raw/*34.6*/("""
    """),format.raw/*35.5*/("""</x-multiselect>
""")))}),format.raw/*36.2*/("""
"""))
      }
    }
  }

  def render(element:na.catalog.basemodule.ui.MultiSelect): play.twirl.api.HtmlFormat.Appendable = apply(element)

  def f:((na.catalog.basemodule.ui.MultiSelect) => play.twirl.api.HtmlFormat.Appendable) = (element) => apply(element)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/catalog-basemodule/target/TwirlSource/na/catalog/basemodule/views/components/tags.scala.html
                  HASH: 2ac8cb5f995f1a7889d24040c0db566cdc4deb20
                  MATRIX: 1010->1|1130->51|1176->91|1226->135|1277->176|1296->187|1410->290|1428->299|1509->48|1537->174|1565->287|1594->350|1623->353|1694->415|1734->417|1766->422|1804->433|1820->440|1847->446|1879->461|1934->507|1974->509|2006->514|2055->536|2071->543|2098->549|2132->557|2184->593|2224->595|2260->604|2295->612|2323->619|2367->633|2399->638|2437->659|2494->707|2533->708|2565->713|2608->729|2630->742|2703->794|2740->814|2752->819|2790->820|2822->825|2865->841|2887->854|2960->906|2993->913|3045->956|3085->958|3122->968|3174->1004|3214->1006|3255->1019|3298->1035|3326->1042|3365->1054|3393->1061|3443->1080|3479->1086|3511->1091|3559->1109
                  LINES: 28->1|31->3|32->4|33->5|35->7|35->7|39->13|39->13|42->1|44->6|45->11|47->15|49->17|49->17|49->17|50->18|50->18|50->18|50->18|52->20|52->20|52->20|53->21|53->21|53->21|53->21|54->22|54->22|54->22|55->23|55->23|55->23|56->24|57->25|58->26|58->26|58->26|59->27|59->27|59->27|59->27|60->28|60->28|60->28|61->29|61->29|61->29|61->29|62->30|62->30|62->30|63->31|63->31|63->31|64->32|64->32|64->32|64->32|64->32|65->33|66->34|67->35|68->36
                  -- GENERATED --
              */
          