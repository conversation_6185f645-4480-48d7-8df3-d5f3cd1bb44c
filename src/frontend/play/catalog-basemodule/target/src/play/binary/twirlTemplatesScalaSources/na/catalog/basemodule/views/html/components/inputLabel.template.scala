
package na.catalog.basemodule.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputLabel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.catalog.basemodule.ui.Label,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(element: na.catalog.basemodule.ui.Label):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.43*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<label
    """),_display_(/*6.6*/if(element.getClasses.size > 0)/*6.37*/ {_display_(Seq[Any](format.raw/*6.39*/("""
        """),format.raw/*7.9*/("""class=""""),_display_(/*7.17*/element/*7.24*/.getClasses.mkString(" ")),format.raw/*7.49*/(""""
    """)))}),format.raw/*8.6*/("""
    """),_display_(/*9.6*/if(element.getTargetId != null)/*9.37*/ {_display_(Seq[Any](format.raw/*9.39*/("""
        """),format.raw/*10.9*/("""for=""""),_display_(/*10.15*/element/*10.22*/.getTargetId),format.raw/*10.34*/(""""
    """)))}),format.raw/*11.6*/("""
    """),_display_(/*12.6*/if(element.getDataAttributes != null)/*12.43*/ {_display_(Seq[Any](format.raw/*12.45*/("""
        """),_display_(/*13.10*/TemplateUtils/*13.23*/.dynamicElementsAttributes(element.getDataAttributes)),format.raw/*13.76*/("""
    """)))}),format.raw/*14.6*/("""
"""),format.raw/*15.1*/(""">"""),_display_(/*15.3*/element/*15.10*/.getI18n.getTranslation),format.raw/*15.33*/("""</label>"""))
      }
    }
  }

  def render(element:na.catalog.basemodule.ui.Label): play.twirl.api.HtmlFormat.Appendable = apply(element)

  def f:((na.catalog.basemodule.ui.Label) => play.twirl.api.HtmlFormat.Appendable) = (element) => apply(element)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:21 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/catalog-basemodule/target/TwirlSource/na/catalog/basemodule/views/components/inputLabel.scala.html
                  HASH: 5a8b58c2e4d3a314e8f73066d0c57f348c19319b
                  MATRIX: 1010->1|1124->45|1196->42|1224->88|1251->89|1288->101|1327->132|1366->134|1401->143|1435->151|1450->158|1495->183|1531->190|1562->196|1601->227|1640->229|1676->238|1709->244|1725->251|1758->263|1795->270|1827->276|1873->313|1913->315|1950->325|1972->338|2046->391|2082->397|2110->398|2138->400|2154->407|2198->430
                  LINES: 28->1|31->3|34->1|36->4|37->5|38->6|38->6|38->6|39->7|39->7|39->7|39->7|40->8|41->9|41->9|41->9|42->10|42->10|42->10|42->10|43->11|44->12|44->12|44->12|45->13|45->13|45->13|46->14|47->15|47->15|47->15|47->15
                  -- GENERATED --
              */
          