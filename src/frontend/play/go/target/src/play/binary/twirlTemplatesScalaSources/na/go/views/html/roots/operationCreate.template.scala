
package na.go.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.utils.SecurityUtils
/*2.2*/import na.go.settings.AAAPIResources
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.go.views.html.skeletons.mainSkel
/*5.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family
/*6.2*/import na.go.models.datatables.operations.ExtendedOperation
/*7.2*/import na.go.views.html.components.common.lateralNav
/*8.2*/import na.go.views.html.components.operationcreate.operationWizard
/*9.2*/import na.go.settings.GoConstants.NavContext

object operationCreate extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[List[Family],String,ExtendedOperation,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(groups: List[Family], tab: String, operationToClone: ExtendedOperation):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*14.2*/isAdmin/*14.9*/ = {{SecurityUtils.isAuthorized(AAAPIResources.genericOperationsAdmin().R())}};
Seq[Any](format.raw/*11.74*/("""


"""),format.raw/*14.86*/("""

"""),_display_(/*16.2*/mainSkel()/*16.12*/{_display_(Seq[Any](format.raw/*16.13*/("""
	"""),format.raw/*17.2*/("""<div class="fx-full-height """),_display_(/*17.30*/if(isAdmin)/*17.41*/{_display_(Seq[Any](format.raw/*17.42*/("""layout--page-with-navbar""")))}),format.raw/*17.67*/(""""
		 data-na-portal-go-create-operation-breadcrumb>
		"""),_display_(/*19.4*/if(isAdmin)/*19.15*/{_display_(_display_(/*19.17*/lateralNav/*19.27*/.render(NavContext.OPERATIONS)))}),format.raw/*19.58*/("""
		"""),format.raw/*20.3*/("""<div class="fx-main-content-wrapper fx-full-height go-wizard-page">
			<div class="fx-entity-header" data-template-loader data-context="operation">
				<div class="fx-entity-header-info">
					<span class="fx-entity-header-icon">
						<i class="fuxicons fuxicons-go"></i>
					</span>
					<div class="fx-entity-header-title">
						<h1>
						"""),_display_(/*28.8*/i18n("na.portal.go.operation.create.title")),format.raw/*28.51*/("""
						"""),format.raw/*29.7*/("""</h1>
					</div>
				</div>
			</div>
			<div class="fx-entity-info" data-na-portal-go-common>
				<x-tab-container>
					<x-tab label=""""),_display_(/*35.21*/i18n("na.portal.go.operation.details.characteristics")),format.raw/*35.75*/("""">
						"""),_display_(/*36.8*/operationWizard/*36.23*/.render(groups, operationToClone)),format.raw/*36.56*/("""
						"""),format.raw/*37.7*/("""<div data-na-portal-go-dynamic-loading-bar></div>
					</x-tab>
				</x-tab-container>
			</div>
		</div>
	</div>
""")))}),format.raw/*43.2*/("""
"""))
      }
    }
  }

  def render(groups:List[Family],tab:String,operationToClone:ExtendedOperation): play.twirl.api.HtmlFormat.Appendable = apply(groups,tab,operationToClone)

  def f:((List[Family],String,ExtendedOperation) => play.twirl.api.HtmlFormat.Appendable) = (groups,tab,operationToClone) => apply(groups,tab,operationToClone)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/roots/operationCreate.scala.html
                  HASH: 245cbec7352da6879971b6784732399c28fba0b3
                  MATRIX: 663->1|713->45|757->83|803->123|853->167|932->240|999->301|1059->355|1133->423|1524->470|1675->546|1690->553|1798->542|1829->630|1858->633|1877->643|1916->644|1945->646|2000->674|2020->685|2059->686|2115->711|2196->766|2216->777|2246->779|2265->789|2319->820|2349->823|2721->1169|2785->1212|2819->1219|2984->1357|3059->1411|3095->1421|3119->1436|3173->1469|3207->1476|3352->1591
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|37->11|41->14|41->14|42->11|45->14|47->16|47->16|47->16|48->17|48->17|48->17|48->17|48->17|50->19|50->19|50->19|50->19|50->19|51->20|59->28|59->28|60->29|66->35|66->35|67->36|67->36|67->36|68->37|74->43
                  -- GENERATED --
              */
          