
package na.go.views.html.components.operationcreate

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping
/*2.2*/import play.libs.Json;

object configureOptionalFields extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[List[Mapping],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*4.2*/(optionalMappings: List[Mapping]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*4.35*/("""

"""),_display_(/*6.2*/if(!optionalMappings.isEmpty)/*6.31*/{_display_(Seq[Any](format.raw/*6.32*/("""
    """),format.raw/*7.5*/("""<div class="pull-right go-select-optional-fields " id="operation-optional-fields" data-na-portal-go-configure-optional-fields data-na-portal-go-optional-mappings=""""),_display_(/*7.169*/Json/*7.173*/.toJson(optionalMappings)),format.raw/*7.198*/("""">
    </div>
""")))}),format.raw/*9.2*/("""
"""))
      }
    }
  }

  def render(optionalMappings:List[Mapping]): play.twirl.api.HtmlFormat.Appendable = apply(optionalMappings)

  def f:((List[Mapping]) => play.twirl.api.HtmlFormat.Appendable) = (optionalMappings) => apply(optionalMappings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/operationcreate/configureOptionalFields.scala.html
                  HASH: c42bf722d67932193a6c1ab4b8ab434613c9d657
                  MATRIX: 684->1|764->75|1116->100|1244->133|1272->136|1309->165|1347->166|1378->171|1569->335|1582->339|1628->364|1672->379
                  LINES: 24->1|25->2|30->4|35->4|37->6|37->6|37->6|38->7|38->7|38->7|38->7|40->9
                  -- GENERATED --
              */
          