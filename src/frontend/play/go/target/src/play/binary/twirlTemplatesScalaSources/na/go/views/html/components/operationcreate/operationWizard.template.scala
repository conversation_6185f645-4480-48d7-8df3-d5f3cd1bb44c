
package na.go.views.html.components.operationcreate

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.naportalbase.views.tags.wizardStep
/*3.2*/import na.naportalbase.utils.TemplateUtils
/*4.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family
/*5.2*/import na.go.models.datatables.operations.ExtendedOperation
/*6.2*/import na.go.views.html.components.operationcreate
/*7.2*/import play.libs.Json

object operationWizard extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[List[Family],ExtendedOperation,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(groups: List[Family], operationToClone: ExtendedOperation):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*11.2*/step2Content/*11.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*11.18*/("""
    """),format.raw/*12.5*/("""<div data-na-portal-wizard-template-loader></div>
""")))};def /*15.2*/step1/*15.7*/ = {{
    wizardStep(stepName = "characteristics",
    stepLabel = i18n("na.portal.go.operation.create.tab.characteristics.label")){
        operationcreate.characteristicsTab("createCtrl.characteristics", groups)
    }
}};def /*21.2*/step2/*21.7*/ = {{
    wizardStep(stepName = "operation",
    stepLabel = i18n("na.portal.go.operation.create.tab.operation.label")){
        step2Content
    }
}};def /*28.2*/wizardAttributes/*28.18*/ = {{
    var map = new java.util.HashMap[String, String]()
    map.put("data-na-portal-go-wizard-context", "operation")
    map.put("data-ng-controller", "NaPortalGoCreateOperationController as createCtrl")
    if(operationToClone != null){
        map.put("data-na-portal-go-wizard-initial-tab", "operation")
        map.put("data-na-portal-go-clone-operation", Json.toJson(operationToClone).toString)
    } else {
        map.put("data-na-portal-go-wizard-initial-tab", "characteristics")
    }
    map
}};
Seq[Any](format.raw/*9.61*/("""

"""),format.raw/*13.2*/("""

"""),format.raw/*20.2*/("""
"""),format.raw/*26.2*/("""

"""),format.raw/*39.2*/("""

"""),format.raw/*41.1*/("""<div class="tab-pane active" id="tab-car" """),_display_(/*41.44*/TemplateUtils/*41.57*/.dynamicElementsAttributes(wizardAttributes)),format.raw/*41.101*/(""">
    <x-wizard class="create-operation-wizard">
    """),_display_(/*43.6*/if(operationToClone != null)/*43.34*/{_display_(Seq[Any](format.raw/*43.35*/("""
        """),format.raw/*44.9*/("""<x-wizard-step class="opened" data-tab-name=""""),_display_(/*44.55*/i18n("na.portal.go.operation.create.tab.characteristics.label")),format.raw/*44.118*/("""">"""),_display_(/*44.121*/step1/*44.126*/.content),format.raw/*44.134*/("""</x-wizard-step>
        <x-wizard-step class="opened open" data-tab-name=""""),_display_(/*45.60*/i18n("na.portal.go.operation.create.tab.operation.label")),format.raw/*45.117*/("""" ></x-wizard-step>
    """)))}/*46.7*/else/*46.12*/{_display_(Seq[Any](format.raw/*46.13*/("""
        """),format.raw/*47.9*/("""<x-wizard-step data-tab-name=""""),_display_(/*47.40*/i18n("na.portal.go.operation.create.tab.characteristics.label")),format.raw/*47.103*/("""">"""),_display_(/*47.106*/step1/*47.111*/.content),format.raw/*47.119*/("""</x-wizard-step>
        <x-wizard-step data-tab-name=""""),_display_(/*48.40*/i18n("na.portal.go.operation.create.tab.operation.label")),format.raw/*48.97*/(""""></x-wizard-step>
    """)))}),format.raw/*49.6*/("""
    """),format.raw/*50.5*/("""</x-wizard>
</div>
"""))
      }
    }
  }

  def render(groups:List[Family],operationToClone:ExtendedOperation): play.twirl.api.HtmlFormat.Appendable = apply(groups,operationToClone)

  def f:((List[Family],ExtendedOperation) => play.twirl.api.HtmlFormat.Appendable) = (groups,operationToClone) => apply(groups,operationToClone)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/operationcreate/operationWizard.scala.html
                  HASH: cbbdd147a59b985b9ffeb3f910c258339ac03678
                  MATRIX: 684->1|730->41|782->87|832->131|911->204|978->265|1036->317|1396->341|1534->403|1555->415|1636->419|1668->424|1742->478|1755->483|1990->706|2003->711|2166->863|2191->879|2728->400|2757->475|2786->704|2814->860|2843->1386|2872->1388|2942->1431|2964->1444|3030->1488|3110->1542|3147->1570|3186->1571|3222->1580|3295->1626|3380->1689|3411->1692|3426->1697|3456->1705|3559->1781|3638->1838|3681->1864|3694->1869|3733->1870|3769->1879|3827->1910|3912->1973|3943->1976|3958->1981|3988->1989|4071->2045|4149->2102|4203->2126|4235->2131
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|35->9|39->11|39->11|41->11|42->12|43->15|43->15|48->21|48->21|53->28|53->28|65->9|67->13|69->20|70->26|72->39|74->41|74->41|74->41|74->41|76->43|76->43|76->43|77->44|77->44|77->44|77->44|77->44|77->44|78->45|78->45|79->46|79->46|79->46|80->47|80->47|80->47|80->47|80->47|80->47|81->48|81->48|82->49|83->50
                  -- GENERATED --
              */
          