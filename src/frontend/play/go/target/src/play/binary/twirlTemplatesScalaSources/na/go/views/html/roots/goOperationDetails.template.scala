
package na.go.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import play.api.libs.json._
/*3.2*/import na.go.views.html.skeletons.mainSkel
/*4.2*/import na.go.settings.AAAPIResources
/*5.2*/import na.naportalbase.utils.SecurityUtils
/*6.2*/import na.go.settings.GoConstants.NavContext
/*7.2*/import na.go.views.html.components.common.lateralNav
/*8.2*/import na.go.views.html.components.operationdetails.operationDetailsCharacteristicsTab

object goOperationDetails extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[String,String,String,String,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(orderKey: String,operation: String,rfs: String,jsonDetails: String,tab:String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*12.2*/parsedJsonDetails/*12.19*/ = {{
	val json = Json.parse(jsonDetails)
	json
}};def /*17.2*/isAdmin/*17.9*/ = {{SecurityUtils.isAuthorized(AAAPIResources.genericOperationsAdmin().R())}};
Seq[Any](format.raw/*10.81*/("""

"""),format.raw/*15.2*/("""

"""),format.raw/*17.86*/("""

"""),_display_(/*19.2*/mainSkel()/*19.12*/ {_display_(Seq[Any](format.raw/*19.14*/("""
"""),format.raw/*20.1*/("""<div class="layout--page-with-navbar">

	"""),_display_(/*22.3*/if(isAdmin)/*22.14*/{_display_(Seq[Any](format.raw/*22.15*/("""
		"""),_display_(/*23.4*/lateralNav/*23.14*/.render(NavContext.OPERATIONS)),format.raw/*23.44*/("""
	""")))}),format.raw/*24.3*/("""
	"""),format.raw/*25.2*/("""<div class="page__content--operation-details-view fx-main-content-wrapper fx-with-main-nav" data-order-key=""""),_display_(/*25.111*/orderKey),format.raw/*25.119*/("""" data-na-portal-go-operation-details-breadcrumb>

		<div class="fx-entity-header">
			<div class="fx-entity-header-info">
				<span class="fx-entity-header-icon">
					<i class="fuxicons fuxicons-go"></i>
				</span>
				<div class="fx-entity-header-title">
					<h1>
						"""),_display_(/*34.8*/orderKey),format.raw/*34.16*/("""
					"""),format.raw/*35.6*/("""</h1>
					<p class="fx-entity-header-details">
						<span class="fx-entity-header-details--label">"""),_display_(/*37.54*/i18n("na.portal.go.operation.details.label.operation")),format.raw/*37.108*/(""":&nbsp</span>
						<span is="x-dynamic-span" data-truncated-at="middle">"""),_display_(/*38.61*/operation),format.raw/*38.70*/("""</span>
						<span class="fx-entity-header-details--label">&nbsp/&nbsp"""),_display_(/*39.65*/i18n("na.portal.go.operation.details.label.rfs")),format.raw/*39.113*/(""":&nbsp</span>
						<span is="x-dynamic-span" data-truncated-at="middle">"""),_display_(/*40.61*/rfs),format.raw/*40.64*/("""</span>
					</p>
				</div>
			</div>
		</div>

		<div class="container--entity-content">
			<x-tab-container>
				<x-tab label=""""),_display_(/*48.20*/i18n("na.portal.go.operation.create.tab.characteristics.label")),format.raw/*48.83*/("""">
					<x-shadow-scroll flex>
						<div class="tab-pane fx-form-vertical active" data-formatted-details-json=""""),_display_(/*50.83*/parsedJsonDetails),format.raw/*50.100*/("""" data-na-portal-go-details-context data-context="operation">
							"""),_display_(/*51.9*/operationDetailsCharacteristicsTab/*51.43*/.render()),format.raw/*51.52*/("""
						"""),format.raw/*52.7*/("""</div>
					</x-shadow-scroll>
				</x-tab>
			</x-tab-container>
		</div>
	</div>
</div>
""")))}),format.raw/*59.2*/("""




"""))
      }
    }
  }

  def render(orderKey:String,operation:String,rfs:String,jsonDetails:String,tab:String): play.twirl.api.HtmlFormat.Appendable = apply(orderKey,operation,rfs,jsonDetails,tab)

  def f:((String,String,String,String,String) => play.twirl.api.HtmlFormat.Appendable) = (orderKey,operation,rfs,jsonDetails,tab) => apply(orderKey,operation,rfs,jsonDetails,tab)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/roots/goOperationDetails.scala.html
                  HASH: 924b21fc4e9bcfde068760b7c23f0ce75545af44
                  MATRIX: 663->1|709->42|744->72|794->117|838->156|888->201|940->248|1000->303|1433->394|1591->478|1617->495|1683->552|1698->559|1806->473|1837->547|1869->636|1900->641|1919->651|1959->653|1988->655|2058->699|2078->710|2117->711|2148->716|2167->726|2218->756|2252->760|2282->763|2419->872|2449->880|2760->1165|2789->1173|2823->1180|2953->1283|3029->1337|3131->1412|3161->1421|3261->1494|3331->1542|3433->1617|3457->1620|3623->1759|3707->1822|3849->1937|3888->1954|3985->2025|4028->2059|4058->2068|4093->2076|4221->2174
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|36->10|40->12|40->12|43->17|43->17|44->10|46->15|48->17|50->19|50->19|50->19|51->20|53->22|53->22|53->22|54->23|54->23|54->23|55->24|56->25|56->25|56->25|65->34|65->34|66->35|68->37|68->37|69->38|69->38|70->39|70->39|71->40|71->40|79->48|79->48|81->50|81->50|82->51|82->51|82->51|83->52|90->59
                  -- GENERATED --
              */
          