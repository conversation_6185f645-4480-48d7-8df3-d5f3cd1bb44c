
package na.go.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.go.views.html.skeletons.mainSkel
/*3.2*/import na.go.settings.GoConstants.NavContext
/*4.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family
/*5.2*/import na.go.views.html.components.common.lateralNav
/*6.2*/import na.go.views.html.components.admintemplatecreate.operationWizard

object adminTemplateCreate extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[List[Family],String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(groups: List[Family], tab: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*8.37*/("""

"""),_display_(/*10.2*/mainSkel()/*10.12*/ {_display_(Seq[Any](format.raw/*10.14*/("""
	"""),format.raw/*11.2*/("""<div class="layout--page-with-navbar" data-na-portal-go-admin-add-template-breadcrumb>
		"""),_display_(/*12.4*/lateralNav/*12.14*/.render(NavContext.ADMIN_TEMPLATES)),format.raw/*12.49*/("""
		"""),format.raw/*13.3*/("""<div class="fx-main-content-wrapper go-wizard-page">
			<div class="fx-entity-header" data-template-loader data-context="template">
				<div class="fx-entity-header-info">
					<span class="fx-entity-header-icon">
						<i class="fuxicons fuxicons-go-admin"></i>
					</span>
					<div class="fx-entity-header-title">
						<h1>
						"""),_display_(/*21.8*/i18n("na.portal.go.admin.operation.create.title")),format.raw/*21.57*/("""
						"""),format.raw/*22.7*/("""</h1>
					</div>
				</div>
			</div>
			<div class="fx-entity-info" data-na-portal-go-common>
				<x-tab-container>
					<x-tab label=""""),_display_(/*28.21*/i18n("na.portal.go.operation.details.characteristics")),format.raw/*28.75*/("""">
					"""),_display_(/*29.7*/operationWizard/*29.22*/.render(groups, null, "create-template")),format.raw/*29.62*/("""
					"""),format.raw/*30.6*/("""</x-tab>
				</x-tab-container>
			</div>
		</div>
	</div>
""")))}))
      }
    }
  }

  def render(groups:List[Family],tab:String): play.twirl.api.HtmlFormat.Appendable = apply(groups,tab)

  def f:((List[Family],String) => play.twirl.api.HtmlFormat.Appendable) = (groups,tab) => apply(groups,tab)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/roots/adminTemplateCreate.scala.html
                  HASH: 492d6fc0bd3acda68e144f9d357d36b0e8dd9669
                  MATRIX: 663->1|709->41|759->85|811->131|890->204|950->258|1352->331|1482->366|1511->369|1530->379|1570->381|1599->383|1715->473|1734->483|1790->518|1820->521|2182->857|2252->906|2286->913|2451->1051|2526->1105|2561->1114|2585->1129|2646->1169|2679->1175
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|39->8|41->10|41->10|41->10|42->11|43->12|43->12|43->12|44->13|52->21|52->21|53->22|59->28|59->28|60->29|60->29|60->29|61->30
                  -- GENERATED --
              */
          