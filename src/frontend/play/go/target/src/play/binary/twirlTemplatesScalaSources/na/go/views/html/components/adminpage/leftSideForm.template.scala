
package na.go.views.html.components.adminpage

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import scala.collection.immutable
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.go.settings.GoConstants
/*5.2*/import na.go.controllers.routes.Search
/*6.2*/import na.naportalbase.views.html.components.selectsComp.selectInputComp
/*7.2*/import na.naportalbase.views.html.components.buttonsComp.caretDropdown
/*8.2*/import na.go.views.html.components.adminpage.operationForm
/*9.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
/*10.2*/import na.naportalbase.views.html.components.splitterComp
/*11.2*/import na.go.views.html.components.common.saveFilterButton

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[na.go.models.general.OperCatFamilyList,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*13.2*/(groups : na.go.models.general.OperCatFamilyList):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*65.2*/headerContent/*65.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*65.19*/("""


""")))};def /*94.2*/formButtons/*94.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*94.17*/("""
    """),_display_(/*95.6*/leftSideFormInputButtonsComp/*95.34*/.render(searchButtonAttributes,clearButtonAttributes)),format.raw/*95.87*/("""
""")))};def /*98.2*/renderedButtons/*98.17*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*98.21*/("""
    """),_display_(/*99.6*/splitterComp/*99.18*/.sidebarButtonsComp(formButtons)),format.raw/*99.50*/("""
""")))};def /*102.2*/renderedSaveFilterButton/*102.26*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*102.30*/("""
    """),_display_(/*103.6*/saveFilterButton/*103.22*/.render("templateCtrl.openSaveFilterModal()")),format.raw/*103.67*/("""
""")))};def /*15.2*/caretTitle/*15.12*/ = {{i18n("na.buttons.caret.moreoptions")}};def /*16.2*/caretOptions/*16.14*/ = {{
    var map = mutable.LinkedHashMap[String,mutable.HashMap[String,mutable.HashMap[String,String]]]()

    var firstOptionTitle = i18n("na.modal.filters.title.editfilter")
    var secondOptionTitle = i18n("na.modal.filters.title.deletefilter")

    var firstOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var firstOptionAattributes = mutable.HashMap[String,String]()
    firstOptionAattributes.put("id","editFilterButton")
    firstOptionAattributes.put("data-ng-click","templateCtrl.openEditFilterModal()")

    var firstOptionIattributes = mutable.HashMap[String,String]()
    firstOptionIattributes.put("class","glyphicon glyphicon-pencil")

    firstOptionComponents.put("a",firstOptionAattributes)
    firstOptionComponents.put("i",firstOptionIattributes)

    var secondOptionComponents = mutable.HashMap[String,mutable.HashMap[String,String]]()

    var secondOptionAattributes = mutable.HashMap[String,String]()
    secondOptionAattributes.put("id","deleteFilterButton")
    secondOptionAattributes.put("data-ng-click","templateCtrl.openDeleteFilterModal()")

    var secondOptionIattributes = mutable.HashMap[String,String]()
    secondOptionIattributes.put("class","glyphicon glyphicon-remove")

    secondOptionComponents.put("a",secondOptionAattributes)
    secondOptionComponents.put("i",secondOptionIattributes)

    map.put(firstOptionTitle,firstOptionComponents)
    map.put(secondOptionTitle,secondOptionComponents)

    map
}};def /*51.2*/selectAttributes/*51.18*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("class","input-sm fx-filter-input select2-offscreen")
    map.put("data-ng-model", "selectedFilter")
    map.put("name", "filters")
    map.put("data-na-portal-select-box",null)
    map.put("value", "")
    map.put("id", "rfsFilters")
    map.put("data-is-dynamic",null)
    map.put("data-context","filters")
    map.put("data-url",""+Search.getFilterNamesForSelect(GoConstants.TEMPLATE_SEARCH_CONTEXT))
    map
}};def /*70.2*/form/*70.6*/ = {{
     operationForm.render("searchFormItems", immutable.HashMap[String,String](), groups, false)
}};def /*74.2*/searchButtonAttributes/*74.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "templateCtrl.submitSearchForm()")
    map.put("id", "searchButton")
    map.put("type","submit")
    map.put("value",i18n("na.buttons.search"))

    map
}};def /*84.2*/clearButtonAttributes/*84.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "templateCtrl.clearSearchFormItems()")
    map.put("id", "clearSearchButton")
    map.put("type","reset")
    map.put("value",i18n("na.buttons.clear"))

    map
}};
Seq[Any](format.raw/*13.51*/("""

"""),format.raw/*15.54*/("""
"""),format.raw/*50.2*/("""
"""),format.raw/*63.2*/("""

"""),format.raw/*68.2*/("""

"""),format.raw/*72.2*/("""

"""),format.raw/*82.2*/("""

"""),format.raw/*92.2*/("""

"""),format.raw/*96.2*/("""

"""),format.raw/*100.2*/("""

"""),format.raw/*104.2*/("""


"""),format.raw/*107.1*/("""<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.general.filters"></x-i18n>
        </p>
    </div>
    <div class="form-group search-form--filter-select">
        <div class="search-form__selectbox">
            """),_display_(/*118.14*/selectInputComp/*118.29*/.render(selectAttributes)),format.raw/*118.54*/("""
        """),format.raw/*119.9*/("""</div>
        """),_display_(/*120.10*/caretDropdown/*120.23*/.render(caretTitle,caretOptions)),format.raw/*120.55*/("""
    """),format.raw/*121.5*/("""</div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-shadow-scroll vertical>
    """),_display_(/*125.6*/form),format.raw/*125.10*/("""
    """),format.raw/*126.5*/("""</x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
    <div class="fx-splitter-sidebar-buttons">
        <div class="fx-splitter-sidebar-buttons-inner">
        """),_display_(/*131.10*/leftSideFormInputButtonsComp/*131.38*/.render(searchButtonAttributes,clearButtonAttributes)),format.raw/*131.91*/("""
        """),format.raw/*132.9*/("""</div>
    </div>
</div>

"""))
      }
    }
  }

  def render(groups:na.go.models.general.OperCatFamilyList): play.twirl.api.HtmlFormat.Appendable = apply(groups)

  def f:((na.go.models.general.OperCatFamilyList) => play.twirl.api.HtmlFormat.Appendable) = (groups) => apply(groups)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/adminpage/leftSideForm.scala.html
                  HASH: 9b6ecd3c48a5c7c9dd153fcd66693ab648c83d7f
                  MATRIX: 678->1|717->34|758->69|804->109|845->144|891->184|971->258|1049->330|1115->390|1209->477|1275->536|1678->597|1806->2697|1828->2710|1909->2714|1936->3363|1956->3374|2037->3378|2069->3384|2106->3412|2180->3465|2205->3470|2229->3485|2310->3489|2342->3495|2363->3507|2416->3539|2442->3544|2476->3568|2558->3572|2591->3578|2617->3594|2684->3639|2709->649|2728->659|2784->703|2805->715|4301->2199|4326->2215|4819->2721|4831->2725|4948->2831|4979->2853|5231->3094|5261->3115|5537->646|5567->701|5595->2197|5623->2694|5652->2718|5681->2828|5710->3091|5739->3360|5768->3467|5798->3541|5828->3641|5859->3644|6306->4063|6331->4078|6378->4103|6415->4112|6459->4128|6482->4141|6536->4173|6569->4178|6717->4299|6743->4303|6776->4308|6994->4498|7032->4526|7107->4579|7144->4588
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|34->11|39->13|43->65|43->65|45->65|48->94|48->94|50->94|51->95|51->95|51->95|52->98|52->98|54->98|55->99|55->99|55->99|56->102|56->102|58->102|59->103|59->103|59->103|60->15|60->15|60->16|60->16|94->51|94->51|106->70|106->70|108->74|108->74|116->84|116->84|125->13|127->15|128->50|129->63|131->68|133->72|135->82|137->92|139->96|141->100|143->104|146->107|157->118|157->118|157->118|158->119|159->120|159->120|159->120|160->121|164->125|164->125|165->126|170->131|170->131|170->131|171->132
                  -- GENERATED --
              */
          