
package na.go.views.html.components.dynamicForm.fields

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import na.naportalbase.settings.BaseMappings
/*3.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.TextArea
/*4.2*/import na.naportalbase.views.html.components.inputsComp.inputTextAreaComp
/*5.2*/import na.naportalbase.views.tags.i18n

object textArea extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(mapping: pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/textAreaComponent/*9.19*/ = {{mapping.getComponent.asInstanceOf[TextArea]}};def /*10.2*/inputLabel/*10.12*/ = {{mapping.getLabel}};def /*11.2*/inputId/*11.9*/ = {{mapping.getTemplateMapping}};def /*12.2*/placeHolder/*12.13*/ = {{mapping.getLabel}};def /*13.2*/inputValue/*13.12*/ = {{ mapping.getComponent.getDefaultValue }};def /*14.2*/isRequired/*14.12*/ = {{textAreaComponent.getRequired}};def /*15.2*/inputAttributes/*15.17*/ = {{
    var map = mutable.HashMap[String, String](
        "data-ng-model" -> ("model[\""+inputId+"\"]"),
        "data-model" -> mapping.getTemplateMapping,
        "data-na-portal-go-dynamic-form-text-area" -> null,
        "name" -> inputId,
        "value" -> "",
        "rows" -> "3",
        "id" -> inputId,
        "data-component" -> BaseMappings.dualMapper.valueToTree(textAreaComponent),
        "class" -> "form-control input-sm pull-left"
    )

    if(textAreaComponent.getRegex){
        map.put("data-na-portal-regex-error",null)
        map.put("regex", textAreaComponent.getRegex)
        map.put("notification-message", textAreaComponent.getRegexErrorMessage)
    }
    if(textAreaComponent.getMaxSize != null){
        map.put("maxlength", String.valueOf(textAreaComponent.getMaxSize))
    }
    if(textAreaComponent.getHeight != null){
        map.put("rows", String.valueOf(textAreaComponent.getHeight))
    }
    if(textAreaComponent.getDefaultValue != null){
        map.put("value", textAreaComponent.getDefaultValue)
    }
    if(isRequired){
        map.put("data-na-portal-required", null)
    }
    if(!textAreaComponent.getEditable){
        map.put("disabled", null)
    }

    map
}};
Seq[Any](format.raw/*7.78*/("""

"""),format.raw/*9.68*/("""
"""),format.raw/*10.34*/("""
"""),format.raw/*11.41*/("""
"""),format.raw/*12.35*/("""
"""),format.raw/*13.56*/("""
"""),format.raw/*14.47*/("""
"""),format.raw/*50.2*/("""

"""),format.raw/*52.1*/("""<div class="form-group">
    <label for=""""),_display_(/*53.18*/inputId),format.raw/*53.25*/("""" class="col-sm-2 control-label """),_display_(/*53.58*/if(isRequired)/*53.72*/{_display_(Seq[Any](format.raw/*53.73*/("""fx-required""")))}),format.raw/*53.85*/("""" title=""""),_display_(/*53.95*/mapping/*53.102*/.getDescription),format.raw/*53.117*/("""">
        """),_display_(/*54.10*/inputLabel),_display_(/*54.21*/if(isRequired)/*54.35*/{_display_(Seq[Any](format.raw/*54.36*/("""<abbr title=""""),_display_(/*54.50*/i18n("na.basemodule.validations.mandatory")),format.raw/*54.93*/(""""></abbr>""")))}),format.raw/*54.103*/("""
    """),format.raw/*55.5*/("""</label>
    <div class="col-sm-10" data-na-portal-field-notification data-na-portal-button-notification>
    """),_display_(/*57.6*/inputTextAreaComp(inputId,inputAttributes,inputValue)),format.raw/*57.59*/("""
    """),format.raw/*58.5*/("""</div>
</div>"""))
      }
    }
  }

  def render(mapping:pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping): play.twirl.api.HtmlFormat.Appendable = apply(mapping)

  def f:((pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping) => play.twirl.api.HtmlFormat.Appendable) = (mapping) => apply(mapping)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/dynamicForm/fields/textArea.scala.html
                  HASH: ee3bfcc683148288803bc109e83f6f0fb1793465
                  MATRIX: 687->1|726->34|778->80|870->166|951->241|1356->282|1510->361|1535->378|1598->429|1617->439|1653->463|1668->470|1714->504|1734->515|1770->539|1789->549|1847->595|1866->605|1915->642|1939->657|3186->358|3215->427|3244->461|3273->502|3302->537|3331->593|3360->640|3388->1874|3417->1876|3486->1918|3514->1925|3574->1958|3597->1972|3636->1973|3679->1985|3716->1995|3733->2002|3770->2017|3809->2029|3840->2040|3863->2054|3902->2055|3943->2069|4007->2112|4049->2122|4081->2127|4218->2238|4292->2291|4324->2296
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->9|37->9|37->10|37->10|37->11|37->11|37->12|37->12|37->13|37->13|37->14|37->14|37->15|37->15|73->7|75->9|76->10|77->11|78->12|79->13|80->14|81->50|83->52|84->53|84->53|84->53|84->53|84->53|84->53|84->53|84->53|84->53|85->54|85->54|85->54|85->54|85->54|85->54|85->54|86->55|88->57|88->57|89->58
                  -- GENERATED --
              */
          