
package na.go.views.html.components.common

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import scala.collection.mutable
/*3.2*/import na.naportalbase.views.html.components.buttonsComp.buttonComp

object saveFilterButton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(ngClick: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/saveFilterButtonAttributes/*7.28*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", ngClick)
    map.put("id", "openSaveFilter")
    map.put("class","btn btn-default btn-sm")
    map.put("type","button")
    map
}};def /*15.2*/saveFilterButtoniAttributes/*15.29*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("class","fuxicons fuxicons-floppy-disk")

    map
}};def /*21.2*/saveFilterButtonValue/*21.23*/ = {{i18n("na.modal.button.savefilter")}};
Seq[Any](format.raw/*5.19*/("""

"""),format.raw/*14.2*/("""
"""),format.raw/*20.2*/("""
"""),format.raw/*21.63*/("""
"""),_display_(/*22.2*/buttonComp/*22.12*/.render(saveFilterButtonValue,saveFilterButtonAttributes,saveFilterButtoniAttributes)),format.raw/*22.97*/("""

"""))
      }
    }
  }

  def render(ngClick:String): play.twirl.api.HtmlFormat.Appendable = apply(ngClick)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (ngClick) => apply(ngClick)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/common/saveFilterButton.scala.html
                  HASH: 473d6f29ee7ae6eebfdcbbd0a11e1d7656e9d133
                  MATRIX: 675->1|721->41|760->74|1143->144|1238->164|1272->190|1498->404|1534->431|1664->549|1694->570|1764->161|1793->402|1821->547|1850->610|1878->612|1897->622|2003->707
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|42->15|42->15|47->21|47->21|48->5|50->14|51->20|52->21|53->22|53->22|53->22
                  -- GENERATED --
              */
          