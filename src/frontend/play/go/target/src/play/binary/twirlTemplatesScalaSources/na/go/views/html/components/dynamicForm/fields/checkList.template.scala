
package na.go.views.html.components.dynamicForm.fields

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.settings.BaseMappings
/*2.2*/import scala.collection.mutable
/*3.2*/import scala.collection.immutable
/*4.2*/import com.google.common.base.Splitter
/*5.2*/import com.google.common.collect.Sets
/*6.2*/import na.naportalbase.utils.TemplateUtils
/*7.2*/import na.naportalbase.views.html.components.checkBoxComp.checkBoxComp
/*8.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.components.CheckList
/*9.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping
/*10.2*/import na.naportalbase.views.tags.i18n

object checkList extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[Mapping,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*12.2*/(mapping: Mapping):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*14.2*/checkList/*14.11*/ = {{mapping.getComponent.asInstanceOf[CheckList]}};def /*15.2*/checkListId/*15.13*/ = {{mapping.getTemplateMapping}};def /*16.2*/isRequired/*16.12*/ = {{checkList.getRequired}};def /*18.2*/checkLisAttributes/*18.20*/ = {{
    var map = mutable.HashMap[String, String](
        "data-ng-model" -> ("model[\""+mapping.getTemplateMapping+"\"]"),
        "data-model" -> mapping.getTemplateMapping,
        "data-na-portal-go-dynamic-form-check-list" -> null,
        "id" -> checkListId,
        "data-component" -> BaseMappings.dualMapper.valueToTree(checkList),
        "name" -> mapping.getTemplateMapping
    )
    if(isRequired){
        map.put("data-na-portal-required",null)
    }
    immutable.HashMap(map.toSeq:_*)
}};def /*33.2*/dynamicOptions/*33.16*/ = {{
    var stringBuilder = new StringBuilder

    var values = Sets.newHashSet(Splitter.on(",").split(checkList.getDefaultValue))

    for(option <- checkList.getOptions){
        if (option.getVisible) {
            val checkboxIndex = String.valueOf(option.getIndex)
            var checkBoxId = checkListId+"_"+option.getValue;
            val optionAttributes = mutable.HashMap[String,String](
                "id" -> checkBoxId,
                "value" -> option.getValue,
                "name" -> mapping.getTemplateMapping,
                "data-component" -> BaseMappings.dualMapper.valueToTree(checkList)
            )
            if(values.contains(checkboxIndex)){
                optionAttributes.put("checked","checked")
            }
            val labelAttributes = mutable.HashMap[String,String](
                "class" -> "control-label"
            )
            stringBuilder.append(checkBoxComp.render(option.getLabel,checkBoxId,optionAttributes,labelAttributes))
        }
    }
    Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*12.20*/("""

"""),format.raw/*14.61*/("""
"""),format.raw/*15.45*/("""
"""),format.raw/*16.39*/("""

"""),format.raw/*31.2*/("""

"""),format.raw/*58.2*/("""

"""),format.raw/*60.1*/("""<div class="form-group">
    <label for=""""),_display_(/*61.18*/mapping/*61.25*/.getLabel),format.raw/*61.34*/("""" class="col-sm-2 control-label """),_display_(/*61.67*/if(isRequired)/*61.81*/{_display_(Seq[Any](format.raw/*61.82*/("""fx-required""")))}),format.raw/*61.94*/("""" title=""""),_display_(/*61.104*/mapping/*61.111*/.getDescription),format.raw/*61.126*/("""">
        """),_display_(/*62.10*/mapping/*62.17*/.getLabel),_display_(/*62.27*/if(isRequired)/*62.41*/{_display_(Seq[Any](format.raw/*62.42*/("""<abbr title=""""),_display_(/*62.56*/i18n("na.basemodule.validations.mandatory")),format.raw/*62.99*/(""""></abbr>""")))}),format.raw/*62.109*/("""
    """),format.raw/*63.5*/("""</label>
    <div class="col-sm-10" data-na-portal-field-notification >
        <div """),_display_(/*65.15*/TemplateUtils/*65.28*/.dynamicElementsAttributes(checkLisAttributes)),format.raw/*65.74*/(""">
        """),_display_(/*66.10*/{dynamicOptions}),format.raw/*66.26*/("""
        """),format.raw/*67.9*/("""</div>
    </div>
</div>"""))
      }
    }
  }

  def render(mapping:Mapping): play.twirl.api.HtmlFormat.Appendable = apply(mapping)

  def f:((Mapping) => play.twirl.api.HtmlFormat.Appendable) = (mapping) => apply(mapping)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/dynamicForm/fields/checkList.scala.html
                  HASH: 38926f96e6a22b33607abb651e2a6a72f1fab211
                  MATRIX: 687->1|739->47|778->80|819->115|865->155|910->194|960->238|1038->310|1131->397|1212->471|1561->512|1658->533|1676->542|1740->594|1760->605|1806->639|1825->649|1866->679|1893->697|2414->1207|2437->1221|3510->530|3540->592|3569->637|3598->676|3627->1204|3656->2263|3685->2265|3754->2307|3770->2314|3800->2323|3860->2356|3883->2370|3922->2371|3965->2383|4003->2393|4020->2400|4057->2415|4096->2427|4112->2434|4142->2444|4165->2458|4204->2459|4245->2473|4309->2516|4351->2526|4383->2531|4496->2617|4518->2630|4585->2676|4623->2687|4660->2703|4696->2712
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|38->12|42->14|42->14|42->15|42->15|42->16|42->16|42->18|42->18|55->33|55->33|81->12|83->14|84->15|85->16|87->31|89->58|91->60|92->61|92->61|92->61|92->61|92->61|92->61|92->61|92->61|92->61|92->61|93->62|93->62|93->62|93->62|93->62|93->62|93->62|93->62|94->63|96->65|96->65|96->65|97->66|97->66|98->67
                  -- GENERATED --
              */
          