
package na.go.views.html.components.admintemplatecreate

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*3.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Family
/*4.2*/import scala.collection.immutable
/*5.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*6.2*/import na.naportalbase.views.html.components.wizard.wizardContentComp

object characteristicsTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,List[Family],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(ngModelVariable: String, groups : List[Family]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*103.2*/headContent/*103.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*103.17*/("""
	"""),format.raw/*104.2*/("""<p class="fx-required">
        <abbr title=""""),_display_(/*105.23*/i18n("na.basemodule.validations.mandatory")),format.raw/*105.66*/(""""></abbr>
        """),_display_(/*106.10*/i18n("na.portal.go.operation.create.legend.requiredfields.label")),format.raw/*106.75*/("""
	"""),format.raw/*107.2*/("""</p>
	<p>
		<strong>
		"""),_display_(/*110.4*/i18n("na.portal.go.operation.create.tab.characteristics.description.title")),format.raw/*110.79*/("""
		"""),format.raw/*111.3*/("""</strong>
	</p>
	<p>"""),_display_(/*113.6*/i18n("na.portal.go.operation.create.tab.characteristics.description.text")),format.raw/*113.80*/("""</p>
""")))};def /*119.2*/blockContent/*119.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*119.18*/("""
	"""),format.raw/*120.2*/("""<!-- FAMILY FIELD -->
	<div class="form-group">
	    <label for="selectRfsNames" class="col-sm-2 control-label fx-required">
            """),_display_(/*123.14*/operationFamilyLabel),format.raw/*123.34*/("""<abbr title=""""),_display_(/*123.48*/i18n("na.basemodule.validations.mandatory")),format.raw/*123.91*/(""""></abbr>
        </label>
	    <div class="col-sm-10">
		    """),_display_(/*126.8*/selectComp/*126.18*/.render(
	            operationFamilySelectAttributes,null,null,operationFamilySelectData)),format.raw/*127.82*/("""
	    """),format.raw/*128.6*/("""</div>

	</div>

	<!-- RFS FIELD -->
	<div class="form-group">
	    <label for=""""),_display_(/*134.19*/{operationRfsId}),format.raw/*134.35*/("""" class="col-sm-2 control-label fx-required">
            """),_display_(/*135.14*/{operationRfsLabel}),format.raw/*135.33*/("""<abbr title=""""),_display_(/*135.47*/i18n("na.basemodule.validations.mandatory")),format.raw/*135.90*/(""""></abbr>
        </label>
			<div class="col-sm-10">
				"""),_display_(/*138.6*/selectComp/*138.16*/.render(
	            operationRfsSelectAttributes,null,null,operationRfsSelectData)),format.raw/*139.76*/("""
	    """),format.raw/*140.6*/("""</div>
	</div>

	<!-- OPERATION FIELD -->
	<div class="form-group">
	    <label for=""""),_display_(/*145.19*/{operationOpId}),format.raw/*145.34*/("""" class="col-sm-2 control-label fx-required">
            """),_display_(/*146.14*/{operationOpLabel}),format.raw/*146.32*/("""<abbr title=""""),_display_(/*146.46*/i18n("na.basemodule.validations.mandatory")),format.raw/*146.89*/(""""></abbr>
        </label>
			<div class="col-sm-10">
		    """),_display_(/*149.8*/selectComp/*149.18*/.render(
	            operationOpSelectAttributes,null,null,operationOpSelectData)),format.raw/*150.74*/("""
	    """),format.raw/*151.6*/("""</div>
	</div>
""")))};def /*10.2*/prefixId/*10.10*/ = {{""}};def /*14.2*/operationFamilyId/*14.19*/ = {{"family-field"}};def /*15.2*/operationFamilyLabel/*15.22*/ = {{i18n("na.portal.go.family.field.label")}};def /*16.2*/operationFamilySelectAttributes/*16.33*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> (ngModelVariable+".data.family"),
        "name" -> operationFamilyId,
        "data-na-portal-select-box" -> null,
        "data-na-portal-select-box-auto-dropdown-size" -> null,
        "data-na-portal-go-wizard-family-field" -> null,
        "value" -> "",
        "id" -> (prefixId+operationFamilyId),
        "class" -> "form-control input-sm",
        "data-context" -> "family",
        "full-width" -> "",
    )
    map
}};def /*32.2*/operationFamilySelectData/*32.27*/ = {{
    var stringBuilder = new StringBuilder
    for(group <- groups){
        var optionAttributes = immutable.HashMap[String,String](
            "id" -> group.getName,
            "text" -> group.getName,
            "value" -> group.getName
        )
        stringBuilder.append(dynamicSelectOptionComp.render(group.getName,optionAttributes))
    }
    Html(stringBuilder.toString())
}};def /*47.2*/operationRfsId/*47.16*/ = {{"selectRfsNames"}};def /*48.2*/operationRfsLabel/*48.19*/ = {{i18n("na.portal.go.rfs.field.label")}};def /*49.2*/operationRfsSelectAttributes/*49.30*/ = {{
    var map = immutable.HashMap[String, String](
        "data-ng-model" -> (ngModelVariable+".data.rfs"),
        "name" -> operationRfsId,
        "data-na-portal-select-box" -> null,
        "data-na-portal-select-box-auto-dropdown-size" -> null,
        "data-na-portal-go-wizard-rfs-field" -> null,
        "value" -> "",
        "id" -> (prefixId+operationRfsId),
        "class" -> "form-control input-sm",
        "data-context" -> "rfs",
        "full-width" -> "",
    )
    map
}};def /*65.2*/operationRfsSelectData/*65.24*/ = {{
    var stringBuilder = new StringBuilder
    Html(stringBuilder.toString())
}};def /*72.2*/operationOpId/*72.15*/ = {{"operation"}};def /*73.2*/operationOpLabel/*73.18*/ = {{i18n("na.portal.go.operation.field.label")}};def /*74.2*/operationOpSelectAttributes/*74.29*/ = {{ immutable.HashMap[String, String](
    "data-ng-model" -> (ngModelVariable+".data.operation"),
    "name" -> operationOpId,
    "data-na-portal-select-box" -> null,
    "data-na-portal-select-box-auto-dropdown-size" -> null,
    "data-na-portal-go-wizard-operation-field" -> null,
    "value" -> "",
    "id" -> (prefixId+operationOpId),
    "class" -> "form-control input-sm",
    "full-width" -> "",
)}};def /*86.2*/operationOpSelectData/*86.23*/ = {{
    var list = immutable.List[String](
        "getFisicosBits","getClientInfoMox","getFastZapping","getUserStackedPortCounters","getClients","trDiagnostic"
    )
    var stringBuilder = new StringBuilder
    for(option <- list){
        var optionAttributes = immutable.HashMap[String, String](
            "name" -> option,
            "id" -> option,
            "value" -> option
        )
        stringBuilder.append(dynamicSelectOptionComp.render(option,optionAttributes))
    }
    Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*8.50*/("""

"""),format.raw/*10.18*/("""


"""),format.raw/*13.1*/("""<!-- FAMILY FIELD CONFIG -->
"""),format.raw/*14.39*/("""
"""),format.raw/*15.67*/("""
"""),format.raw/*30.2*/("""

"""),format.raw/*43.2*/("""


"""),format.raw/*46.1*/("""<!-- RFS FIELD CONFIG -->
"""),format.raw/*47.38*/("""
"""),format.raw/*48.61*/("""
"""),format.raw/*63.2*/("""

"""),format.raw/*68.2*/("""


"""),format.raw/*71.1*/("""<!-- OPERATION FIELD CONFIG -->
"""),format.raw/*72.32*/("""
"""),format.raw/*73.74*/("""
"""),format.raw/*84.3*/("""

"""),format.raw/*100.2*/("""


"""),format.raw/*114.2*/("""




"""),format.raw/*153.2*/("""

"""),_display_(/*155.2*/wizardContentComp/*155.19*/.render(headContent,blockContent)))
      }
    }
  }

  def render(ngModelVariable:String,groups:List[Family]): play.twirl.api.HtmlFormat.Appendable = apply(ngModelVariable,groups)

  def f:((String,List[Family]) => play.twirl.api.HtmlFormat.Appendable) = (ngModelVariable,groups) => apply(ngModelVariable,groups)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/admintemplatecreate/characteristicsTab.scala.html
                  HASH: 1b71d5f6ca12cc320424d43984cf6761132d1bbc
                  MATRIX: 688->1|734->41|822->123|901->196|942->231|1017->300|1417->372|1545->3438|1566->3449|1648->3453|1678->3455|1752->3501|1817->3544|1864->3563|1951->3628|1981->3630|2032->3654|2129->3729|2160->3732|2208->3753|2304->3827|2334->3839|2356->3851|2438->3855|2468->3857|2634->3995|2676->4015|2718->4029|2783->4072|2873->4135|2893->4145|3005->4235|3039->4241|3148->4322|3186->4338|3273->4397|3314->4416|3356->4430|3421->4473|3507->4532|3527->4542|3633->4626|3667->4632|3781->4718|3818->4733|3905->4792|3945->4810|3987->4824|4052->4867|4140->4928|4160->4938|4264->5020|4298->5026|4337->423|4354->431|4376->472|4402->489|4436->511|4465->531|4524->578|4564->609|5089->1123|5123->1148|5530->1571|5553->1585|5589->1609|5615->1626|5671->1670|5708->1698|6218->2197|6249->2219|6347->2339|6369->2352|6400->2371|6425->2387|6487->2445|6523->2472|6947->2885|6977->2906|7535->420|7565->439|7595->442|7652->509|7681->576|7709->1120|7738->1541|7768->1544|7822->1607|7851->1668|7879->2194|7908->2303|7938->2306|7998->2369|8027->2443|8055->2882|8085->3434|8116->3833|8149->5042|8179->5045|8206->5062
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->103|38->103|40->103|41->104|42->105|42->105|43->106|43->106|44->107|47->110|47->110|48->111|50->113|50->113|51->119|51->119|53->119|54->120|57->123|57->123|57->123|57->123|60->126|60->126|61->127|62->128|68->134|68->134|69->135|69->135|69->135|69->135|72->138|72->138|73->139|74->140|79->145|79->145|80->146|80->146|80->146|80->146|83->149|83->149|84->150|85->151|87->10|87->10|87->14|87->14|87->15|87->15|87->16|87->16|101->32|101->32|112->47|112->47|112->48|112->48|112->49|112->49|126->65|126->65|129->72|129->72|129->73|129->73|129->74|129->74|139->86|139->86|154->8|156->10|159->13|160->14|161->15|162->30|164->43|167->46|168->47|169->48|170->63|172->68|175->71|176->72|177->73|178->84|180->100|183->114|188->153|190->155|190->155
                  -- GENERATED --
              */
          