
package na.go.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import play.mvc.Http
/*2.2*/import na.naportalbase.views.tags.i18n

object resume extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.1*/("""<!DOCTYPE html>
<html lang=""""),_display_(/*4.14*/{Http.Context.current().lang().code()}),format.raw/*4.52*/("""" data-app-theme="nossis-one">
    <head>
        <meta charset="utf-8">
        <title>"""),_display_(/*7.17*/i18n("na.information.title")),format.raw/*7.45*/("""</title>
        """),_display_(/*8.10*/na/*8.12*/.naportalbase.views.html.imports.resumescripts.render()),format.raw/*8.67*/("""
    """),format.raw/*9.5*/("""</head>
    <body>
        <x-resume-section button-action-go-to-module="go" data-module="na.portal.go"></x-resume-section>
    </body>
</html>
"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/roots/resume.scala.html
                  HASH: 69c6570c18e5a6461bf6bfd7a8c482b9adfe5c8f
                  MATRIX: 663->1|691->24|1117->64|1173->94|1231->132|1349->224|1397->252|1442->271|1452->273|1527->328|1559->334
                  LINES: 24->1|25->2|35->3|36->4|36->4|39->7|39->7|40->8|40->8|40->8|41->9
                  -- GENERATED --
              */
          