
package na.go.views.html.components.admintemplateedit

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n
/*2.2*/import scala.collection.mutable
/*3.2*/import na.naportalbase.views.html.components.inputsComp.inputTextComp
/*4.2*/import na.naportalbase.views.html.skeletons.datatables.naDatatablesSkeleton
/*5.2*/import na.naportalbase.views.html.components.wizard.wizardContentComp

object operationTab extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(template: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*23.2*/headContent/*23.13*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*23.17*/("""
	"""),format.raw/*24.2*/("""<p class="fx-required">
		<abbr title=""""),_display_(/*25.17*/i18n("na.basemodule.validations.mandatory")),format.raw/*25.60*/(""""></abbr>
		"""),_display_(/*26.4*/i18n("na.portal.go.operation.create.legend.requiredfields.label")),format.raw/*26.69*/("""
	"""),format.raw/*27.2*/("""</p>
	<p><strong>"""),_display_(/*28.14*/i18n("na.portal.go.adminoperation.create.tab.operation.description.title")),format.raw/*28.88*/("""</strong></p>
	<p>"""),_display_(/*29.6*/i18n("na.portal.go.adminoperation.create.tab.operation.description.text")),format.raw/*29.79*/("""</p><br>
""")))};def /*38.2*/blockContent/*38.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*38.18*/("""
	"""),format.raw/*39.2*/("""<form class="form-horizontal" data-na-portal-go-dynamic-form data-model="operationTabFormModel">
		<div class="form-group">
			<label for=""""),_display_(/*41.17*/inputId),format.raw/*41.24*/("""" class="col-sm-2 control-label fx-required">
				"""),_display_(/*42.6*/inputLabel),format.raw/*42.16*/("""<abbr title=""""),_display_(/*42.30*/i18n("na.basemodule.validations.mandatory")),format.raw/*42.73*/(""""></abbr>
			</label>
			<div class="col-sm-10" data-na-portal-field-notification data-na-portal-input-notification-hr>
			"""),_display_(/*45.5*/inputTextComp/*45.18*/.render(inputId, inputPlaceHolder, inputAttributes)),format.raw/*45.69*/("""
			"""),format.raw/*46.4*/("""</div>
		</div>
		<br>
		<p><strong>"""),_display_(/*49.15*/i18n("na.portal.go.adminoperation.create.tab.operation.fieldDefinition.title")),format.raw/*49.93*/("""</strong></p><br>
		<p>"""),_display_(/*50.7*/i18n("na.portal.go.adminoperation.create.tab.operation.fieldDefinition.text")),format.raw/*50.84*/("""</p>

		<div data-na-portal-go-template-operation=""""),_display_(/*52.47*/template),format.raw/*52.55*/("""">
			<div id="fx-entity-content" class="fx-entity-info" data-ng-controller="NaPortalGoAdminTemplateParametersDatatableController">
			"""),_display_(/*54.5*/naDatatablesSkeleton/*54.25*/.render(adminOperationsTableAttributes)),format.raw/*54.64*/("""
			"""),format.raw/*55.4*/("""</div>
		</div>
    </form>
""")))};def /*10.2*/inputLabel/*10.12*/ = {{i18n("na.portal.go.field.operationName.label")}};def /*11.2*/inputPlaceHolder/*11.18*/ = {{i18n("na.portal.go.field.operationName.placeholder")}};def /*12.2*/inputId/*12.9*/ = {{"templateNameField"}};def /*13.2*/inputAttributes/*13.17*/ = {{
	var map = mutable.HashMap[String, String]()
	map.put("data-entity", inputId)
	map.put("data-ng-model", "staticFieldValues.name")
	map.put("name", inputId)
	map.put("id", inputId)
	map.put("data-na-portal-required",null)
	map
}};def /*32.2*/adminOperationsTableAttributes/*32.32*/ = {{
	var map = mutable.HashMap[String,String]()
	map.put("id","datatableMappingsTemplate")
	map
}};
Seq[Any](format.raw/*7.20*/("""

"""),format.raw/*9.1*/("""<!-- TEMPLATE NAME FIELD CONFIG -->
"""),format.raw/*10.64*/("""
"""),format.raw/*11.76*/("""
"""),format.raw/*12.34*/("""
"""),format.raw/*21.2*/("""

"""),format.raw/*30.2*/("""

"""),format.raw/*36.2*/("""

"""),format.raw/*58.2*/("""

"""),_display_(/*60.2*/wizardContentComp/*60.19*/.render(headContent,blockContent)))
      }
    }
  }

  def render(template:String): play.twirl.api.HtmlFormat.Appendable = apply(template)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (template) => apply(template)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/admintemplateedit/operationTab.scala.html
                  HASH: 66dc243f673b8ff49f10c0f99dcd225e6689026a
                  MATRIX: 686->1|732->41|771->74|848->145|931->222|1312->294|1409->776|1429->787|1510->791|1539->793|1606->833|1670->876|1709->889|1795->954|1824->956|1869->974|1964->1048|2009->1067|2103->1140|2136->1285|2157->1297|2238->1301|2267->1303|2434->1443|2462->1450|2539->1501|2570->1511|2611->1525|2675->1568|2825->1692|2847->1705|2919->1756|2950->1760|3014->1797|3113->1875|3163->1899|3261->1976|3340->2028|3369->2036|3531->2172|3560->2192|3620->2231|3651->2235|3703->351|3722->361|3788->415|3813->431|3885->491|3900->498|3939->525|3963->540|4210->1153|4249->1183|4378->312|4406->314|4470->413|4499->489|4528->523|4556->773|4585->1150|4614->1282|4643->2264|4672->2267|4698->2284
                  LINES: 24->1|25->2|26->3|27->4|28->5|33->7|37->23|37->23|39->23|40->24|41->25|41->25|42->26|42->26|43->27|44->28|44->28|45->29|45->29|46->38|46->38|48->38|49->39|51->41|51->41|52->42|52->42|52->42|52->42|55->45|55->45|55->45|56->46|59->49|59->49|60->50|60->50|62->52|62->52|64->54|64->54|64->54|65->55|68->10|68->10|68->11|68->11|68->12|68->12|68->13|68->13|76->32|76->32|81->7|83->9|84->10|85->11|86->12|87->21|89->30|91->36|93->58|95->60|95->60
                  -- GENERATED --
              */
          