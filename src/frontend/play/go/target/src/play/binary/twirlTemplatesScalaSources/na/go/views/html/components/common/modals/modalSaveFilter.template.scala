
package na.go.views.html.components.common.modals

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import scala.collection.mutable
/*2.2*/import scala.collection.immutable
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import na.naportalbase.views.html.components.inputsComp.modalInputTextComp
/*5.2*/import na.go.views.html.components.adminpage.{ operationForm => adminOperationForm }
/*6.2*/import na.go.views.html.components.home.{ operationForm => operationForm }
/*7.2*/import na.naportalbase.views.html.skeletons.search.horizontalFormSkel
/*8.2*/import na.go.models.general.OperCatFamilyList

object modalSaveFilter extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[OperCatFamilyList,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*10.2*/(groups : OperCatFamilyList, operationFormType: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*19.2*/operationFormTemplate/*19.23*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*19.27*/("""
    """),_display_(/*20.6*/if(operationFormType eq "operationForm")/*20.46*/ {_display_(Seq[Any](format.raw/*20.48*/("""
        """),_display_(/*21.10*/operationForm/*21.23*/.render("tempSearchFormItems",immutable.HashMap[String,String](), groups, true)),format.raw/*21.102*/("""
    """)))}/*22.7*/else/*22.12*/{_display_(Seq[Any](format.raw/*22.13*/("""
        """),_display_(/*23.10*/adminOperationForm/*23.28*/.render("tempSearchFormItems",immutable.HashMap[String,String](), groups, true)),format.raw/*23.107*/("""
    """)))}),format.raw/*24.6*/("""
""")))};def /*48.2*/form/*48.6*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*48.10*/("""
"""),format.raw/*49.1*/("""<div id="generic-operation-modal-form">
    <div class="form-group" data-na-portal-field-notification>
        """),_display_(/*51.10*/modalInputTextComp/*51.28*/.render(filterNameLabel, "filterName",filterNamePlaceHolder,filterNameInputAttributes)),format.raw/*51.114*/("""
    """),format.raw/*52.5*/("""</div>

    <div class="fx-expand">
        """),_display_(/*55.10*/i18n("na.modals.filters.options")),format.raw/*55.43*/("""
    """),format.raw/*56.5*/("""</div>
    <div class="clearfix">
        """),_display_(/*58.10*/operationFormTemplate),format.raw/*58.31*/("""
    """),format.raw/*59.5*/("""</div>
</div>
""")))};def /*28.2*/filterNameLabel/*28.17*/ = {{i18n("na.modals.fields.name.label")}};def /*29.2*/filterNamePlaceHolder/*29.23*/ = {{i18n("na.modals.fields.name.placeholder")}};def /*30.2*/filterNameInputAttributes/*30.27*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-model", "tempSearchFormItems.filterName")
    map.put("data-na-portal-required",null)
    map.put("name", "filterName")
    map.put("id", "filterName")
    map.put("maxlength", "64")

    map
}};def /*41.2*/formAttributes/*41.16*/ = {{
    var map = mutable.HashMap[String,String]()
    map.put("data-ng-controller","NaPortalGoOperationSaveModalController as opModalCtrl")

    map
}};
Seq[Any](format.raw/*10.57*/("""

"""),format.raw/*12.1*/("""<script>
    //https://github.com/ivaynberg/select2/issues/1436
    $.fn.modal.Constructor.prototype.enforceFocus = function() """),format.raw/*14.64*/("""{"""),format.raw/*14.65*/("""}"""),format.raw/*14.66*/(""";
</script>

<p>"""),_display_(/*17.5*/i18n("na.modal.filters.info.savefilter")),format.raw/*17.45*/("""</p>

"""),format.raw/*25.2*/("""

"""),format.raw/*27.1*/("""<!-- FilterName FIELD -->
"""),format.raw/*28.58*/("""
"""),format.raw/*29.70*/("""
"""),format.raw/*39.2*/("""

"""),format.raw/*46.2*/("""

"""),format.raw/*61.2*/("""

"""),_display_(/*63.2*/horizontalFormSkel/*63.20*/.render(formAttributes,form)))
      }
    }
  }

  def render(groups:OperCatFamilyList,operationFormType:String): play.twirl.api.HtmlFormat.Appendable = apply(groups,operationFormType)

  def f:((OperCatFamilyList,String) => play.twirl.api.HtmlFormat.Appendable) = (groups,operationFormType) => apply(groups,operationFormType)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/components/common/modals/modalSaveFilter.scala.html
                  HASH: 63f583fa4cc4804d527c61aaf6da7c3ecb770d49
                  MATRIX: 682->1|721->34|762->69|808->109|890->185|982->271|1064->347|1141->418|1520->466|1654->716|1684->737|1765->741|1797->747|1846->787|1886->789|1923->799|1945->812|2046->891|2070->898|2083->903|2122->904|2159->914|2186->932|2287->1011|2323->1017|2348->1641|2360->1645|2441->1649|2469->1650|2608->1762|2635->1780|2743->1866|2775->1871|2847->1916|2901->1949|2933->1954|3003->1997|3045->2018|3077->2023|3115->1048|3139->1063|3194->1106|3224->1127|3285->1176|3319->1201|3600->1471|3623->1485|3807->521|3836->523|3991->650|4020->651|4049->652|4092->669|4153->709|4186->1019|4215->1021|4269->1104|4298->1174|4326->1468|4355->1638|4384->2038|4413->2041|4440->2059
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|36->10|40->19|40->19|42->19|43->20|43->20|43->20|44->21|44->21|44->21|45->22|45->22|45->22|46->23|46->23|46->23|47->24|48->48|48->48|50->48|51->49|53->51|53->51|53->51|54->52|57->55|57->55|58->56|60->58|60->58|61->59|63->28|63->28|63->29|63->29|63->30|63->30|72->41|72->41|78->10|80->12|82->14|82->14|82->14|85->17|85->17|87->25|89->27|90->28|91->29|92->39|94->46|96->61|98->63|98->63
                  -- GENERATED --
              */
          