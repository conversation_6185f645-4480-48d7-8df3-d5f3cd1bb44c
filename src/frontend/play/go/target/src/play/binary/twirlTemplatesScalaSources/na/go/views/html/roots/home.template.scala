
package na.go.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.go.views.html.skeletons.mainSkel
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.go.settings.AAAPIResources
/*4.2*/import na.naportalbase.utils.SecurityUtils
/*5.2*/import na.go.settings.GoConstants.NavContext
/*6.2*/import na.go.views.html.components.home.leftSideForm
/*7.2*/import na.go.views.html.components.common.lateralNav
/*8.2*/import na.go.models.general.OperCatFamilyList
/*10.2*/import pt.alticelabs.nossis.security.views.html.authorized

object home extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[OperCatFamilyList,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(groups : OperCatFamilyList):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*13.2*/leftSide/*13.10*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*13.14*/("""
    """),_display_(/*14.6*/leftSideForm/*14.18*/.render(groups)),format.raw/*14.33*/("""
""")))};def /*17.2*/rightSide/*17.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*17.15*/("""
    """),format.raw/*18.5*/("""<div class="fx-splitter-content-inner">
        <div class="fx-entity-header">
            <div class="fx-entity-header-info">
                <span class="fx-entity-header-icon">
                    <i class="fuxicons fuxicons-go"></i>
                </span>
                <div class="fx-entity-header-title">
                    <h1>
                    """),_display_(/*26.22*/i18n("na.portal.go.home.label.title")),format.raw/*26.59*/("""
                    """),format.raw/*27.21*/("""</h1>
                    <p class="fx-entity-header-details">
                    """),_display_(/*29.22*/i18n("na.portal.go.home.label.subtitle")),format.raw/*29.62*/("""
                    """),format.raw/*30.21*/("""</p>
                </div>
            </div>
            """),_display_(/*33.14*/authorized(AAAPIResources.genericOperations.X.toString())/*33.71*/{_display_(Seq[Any](format.raw/*33.72*/("""
                """),format.raw/*34.17*/("""<div class="fx-entity-header-actions">
                    <button id="create-button" class="btn btn-primary fx-call-to-action" data-page-action="execute-operation">
                        <i class="glyphicon glyphicon-plus"></i>
                        """),_display_(/*37.26*/i18n("na.portal.go.buttons.executeoperation")),format.raw/*37.71*/("""
                    """),format.raw/*38.21*/("""</button>
                </div>
            """)))}),format.raw/*40.14*/("""
        """),format.raw/*41.9*/("""</div>
        <div id="entity-content" class="fx-entity-info">
            <x-shadow-scroll vertical>
                <div
                    data-na-portal-table-datatable
                    data-na-portal-table-load-using-ajax
                    data-config-url=""""),_display_(/*47.39*/na/*47.41*/.go.controllers.routes.TableConfigs.operationsSearch()),format.raw/*47.95*/(""""
                    data-na-portal-go-operations-search-table>
                        <div data-na-portal-table-has-column-filter></div>
                </div>
            </x-shadow-scroll>
        </div>
    </div>
""")))};def /*56.2*/isAdmin/*56.9*/ = {{SecurityUtils.isAuthorized(AAAPIResources.genericOperationsAdmin().R())}};def /*58.2*/navBar/*58.8*/ = {{
    if(isAdmin){
        lateralNav.render(NavContext.OPERATIONS);
    } else {
        null;
    }
}};
Seq[Any](format.raw/*11.30*/("""

"""),format.raw/*15.2*/("""

"""),format.raw/*54.2*/("""

"""),format.raw/*56.86*/("""

"""),format.raw/*64.2*/("""


"""),_display_(/*67.2*/mainSkel()/*67.12*/{_display_(Seq[Any](format.raw/*67.13*/("""
    """),format.raw/*68.5*/("""<div class="fx-push-footer page--go-search-page fx-full-height" data-na-portal-go-common data-na-portal-go-operation-search-page>
        """),_display_(/*69.10*/navBar),format.raw/*69.16*/("""
        """),format.raw/*70.9*/("""<x-splitter class="splitter splitter--go-search">
            <form slot="left" class="search-sidebar">
                """),_display_(/*72.18*/leftSide),format.raw/*72.26*/("""
            """),format.raw/*73.13*/("""</form>
            <div id="fx-splitter-content" slot="right">
                """),_display_(/*75.18*/rightSide),format.raw/*75.27*/("""
            """),format.raw/*76.13*/("""</div>
            <div slot="collapsed-left">
                <div class="fx-info-sidebar-collapsed left">
                    <div class="fx-sidebar-header search-sidebar__header">
                        <span class="fx-entity-header-icon">
                            <i class="fa fa-search"></i>
                        </span>
                    </div>
                </div>
            </div>
        </x-splitter>
    </div>
""")))}))
      }
    }
  }

  def render(groups:OperCatFamilyList): play.twirl.api.HtmlFormat.Appendable = apply(groups)

  def f:((OperCatFamilyList) => play.twirl.api.HtmlFormat.Appendable) = (groups) => apply(groups)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:28 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/go/target/TwirlSource/na/go/views/roots/home.scala.html
                  HASH: b09a352dac2aa672b1e20c0cc815c548c6c1d9e6
                  MATRIX: 663->1|713->45|759->85|803->123|853->167|905->213|965->267|1025->321|1079->369|1453->429|1560->460|1577->468|1658->472|1690->478|1711->490|1747->505|1772->510|1790->519|1871->523|1903->528|2290->888|2348->925|2397->946|2508->1030|2569->1070|2618->1091|2705->1151|2771->1208|2810->1209|2855->1226|3138->1482|3204->1527|3253->1548|3330->1594|3366->1603|3663->1873|3674->1875|3749->1929|3993->2153|4008->2160|4099->2240|4113->2246|4251->457|4280->507|4309->2150|4339->2237|4368->2353|4398->2357|4417->2367|4456->2368|4488->2373|4654->2512|4681->2518|4717->2527|4865->2648|4894->2656|4935->2669|5043->2750|5073->2759|5114->2772
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->10|37->11|41->13|41->13|43->13|44->14|44->14|44->14|45->17|45->17|47->17|48->18|56->26|56->26|57->27|59->29|59->29|60->30|63->33|63->33|63->33|64->34|67->37|67->37|68->38|70->40|71->41|77->47|77->47|77->47|84->56|84->56|84->58|84->58|91->11|93->15|95->54|97->56|99->64|102->67|102->67|102->67|103->68|104->69|104->69|105->70|107->72|107->72|108->73|110->75|110->75|111->76
                  -- GENERATED --
              */
          