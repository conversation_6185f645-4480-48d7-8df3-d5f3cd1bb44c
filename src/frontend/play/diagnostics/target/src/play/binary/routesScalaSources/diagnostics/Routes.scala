// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/diagnostics/conf/diagnostics.routes
// @DATE:Wed Jul 02 14:15:24 WEST 2025

package diagnostics

import play.core.routing._
import play.core.routing.HandlerInvokerFactory._

import play.api.mvc._

import _root_.controllers.Assets.Asset

class Routes(
  override val errorHandler: play.api.http.HttpErrorHandler, 
  // @LINE:2
  Application_0: na.diagnostics.controllers.Application,
  // @LINE:8
  naportalbase_Routes_0: naportalbase.Routes,
  val prefix: String
) extends GeneratedRouter {

   @javax.inject.Inject()
   def this(errorHandler: play.api.http.HttpErrorHandler,
    // @LINE:2
    Application_0: na.diagnostics.controllers.Application,
    // @LINE:8
    naportalbase_Routes_0: naportalbase.Routes
  ) = this(error<PERSON><PERSON><PERSON>, Application_0, naportalbase_Routes_0, "/")

  def withPrefix(prefix: String): Routes = {
    diagnostics.RoutesPrefix.setPrefix(prefix)
    new Routes(errorHandler, Application_0, naportalbase_Routes_0, prefix)
  }

  private[this] val defaultPrefix: String = {
    if (this.prefix.endsWith("/")) "" else "/"
  }

  def documentation = List(
    ("""GET""", this.prefix + (if(this.prefix.endsWith("/")) "" else "/") + """diagnostics/resume""", """na.diagnostics.controllers.Application.resume()"""),
    prefixed_naportalbase_Routes_0_1.router.documentation,
    Nil
  ).foldLeft(List.empty[(String,String,String)]) { (s,e) => e.asInstanceOf[Any] match {
    case r @ (_,_,_) => s :+ r.asInstanceOf[(String,String,String)]
    case l => s ++ l.asInstanceOf[List[(String,String,String)]]
  }}


  // @LINE:2
  private[this] lazy val na_diagnostics_controllers_Application_resume0_route = Route("GET",
    PathPattern(List(StaticPart(this.prefix), StaticPart(this.defaultPrefix), StaticPart("diagnostics/resume")))
  )
  private[this] lazy val na_diagnostics_controllers_Application_resume0_invoker = createInvoker(
    Application_0.resume(),
    play.api.routing.HandlerDef(this.getClass.getClassLoader,
      "diagnostics",
      "na.diagnostics.controllers.Application",
      "resume",
      Nil,
      "GET",
      this.prefix + """diagnostics/resume""",
      """ Resume""",
      Seq()
    )
  )

  // @LINE:8
  private[this] val prefixed_naportalbase_Routes_0_1 = Include(naportalbase_Routes_0.withPrefix(this.prefix + (if (this.prefix.endsWith("/")) "" else "/") + "diagnostics"))


  def routes: PartialFunction[RequestHeader, Handler] = {
  
    // @LINE:2
    case na_diagnostics_controllers_Application_resume0_route(params@_) =>
      call { 
        na_diagnostics_controllers_Application_resume0_invoker.call(Application_0.resume())
      }
  
    // @LINE:8
    case prefixed_naportalbase_Routes_0_1(handler) => handler
  }
}
