
package na.monitoring.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.monitoring.views.html.skeletons.mainSkel

object orderClone extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*2.2*/(orderId: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*2.19*/("""

"""),_display_(/*4.2*/mainSkel()/*4.12*/ {_display_(Seq[Any](format.raw/*4.14*/("""
    """),format.raw/*5.5*/("""<x-na-monitoring-order-clone-page data-from-order=""""),_display_(/*5.57*/orderId),format.raw/*5.64*/("""" class="page page--monitoring-order-create"></x-na-monitoring-order-clone-page>
""")))}))
      }
    }
  }

  def render(orderId:String): play.twirl.api.HtmlFormat.Appendable = apply(orderId)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (orderId) => apply(orderId)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:33 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/target/TwirlSource/na/monitoring/views/roots/orderClone.scala.html
                  HASH: 79917d90981864cab3b79073d25c211cf8fb4cb4
                  MATRIX: 671->1|1031->53|1143->70|1171->73|1189->83|1228->85|1259->90|1337->142|1364->149
                  LINES: 24->1|29->2|34->2|36->4|36->4|36->4|37->5|37->5|37->5
                  -- GENERATED --
              */
          