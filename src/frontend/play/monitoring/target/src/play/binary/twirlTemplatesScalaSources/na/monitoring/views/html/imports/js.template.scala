
package na.monitoring.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object js extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script id="momentLocalesJs" src="/basepack/moment/2.18/moment-with-locales.min.js"></script>
<script id="dateRangePickerJs" src="/fuxi/lib/bootstrap-daterangepicker/3.0/js/daterangepicker.js"></script>
<script src="/fuxi/lib/codemirror/5.1/js/codemirror.js"></script>
<script src="/fuxi/lib/codemirror/5.1/mode/javascript/javascript.js"></script>
<script src="/na-ext/js/dagre.js"></script>
<script src="/basepack/jsplumb/2.8/jsplumb.js"></script>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:33 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/target/TwirlSource/na/monitoring/views/imports/js.scala.html
                  HASH: b4f684272d8616475e5caff98428f61308c115c3
                  MATRIX: 1049->0
                  LINES: 33->1
                  -- GENERATED --
              */
          