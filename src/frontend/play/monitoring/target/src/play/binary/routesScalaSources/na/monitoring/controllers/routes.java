// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/conf/monitoring.routes
// @DATE:Wed Jul 02 14:15:33 WEST 2025

package na.monitoring.controllers;

import monitoring.RoutesPrefix;

public class routes {
  
  public static final na.monitoring.controllers.ReverseAssets Assets = new na.monitoring.controllers.ReverseAssets(RoutesPrefix.byNamePrefix());
  public static final na.monitoring.controllers.ReverseActivityController ActivityController = new na.monitoring.controllers.ReverseActivityController(RoutesPrefix.byNamePrefix());
  public static final na.monitoring.controllers.ReverseTableController TableController = new na.monitoring.controllers.ReverseTableController(RoutesPrefix.byNamePrefix());
  public static final na.monitoring.controllers.ReverseRequesterCallbackController RequesterCallbackController = new na.monitoring.controllers.ReverseRequesterCallbackController(RoutesPrefix.byNamePrefix());
  public static final na.monitoring.controllers.ReverseApplication Application = new na.monitoring.controllers.ReverseApplication(RoutesPrefix.byNamePrefix());
  public static final na.monitoring.controllers.ReverseOrderController OrderController = new na.monitoring.controllers.ReverseOrderController(RoutesPrefix.byNamePrefix());
  public static final na.monitoring.controllers.ReverseNotificationController NotificationController = new na.monitoring.controllers.ReverseNotificationController(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.monitoring.controllers.javascript.ReverseAssets Assets = new na.monitoring.controllers.javascript.ReverseAssets(RoutesPrefix.byNamePrefix());
    public static final na.monitoring.controllers.javascript.ReverseActivityController ActivityController = new na.monitoring.controllers.javascript.ReverseActivityController(RoutesPrefix.byNamePrefix());
    public static final na.monitoring.controllers.javascript.ReverseTableController TableController = new na.monitoring.controllers.javascript.ReverseTableController(RoutesPrefix.byNamePrefix());
    public static final na.monitoring.controllers.javascript.ReverseRequesterCallbackController RequesterCallbackController = new na.monitoring.controllers.javascript.ReverseRequesterCallbackController(RoutesPrefix.byNamePrefix());
    public static final na.monitoring.controllers.javascript.ReverseApplication Application = new na.monitoring.controllers.javascript.ReverseApplication(RoutesPrefix.byNamePrefix());
    public static final na.monitoring.controllers.javascript.ReverseOrderController OrderController = new na.monitoring.controllers.javascript.ReverseOrderController(RoutesPrefix.byNamePrefix());
    public static final na.monitoring.controllers.javascript.ReverseNotificationController NotificationController = new na.monitoring.controllers.javascript.ReverseNotificationController(RoutesPrefix.byNamePrefix());
  }

}
