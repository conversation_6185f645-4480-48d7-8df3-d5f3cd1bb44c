// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/conf/monitoring.routes
// @DATE:Wed Jul 02 14:15:33 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:2
package na.monitoring.controllers {

  // @LINE:14
  class ReverseAssets(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:14
    def versioned(file:Asset): Call = {
      implicit lazy val _rrc = new play.core.routing.ReverseRouteContext(Map(("path", "/public"))); _rrc
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/assets/" + implicitly[play.api.mvc.PathBindable[Asset]].unbind("file", file))
    }
  
  }

  // @LINE:78
  class ReverseActivityController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:80
    def restart(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/activity/restart")
    }
  
    // @LINE:78
    def skip(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/activity/skip")
    }
  
    // @LINE:79
    def rollback(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/activity/rollback")
    }
  
    // @LINE:82
    def complete(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/activity/complete")
    }
  
    // @LINE:81
    def retry(isNewRequest:Boolean = false): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/activity/retry" + play.core.routing.queryString(List(if(isNewRequest == false) None else Some(implicitly[play.api.mvc.QueryStringBindable[Boolean]].unbind("isNewRequest", isNewRequest)))))
    }
  
  }

  // @LINE:23
  class ReverseTableController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:26
    def dataTableDeliveryAttempts(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/table-configs/deliveryAttempts")
    }
  
    // @LINE:25
    def dataTableResponseRetries(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/table-configs/responseRetries")
    }
  
    // @LINE:29
    def dataTableNotificationDetails(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/table-configs/notificationDetails")
    }
  
    // @LINE:24
    def dataTableOperationServiceMonitoring(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/table-configs/operationServiceMonitoring")
    }
  
    // @LINE:27
    def dataTableValidationMessages(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/table-configs/validationMessages")
    }
  
    // @LINE:23
    def dataTableOrderMonitoring(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/table-configs/orderMonitoring")
    }
  
    // @LINE:28
    def dataTableExternalSystemInteractionsHistory(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/table-configs/external-system-history")
    }
  
  }

  // @LINE:62
  class ReverseRequesterCallbackController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:62
    def requesterCallback(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/requestCallback")
    }
  
  }

  // @LINE:2
  class ReverseApplication(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:11
    def getModuleAngularScripts(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/imports/jsscripts")
    }
  
    // @LINE:2
    def goToHome(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/home")
    }
  
    // @LINE:8
    def javascriptRoutes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/assets/javascripts/routes")
    }
  
    // @LINE:5
    def resume(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/resume")
    }
  
  }

  // @LINE:17
  class ReverseOrderController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:52
    def getOperationByVersionAndName(version:String, name:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/operation-info/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("name", name)))
    }
  
    // @LINE:44
    def getOrderSummary(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/view/summary")
    }
  
    // @LINE:40
    def queryOrders(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/search/orders")
    }
  
    // @LINE:53
    def getOperationsByVersion(versionName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/operationsByVersion" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("versionName", versionName)))))
    }
  
    // @LINE:45
    def getSettings(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/settings")
    }
  
    // @LINE:46
    def getStates(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/states")
    }
  
    // @LINE:59
    def cancelOrdersWithRollback(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/actions/cancelWithRollback")
    }
  
    // @LINE:51
    def getOperationById(id:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/operation-info/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("id", id)))
    }
  
    // @LINE:55
    def getGraph(workflowExternalId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/graph" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("workflowExternalId", workflowExternalId)))))
    }
  
    // @LINE:61
    def removeOrders(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/actions/remove")
    }
  
    // @LINE:49
    def getFiltersAsJson(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/filters")
    }
  
    // @LINE:58
    def rollBackOrders(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/actions/rollback")
    }
  
    // @LINE:60
    def skipOrders(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/actions/skip")
    }
  
    // @LINE:48
    def getSystemsWithUsers(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/systemsUsers")
    }
  
    // @LINE:39
    def createOrder(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/create")
    }
  
    // @LINE:56
    def cancelOrders(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/actions/cancel")
    }
  
    // @LINE:57
    def retryOrders(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/actions/retry")
    }
  
    // @LINE:17
    def getAllowedOrderActions(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/allowed-actions")
    }
  
    // @LINE:54
    def getServiceTypes(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/serviceTypes")
    }
  
    // @LINE:42
    def view(orderId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/view/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("orderId", orderId)))
    }
  
    // @LINE:37
    def createPage(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/create")
    }
  
    // @LINE:41
    def home(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders")
    }
  
    // @LINE:47
    def getSystems(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/systems")
    }
  
    // @LINE:50
    def getOperations(): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/orders/operations")
    }
  
    // @LINE:43
    def getOrderById(orderId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/view/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("orderId", orderId)) + "/info")
    }
  
    // @LINE:38
    def clonePage(orderId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/orders/clone/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("orderId", orderId)))
    }
  
  }

  // @LINE:69
  class ReverseNotificationController(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:70
    def retryNotificationBySubscriber(notificationId:String, subscriberName:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/notifications/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("notificationId", notificationId)) + "/subscriber/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("subscriberName", subscriberName)) + "/retry")
    }
  
    // @LINE:71
    def retryNotification(notificationId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + "monitoring/notifications/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("notificationId", notificationId)) + "/retry")
    }
  
    // @LINE:69
    def getNotificationsByOrderId(orderId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "monitoring/notifications/orderId/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("orderId", orderId)))
    }
  
  }


}
