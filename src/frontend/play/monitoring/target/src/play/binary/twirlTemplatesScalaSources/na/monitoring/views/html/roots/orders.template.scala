
package na.monitoring.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.monitoring.settings.{AAAPIResources, Features}
/*2.2*/import na.monitoring.views.html.skeletons.mainSkel
/*3.2*/import na.monitoring.views.tags.feature
/*4.2*/import na.naportalbase.views.html.components.selectsComp.dynamicSelectOptionComp
/*5.2*/import na.naportalbase.views.tags.i18n
/*6.2*/import pt.alticelabs.nossis.security.views.html.authorized

object orders extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[List[String],Boolean,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(searchDateFilterByOptions: List[String], showMassiveActions: Boolean):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*30.2*/header/*30.8*/():play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*30.14*/("""
    """),format.raw/*31.5*/("""<div id="entity-header" class="fx-entity-header">

        <div id="header-fields" class="fx-entity-header-inner" data-view="search-form">
            """),format.raw/*34.25*/("""
            """),format.raw/*35.13*/("""<div class="filters-view in" data-view="search-by-filters">
                """),format.raw/*36.30*/("""
                """),format.raw/*37.17*/("""<div id="filters-col">
                    <div data-na-portal-monitoring-order-listing-filter-list data-parent-container-selector=".fx-entity-header-inner" data-store="filterStore"></div>
                </div>
                """),format.raw/*40.33*/("""
                """),format.raw/*41.17*/("""<div id="add-filters-col">
                    <div>
                        <a id="SwitchToSearchByIdView" data-page-action="SwitchToSearchByIdView">"""),_display_(/*43.99*/i18n("na.portal.monitoring.home.filters.search.by.id")),format.raw/*43.153*/("""</a>
                    </div>
                    <div class="filter-configuration" data-na-portal-monitoring-order-listing-filter-list-manager data-label='"""),_display_(/*45.128*/i18n("na.portal.monitoring.home.filters.add.fields")),format.raw/*45.180*/("""' data-store="filterStore"></div>
                </div>
            </div>

            """),format.raw/*49.31*/("""
            """),format.raw/*50.13*/("""<div class="search-by-id-view" data-na-portal-monitoring-id-search-form data-submit-action="submitAction" data-store="idSearchStore" data-view="search-by-id">
                <div class="row">
                    <div class="col-sm-12">
                        <div data-na-portal-monitoring-id-search-form-tabs data-store="idSearchStore" class="pull-left"></div>
                        <a id="SwitchToFiltersView" data-page-action="SwitchToFiltersView" class="pull-right"> """),_display_(/*54.113*/i18n("na.portal.monitoring.home.filters.search.with.multiple.fields")),format.raw/*54.182*/(""" """),format.raw/*54.183*/("""</a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <input id="id-search-input" title="" class="form-control input-sm" type="text">
                    </div>
                </div>
            </div>
        </div>


        """),format.raw/*66.33*/("""
        """),format.raw/*67.9*/("""<div data-view="search-expression">
            <div class="row" data-view="search-by-filters">
                """),format.raw/*69.30*/("""
                """),format.raw/*70.17*/("""<div class="filterExpression col-sm-12">
                    <div id="filterExpression" data-na-portal-monitoring-order-listing-filter-expression data-store="filterStore"></div>
                </div>
            </div>
            <div class="row" data-view="search-by-id">
                    """),format.raw/*75.34*/("""
                """),format.raw/*76.17*/("""<div class="filterExpression col-sm-12">
                    <div data-na-portal-monitoring-order-listing-filter-expression data-store="idSearchStore"></div>
                </div>
            </div>
        </div>

        """),format.raw/*82.21*/("""
        """),format.raw/*83.9*/("""<div id="header-search" class="fx-search-actions-container clearfix">
            """),format.raw/*84.23*/("""
            """),format.raw/*85.13*/("""<div class="pull-left">
                <form class="form-inline">
                    <div class="pull-left" style="margin-right: 10px">
                        <label> """),_display_(/*88.34*/i18n("na.portal.monitoring.search.date.filterby")),format.raw/*88.83*/(""" """),format.raw/*88.84*/("""</label>
                        """),_display_(/*89.26*/na/*89.28*/.naportalbase.views.html.components.selectsComp.selectComp.render(filterBySelectAttrs,null,null,filterBySelectData)),format.raw/*89.143*/("""
                    """),format.raw/*90.21*/("""</div>
                    <div
                        id="search-date"
                        class="form-group"
                        data-na-portal-monitoring-daterangepicker
                        data-on-change="onRangeChange"
                        data-initial-range="initialRange"
                    >
                        <div id="search-date-range">
                            <i class="fa fa-calendar"></i>
                            <input id="daterangepicker" title="" type="text" autocomplete="off"> <i class="fa fa-caret-down"></i>
                        </div>
                        <i id="daterangepicker-message">"""),_display_(/*102.58*/i18n("na.portal.monitoring.home.filters.need.to.refresh")),format.raw/*102.115*/("""</i>
                        <i class="glyphicon glyphicon-repeat"></i>
                    </div>
                </form>
            </div>
            """),format.raw/*107.24*/("""
            """),format.raw/*108.13*/("""<div class="pull-right">
                <button class="btn btn-sm btn-primary" data-page-action="search">"""),_display_(/*109.83*/i18n("na.buttons.search")),format.raw/*109.108*/("""</button>
                <button class="btn btn-default btn-sm" data-page-action="clear">"""),_display_(/*110.82*/i18n("na.buttons.clear")),format.raw/*110.106*/("""</button>
            </div>
        </div>
    </div>
""")))};def /*116.2*/horizontalSplitter/*116.20*/():play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*116.26*/("""
    """),format.raw/*117.5*/("""<div id="horizontal-splitter" class="ui-layout-pane">
        <div class="toggler">
            <a role="button">
                <span class="ui-layout-toggler-north"></span>
            </a>
        </div>
    </div>
""")))};def /*10.2*/filterBySelectData/*10.20*/ = {{
    var stringBuilder = new StringBuilder
    for(option <- searchDateFilterByOptions) {
        var optionAttributes = scala.collection.immutable.HashMap[String,String](
            "value" -> option
        )
        stringBuilder.append(dynamicSelectOptionComp.render(i18n(s"na.portal.monitoring.search.date.filterby.${option}"),optionAttributes))
    }
    Html(stringBuilder.toString())
}};def /*21.2*/filterBySelectAttrs/*21.21*/ = {{
    var map = scala.collection.immutable.HashMap[String, String](
        "id" -> "filter-by-select-box",
        "data-na-portal-select-box" -> null,
        "data-na-portal-monitoring-order-listing-search-date-filter-by-select-box" -> null
    )
    map
}};
Seq[Any](format.raw/*7.72*/("""


"""),format.raw/*19.2*/("""

"""),format.raw/*28.2*/("""

"""),format.raw/*114.2*/("""

"""),format.raw/*124.2*/("""

"""),_display_(/*126.2*/mainSkel()/*126.12*/ {_display_(Seq[Any](format.raw/*126.14*/("""
    """),format.raw/*127.5*/("""<div class="main-content-wrapper page page--monitoring-order-search" data-na-portal-monitoring-order-listing-page data-na-portal-monitoring-order-search-breadcrumb>
        """),format.raw/*128.26*/("""
        """),format.raw/*129.9*/("""<div id="header-info" class="monitoring-header">
            <h1>"""),_display_(/*130.18*/i18n("na.portal.monitoring.home.title")),format.raw/*130.57*/("""</h1>
            """),_display_(/*131.14*/feature(Features.ORDER_CREATE)/*131.44*/{_display_(Seq[Any](format.raw/*131.45*/("""
            """),_display_(/*132.14*/authorized(AAAPIResources.monitoring.X.toString())/*132.64*/{_display_(Seq[Any](format.raw/*132.65*/("""
                """),format.raw/*133.17*/("""<button class="btn btn-default button button--monitoring-order-create">
                    <i class="glyphicon glyphicon-plus"></i>
                    <x-i18n key="na.portal.monitoring.order.create.label"></x-i18n>
                </button>
            """)))}),format.raw/*137.14*/("""
            """)))}),format.raw/*138.14*/("""
        """),format.raw/*139.9*/("""</div>
        <div>
            """),format.raw/*141.25*/("""
            """),format.raw/*142.13*/("""<div id="header" data-na-portal-monitoring-order-listing-header data-store="headerStore">
                """),_display_(/*143.18*/header),format.raw/*143.24*/("""
                """),_display_(/*144.18*/horizontalSplitter),format.raw/*144.36*/("""
            """),format.raw/*145.13*/("""</div>

            """),format.raw/*147.26*/("""
            """),format.raw/*148.13*/("""<div id="splitter-content">
                <div id="entity-content">
                    """),_display_(/*150.22*/if( showMassiveActions )/*150.46*/{_display_(Seq[Any](format.raw/*150.47*/("""
                        """),format.raw/*151.25*/("""<div data-na-portal-monitoring-orders-massive-actions-dropdown></div>
                        <div data-na-portal-monitoring-orders-select-all-from-filter-notification class="notification--select-all-from-filter"></div>
                    """)))}),format.raw/*153.22*/("""
                    """),format.raw/*154.21*/("""<table
                        id="order-listing-table"
                        data-na-portal-table
                        data-na-portal-table-datatable
                        data-na-portal-table-load-using-ajax
                        data-config-url=""""),_display_(/*159.43*/na/*159.45*/.monitoring.controllers.routes.TableController.dataTableOrderMonitoring()),format.raw/*159.118*/(""""
                        data-na-portal-monitoring-order-listing-table
                        data-na-portal-monitoring-order-listing-table-store="orderListingTable"
                    ></table>
                </div>
            </div>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(searchDateFilterByOptions:List[String],showMassiveActions:Boolean): play.twirl.api.HtmlFormat.Appendable = apply(searchDateFilterByOptions,showMassiveActions)

  def f:((List[String],Boolean) => play.twirl.api.HtmlFormat.Appendable) = (searchDateFilterByOptions,showMassiveActions) => apply(searchDateFilterByOptions,showMassiveActions)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:33 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/monitoring/target/TwirlSource/na/monitoring/views/roots/orders.scala.html
                  HASH: 10362f10592af37267e4b44c597d19557f8eb894
                  MATRIX: 671->1|735->59|793->111|840->152|928->234|974->274|1352->334|1501->1113|1515->1119|1598->1125|1630->1130|1809->1293|1850->1306|1954->1395|1999->1412|2255->1656|2300->1673|2478->1824|2554->1878|2741->2037|2815->2089|2932->2196|2973->2209|3477->2685|3568->2754|3598->2755|3957->3110|3993->3119|4133->3244|4178->3261|4501->3569|4546->3586|4798->3822|4834->3831|4944->3923|4985->3936|5183->4107|5253->4156|5282->4157|5343->4191|5354->4193|5491->4308|5540->4329|6215->4976|6295->5033|6478->5198|6520->5211|6655->5318|6703->5343|6822->5434|6869->5458|6949->5517|6977->5535|7061->5541|7094->5546|7337->408|7364->426|7777->828|7805->847|8098->404|8128->825|8157->1110|8187->5514|8217->5766|8247->5769|8267->5779|8308->5781|8341->5786|8543->5976|8580->5985|8674->6051|8735->6090|8782->6109|8822->6139|8862->6140|8904->6154|8964->6204|9004->6205|9050->6222|9338->6478|9384->6492|9421->6501|9483->6546|9525->6559|9660->6666|9688->6672|9734->6690|9774->6708|9816->6721|9865->6754|9907->6767|10026->6858|10060->6882|10100->6883|10154->6908|10427->7149|10477->7170|10764->7429|10776->7431|10872->7504
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->7|38->30|38->30|40->30|41->31|44->34|45->35|46->36|47->37|50->40|51->41|53->43|53->43|55->45|55->45|59->49|60->50|64->54|64->54|64->54|76->66|77->67|79->69|80->70|85->75|86->76|92->82|93->83|94->84|95->85|98->88|98->88|98->88|99->89|99->89|99->89|100->90|112->102|112->102|117->107|118->108|119->109|119->109|120->110|120->110|124->116|124->116|126->116|127->117|134->10|134->10|143->21|143->21|151->7|154->19|156->28|158->114|160->124|162->126|162->126|162->126|163->127|164->128|165->129|166->130|166->130|167->131|167->131|167->131|168->132|168->132|168->132|169->133|173->137|174->138|175->139|177->141|178->142|179->143|179->143|180->144|180->144|181->145|183->147|184->148|186->150|186->150|186->150|187->151|189->153|190->154|195->159|195->159|195->159
                  -- GENERATED --
              */
          