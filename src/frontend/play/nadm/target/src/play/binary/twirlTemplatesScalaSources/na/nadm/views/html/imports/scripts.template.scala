
package na.nadm.views.html.imports

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object scripts extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.1*/("""<script src=""""),_display_(/*1.15*/na/*1.17*/.nadm.controllers.routes.Application.javascriptRoutes()),format.raw/*1.72*/(""""></script><script src='"""),_display_(/*1.97*/na/*1.99*/.nadm.controllers.routes.Assets.versioned("basemodule/runtime.caec9c6b7f6f9521368d.min.js")),format.raw/*1.190*/("""'></script><script src='"""),_display_(/*1.215*/na/*1.217*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-fuxi.44e71963e917fd2be2ca.min.js")),format.raw/*1.327*/("""'></script><script src='"""),_display_(/*1.352*/na/*1.354*/.nadm.controllers.routes.Assets.versioned("na-portal-assets/base-styles.9e693254edb056a100ad.min.js")),format.raw/*1.455*/("""'></script><script src='"""),_display_(/*1.480*/na/*1.482*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-bootstrap.6f86c32db7bf4ccaf010.min.js")),format.raw/*1.597*/("""'></script><script src='"""),_display_(/*1.622*/na/*1.624*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-selects.c845a1da27d2bbccf773.min.js")),format.raw/*1.737*/("""'></script><script src='"""),_display_(/*1.762*/na/*1.764*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-std-utils.ab8ed0c92f51f5eb905e.min.js")),format.raw/*1.879*/("""'></script><script src='"""),_display_(/*1.904*/na/*1.906*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-tables.bc173ec447d65947d31a.min.js")),format.raw/*1.1018*/("""'></script><script src='"""),_display_(/*1.1043*/na/*1.1045*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/vendors-jquery.c69a6ab040df2ef632e6.min.js")),format.raw/*1.1157*/("""'></script><script src='"""),_display_(/*1.1182*/na/*1.1184*/.nadm.controllers.routes.Assets.versioned("na-portal-assets-vendors/common.a53001b54dbc3b115cc4.min.js")),format.raw/*1.1288*/("""'></script><script src='"""),_display_(/*1.1313*/na/*1.1315*/.nadm.controllers.routes.Assets.versioned("utils/utils.88e56ee512f1a3af972d.min.js")),format.raw/*1.1399*/("""'></script><script src='"""),_display_(/*1.1424*/na/*1.1426*/.nadm.controllers.routes.Assets.versioned("basemodule/basemodule.823a1b10758c828389cd.min.js")),format.raw/*1.1520*/("""'></script><script src='"""),_display_(/*1.1545*/na/*1.1547*/.nadm.controllers.routes.Assets.versioned("nadm/nadm.43e589c610976233d045.min.js")),format.raw/*1.1629*/("""'></script>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/imports/scripts.scala.html
                  HASH: 65bf31b090352818fb55c007b3135126da6f5680
                  MATRIX: 1048->0|1088->14|1098->16|1173->71|1224->96|1234->98|1346->189|1398->214|1409->216|1540->326|1592->351|1603->353|1725->454|1777->479|1788->481|1924->596|1976->621|1987->623|2121->736|2173->761|2184->763|2320->878|2372->903|2383->905|2517->1017|2570->1042|2582->1044|2716->1156|2769->1181|2781->1183|2907->1287|2960->1312|2972->1314|3078->1398|3131->1423|3143->1425|3259->1519|3312->1544|3324->1546|3428->1628
                  LINES: 33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1|33->1
                  -- GENERATED --
              */
          