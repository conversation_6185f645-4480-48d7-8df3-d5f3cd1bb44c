
package na.nadm.views.html.components.equipmentAssociations

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.utils.FormBuilderUtil
/*2.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*3.2*/import na.naportalbase.views.tags.i18n
/*4.2*/import pt.ptinovacao.naportal.nadm.model.{ResourceConfigurationIf, ResourceTemplateIf}
/*6.2*/import java.util.Map.Entry
/*7.2*/import scala.collection.immutable

object createWizardSectionType extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[ResourceConfigurationIf,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*9.2*/(configuration: ResourceConfigurationIf):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*22.2*/selectOptions/*22.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*22.19*/("""
	"""),_display_(/*23.3*/for(option: Entry[String, ResourceTemplateIf] <- configuration.getTemplates.entrySet()) yield /*23.90*/ {_display_(Seq[Any](format.raw/*23.92*/("""
		"""),format.raw/*24.3*/("""<option value=""""),_display_(/*24.19*/option/*24.25*/.getKey),format.raw/*24.32*/("""">"""),_display_(/*24.35*/{FormBuilderUtil.parseI18n(option.getValue.getI18n)}),format.raw/*24.87*/("""</option>
	""")))}),format.raw/*25.3*/("""
""")))};def /*11.2*/elementAttr/*11.13*/ = {{
	immutable.HashMap[String, String] (
		"id" -> "resource-type",
		"name" -> "resource-type",
		"data-ng-model" -> "associationInfo.type",
		"data-na-portal-select-box" -> null,
		"data-na-portal-required" -> null,
		"class" -> "form-control",
		"select-width" -> "280px"
	)
}};
Seq[Any](format.raw/*9.42*/("""

"""),format.raw/*21.2*/("""
"""),format.raw/*26.2*/("""


"""),format.raw/*29.1*/("""<h4><span class="fx-wizardstep-number">1</span> | """),_display_(/*29.52*/i18n("na.wizard.step", "1")),format.raw/*29.79*/("""</h4>
<p class="fx-required-fields-message">"""),_display_(/*30.40*/i18n("na.fill.all.required.fields")),format.raw/*30.75*/("""</p>

<div class="fx-wizardsteps-content-block">
	<div class="form-horizontal">
		<div class="form-group">
			<label for="resource-type" class="col-sm-3 control-label fx-required-field">"""),_display_(/*35.81*/i18n("na.type")),format.raw/*35.96*/("""</label>
			<div class="col-sm-9" data-na-portal-field-notification data-na-portal-input-notification-hr>
				"""),_display_(/*37.6*/selectComp/*37.16*/.render(elementAttr, null, null, selectOptions)),format.raw/*37.63*/("""
			"""),format.raw/*38.4*/("""</div>
		</div>
	</div>
</div>
"""))
      }
    }
  }

  def render(configuration:ResourceConfigurationIf): play.twirl.api.HtmlFormat.Appendable = apply(configuration)

  def f:((ResourceConfigurationIf) => play.twirl.api.HtmlFormat.Appendable) = (configuration) => apply(configuration)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/equipmentAssociations/createWizardSectionType.scala.html
                  HASH: 8c91598afda7333d2a3ae33584de459d8f474a40
                  MATRIX: 692->1|736->39|811->108|857->148|951->237|985->265|1358->301|1477->638|1499->651|1580->655|1609->658|1712->745|1752->747|1782->750|1825->766|1840->772|1868->779|1898->782|1971->834|2013->846|2038->344|2058->355|2369->341|2398->636|2426->848|2456->851|2534->902|2582->929|2654->974|2710->1009|2924->1196|2960->1211|3097->1322|3116->1332|3184->1379|3215->1383
                  LINES: 24->1|25->2|26->3|27->4|28->6|29->7|34->9|38->22|38->22|40->22|41->23|41->23|41->23|42->24|42->24|42->24|42->24|42->24|42->24|43->25|44->11|44->11|55->9|57->21|58->26|61->29|61->29|61->29|62->30|62->30|67->35|67->35|69->37|69->37|69->37|70->38
                  -- GENERATED --
              */
          