
package na.nadm.views.html.components.common.form

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n

object associationsNameField extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.1*/("""<div class="form-group">
    <label for="associationName">"""),_display_(/*4.35*/{i18n("na.portal.nadm.field.name.label")}),format.raw/*4.76*/("""</label>
    <input class="form-control input-sm"
    id="associationName"
    placeholder=""""),_display_(/*7.19*/{i18n("na.portal.nadm.field.name.label")}),format.raw/*7.60*/(""""
    data-field="associationName"/>
</div>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/form/associationsNameField.scala.html
                  HASH: 22efebd87fee4f2e31b1d84b0f3e7c5068dcc245
                  MATRIX: 682->1|1123->41|1208->100|1269->141|1388->234|1449->275
                  LINES: 24->1|34->3|35->4|35->4|38->7|38->7
                  -- GENERATED --
              */
          