
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.views.html.components.clientEquipmentModel.clientEquipModelSearch
/*4.2*/import na.nadm.views.html.components.common.lateralNav
/*5.2*/import na.nadm.views.html.skeletons.mainSkel
/*6.2*/import na.naportalbase.views.tags.i18n

object clientDetailsPage extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,String,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(context: String, clientId:String, tab:String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*10.2*/navBar/*10.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.12*/("""
"""),_display_(/*11.2*/lateralNav/*11.12*/.render(context, NavContext.CLIENT, nadmSettings)),format.raw/*11.61*/("""
""")))};
Seq[Any](format.raw/*8.76*/("""

"""),format.raw/*12.2*/("""

"""),_display_(/*14.2*/mainSkel(context)/*14.19*/ {_display_(Seq[Any](format.raw/*14.21*/("""
	"""),format.raw/*15.2*/("""<div class="layout--page-with-navbar">
		"""),_display_(/*16.4*/navBar),format.raw/*16.10*/("""
		"""),format.raw/*17.3*/("""<div class="fx-main-content-wrapper fx-with-main-nav nadm-client__client-view-page"
			data-ng-controller="NaPortalNadmCommonController"
			data-na-portal-nadm-client-details-page
			data-client-id=""""),_display_(/*20.21*/clientId),format.raw/*20.29*/("""">
			<div class="fx-entity-header">
				<div class="fx-entity-header-info">
					<span class="fx-entity-header-icon">
						<i class="glyphicon glyphicon-user"></i>
					</span>
					<div class="fx-entity-header-title">
						<h1>
							<span is="x-dynamic-span" data-truncated-at="middle">"""),_display_(/*28.62*/clientId),format.raw/*28.70*/("""</span>
						</h1>
					</div>
				</div>
			</div>
			<div class="fx-entity-info">
				<x-tab-container>
					<x-tab label=""""),_display_(/*35.21*/i18n("na.portal.nadm.tabname.equipModel")),format.raw/*35.62*/("""">
						<div class="tab-pane">
						"""),_display_(/*37.8*/clientEquipModelSearch/*37.30*/.render(context, clientId)),format.raw/*37.56*/("""
						"""),format.raw/*38.7*/("""</div>
					</x-tab>
				</x-tab-container>
			</div>
		</div>
	</div>
""")))}),format.raw/*44.2*/("""




"""))
      }
    }
  }

  def render(context:String,clientId:String,tab:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,clientId,tab,nadmSettings)

  def f:((String,String,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,clientId,tab,nadmSettings) => apply(context,clientId,tab,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/clientDetailsPage.scala.html
                  HASH: 8594f1d8c2caaaea6ea5878edb60acdd69c39f28
                  MATRIX: 665->1|721->51|765->89|853->171|915->227|967->273|1349->314|1502->391|1516->397|1597->401|1625->403|1644->413|1714->462|1755->388|1784->464|1813->467|1839->484|1879->486|1908->488|1976->530|2003->536|2033->539|2260->739|2289->747|2609->1040|2638->1048|2792->1175|2854->1216|2919->1255|2950->1277|2997->1303|3031->1310|3133->1382
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->10|38->10|40->10|41->11|41->11|41->11|43->8|45->12|47->14|47->14|47->14|48->15|49->16|49->16|50->17|53->20|53->20|61->28|61->28|68->35|68->35|70->37|70->37|70->37|71->38|77->44
                  -- GENERATED --
              */
          