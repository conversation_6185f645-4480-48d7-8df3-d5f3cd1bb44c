
package na.nadm.views.html.components.common.tableform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.utils.FormBuilderUtil
/*2.2*/import na.naportalbase.views.html.components.selectsComp.selectComp
/*3.2*/import pt.ptinovacao.naportal.components.{Option, SelectableUiComponent}
/*5.2*/import scala.collection.mutable

object selectableUiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[SelectableUiComponent,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(uiComponent: SelectableUiComponent, angularModel: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*31.2*/selectOptions/*31.15*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*31.19*/("""
    """),_display_(/*32.6*/for(option: Option <- uiComponent.getOptions) yield /*32.51*/ {_display_(Seq[Any](format.raw/*32.53*/("""
        """),format.raw/*33.9*/("""<option value=""""),_display_(/*33.25*/option/*33.31*/.getValue),format.raw/*33.40*/(""""><i class="fa fa-undo"></i>"""),_display_(/*33.69*/{
            val i18nText = FormBuilderUtil.parseI18n(option.getI18n)
            if (i18nText != "") {
                i18nText
            } else {
                option.getValue
    }
}),format.raw/*40.2*/("""
        """),format.raw/*41.9*/("""</option>
    """)))}),format.raw/*42.6*/("""
""")))};def /*9.2*/defaultValueIndex/*9.19*/ = {{ if(uiComponent.getDefaultValueIndex != null) uiComponent.getDefaultValueIndex.toString  }};def /*11.2*/componentName/*11.15*/ = {{ FormBuilderUtil.parseI18n(uiComponent.getI18n)}};def /*13.2*/elementAttr/*13.13*/ = {{
    var map = mutable.HashMap[String, String] (
        "id" -> uiComponent.getElementId,
        "name" -> uiComponent.getElementId,
        "data-na-portal-nadm-table-select-box" -> null,
	    "data-ng-model" -> "selectableUiModel",
        "data-na-portal-select-box" -> null,
        "data-show-icons" -> null,
        "class" -> "form-control",
        "select-width" -> "element"
    )

    if (uiComponent.getRequired != null && uiComponent.getRequired) {
        map.put("data-na-portal-required", null)
    }

    collection.immutable.HashMap(map.toSeq:_*)
}};def /*45.2*/initialOptionAttributes/*45.25*/ = {{
	var index = 0
	var inheritValue:String = "inherit" + 0
	var isUniqueValue:Boolean = false
	def updateUniqueValue {
		isUniqueValue = true
		for(option: Option <- uiComponent.getOptions){
			if(inheritValue.equals(option.getValue)){
				isUniqueValue = false
				return
			}
		}
	}
	while(!isUniqueValue){
		inheritValue = "inherit" + index
		index = index + 1
		updateUniqueValue
	}
	collection.immutable.HashMap[String, String](
		"class" -> "inherit-option",
		"value" -> inheritValue
	)}};
Seq[Any](format.raw/*7.60*/("""

"""),format.raw/*9.114*/("""

"""),format.raw/*11.68*/("""

"""),format.raw/*30.2*/("""
"""),format.raw/*43.2*/("""

"""),format.raw/*66.4*/("""

"""),format.raw/*68.1*/("""<div data-na-portal-field-notification data-na-portal-input-notification-hr>
"""),_display_(/*69.2*/uiComponent/*69.13*/.getType/*69.21*/ match/*69.27*/ {/*70.9*/case SelectableUiComponent.Type.selectBox =>/*70.53*/ {_display_(Seq[Any](format.raw/*70.55*/("""
		        """),_display_(/*71.12*/selectComp/*71.22*/.render(elementAttr, null, initialOptionAttributes, selectOptions)),format.raw/*71.88*/("""
        """)))}/*73.9*/case SelectableUiComponent.Type.checkBox =>/*73.52*/ {}/*75.9*/case SelectableUiComponent.Type.radioButton =>/*75.55*/ {}/*77.9*/case _ =>/*77.18*/ {}}),format.raw/*78.6*/("""
"""),format.raw/*79.1*/("""</div>

"""))
      }
    }
  }

  def render(uiComponent:SelectableUiComponent,angularModel:String): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent,angularModel)

  def f:((SelectableUiComponent,String) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent,angularModel) => apply(uiComponent,angularModel)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/tableform/selectableUiComponent.scala.html
                  HASH: 1e28357bdb743f995c51d2e40f3590da2d31f851
                  MATRIX: 687->1|731->39|806->108|886->183|1260->217|1397->1048|1419->1061|1500->1065|1532->1071|1593->1116|1633->1118|1669->1127|1712->1143|1727->1149|1757->1158|1813->1187|2023->1377|2059->1386|2104->1401|2128->278|2153->295|2262->393|2284->406|2351->462|2371->473|2958->1406|2990->1429|3517->275|3547->390|3577->459|3606->1046|3634->1403|3663->1926|3692->1928|3796->2006|3816->2017|3833->2025|3848->2031|3858->2042|3911->2086|3951->2088|3990->2100|4009->2110|4096->2176|4124->2195|4176->2238|4187->2251|4242->2297|4253->2310|4271->2319|4295->2328|4323->2329
                  LINES: 24->1|25->2|26->3|27->5|32->7|36->31|36->31|38->31|39->32|39->32|39->32|40->33|40->33|40->33|40->33|40->33|47->40|48->41|49->42|50->9|50->9|50->11|50->11|50->13|50->13|67->45|67->45|89->7|91->9|93->11|95->30|96->43|98->66|100->68|101->69|101->69|101->69|101->69|101->70|101->70|101->70|102->71|102->71|102->71|103->73|103->73|103->75|103->75|103->77|103->77|103->78|104->79
                  -- GENERATED --
              */
          