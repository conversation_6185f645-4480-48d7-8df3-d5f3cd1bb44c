
package na.nadm.views.html.components.clientEquipmentModel

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.AAAPIResources
/*2.2*/import na.nadm.views.html.components.clientEquipmentModel.leftSideForm
/*3.2*/import pt.alticelabs.nossis.security.views.html.authorized

object clientEquipModelSearch extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(context: String, clientId: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*5.37*/("""


"""),format.raw/*8.1*/("""<div data-na-portal-nadm-side-bar-search data-context="clientEquipmentModel">
    <x-splitter class="splitter splitter--nadm-search">
        <form slot="left" class="search-sidebar">
            """),_display_(/*11.14*/leftSideForm/*11.26*/.render(context)),format.raw/*11.42*/("""
        """),format.raw/*12.9*/("""</form>
        <div id="fx-splitter-content-inner" slot="right">

            <div id="entity-content" class="fx-entity-info">
                """),_display_(/*16.18*/authorized(AAAPIResources.device(context).C().toString)/*16.73*/ {_display_(Seq[Any](format.raw/*16.75*/("""
                    """),format.raw/*17.21*/("""<div class="btn-group pull-right" data-na-portal-nadm-client-equip-model-add-button style="margin-bottom: 5px"></div>
                """)))}),format.raw/*18.18*/("""
                """),format.raw/*19.17*/("""<table id="datatableClientEditEquimentModel" class="table table-striped table-hover"
                data-na-portal-table
                data-na-portal-table-datatable
                data-na-portal-table-load-using-ajax
                data-config-url=""""),_display_(/*23.35*/na/*23.37*/.nadm.controllers.routes.TableConfigs.clientEquipmentModel(context, clientId)),format.raw/*23.114*/(""""
                data-na-portal-nadm-client-equip-model-table>
                </table>
            </div>

        </div>
        <div slot="collapsed-left">
            <div class="fx-info-sidebar-collapsed left">
                <div class="fx-sidebar-header search-sidebar__header">
                    <span class="fx-entity-header-icon">
                        <i class="fa fa-search"></i>
                    </span>
                </div>
            </div>
        </div>
    </x-splitter>

</div>
"""))
      }
    }
  }

  def render(context:String,clientId:String): play.twirl.api.HtmlFormat.Appendable = apply(context,clientId)

  def f:((String,String) => play.twirl.api.HtmlFormat.Appendable) = (context,clientId) => apply(context,clientId)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/clientEquipmentModel/clientEquipModelSearch.scala.html
                  HASH: f3e19087f2753c2ce1cd93439aaeec6293e224be
                  MATRIX: 691->1|737->42|815->115|1202->178|1332->213|1364->219|1591->419|1612->431|1649->447|1686->457|1862->606|1926->661|1966->663|2016->685|2183->821|2229->839|2516->1099|2527->1101|2626->1178
                  LINES: 24->1|25->2|26->3|31->5|36->5|39->8|42->11|42->11|42->11|43->12|47->16|47->16|47->16|48->17|49->18|50->19|54->23|54->23|54->23
                  -- GENERATED --
              */
          