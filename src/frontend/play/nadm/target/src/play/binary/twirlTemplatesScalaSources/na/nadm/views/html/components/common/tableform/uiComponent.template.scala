
package na.nadm.views.html.components.common.tableform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*3.2*/import na.nadm.views.html.components.common.tableform.{dateUiComponent, labelUiComponent, selectableUiComponent, staticLabelUiComponent, textUiComponent}
/*4.2*/import pt.ptinovacao.naportal.components.{DateUiComponent, SelectableUiComponent, TextUiComponent, UiComponent}
/*6.2*/import java.lang.Boolean.FALSE

object uiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[UiComponent,ViewType,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(
        uiComponent: UiComponent,
        viewContext: ViewType,
        context: String,
        nadmSettings: NadmSettings
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*15.2*/angularModel/*15.14*/ = {{"model.currentValue"}};def /*16.2*/isEditable/*16.12*/ = {{!FALSE.equals(uiComponent.getEditable)}};
Seq[Any](format.raw/*13.2*/("""

"""),format.raw/*15.40*/("""
"""),format.raw/*16.56*/("""

"""),format.raw/*18.1*/("""<div>
	<div data-ng-if="!viewMode">
        <div class="form-group">
            """),_display_(/*21.14*/{
                viewContext match {
                    case ViewType.CREATE | ViewType.EDIT if isEditable =>
                        uiComponent match {
                            case textComp: TextUiComponent =>
                                textUiComponent.render(textComp, angularModel, context, nadmSettings)
                            case dateComp: DateUiComponent =>
                                dateUiComponent.render(dateComp, angularModel)
                            case selectableComp: SelectableUiComponent =>
                                selectableUiComponent.render(selectableComp, angularModel)
                            case _ =>
                        }
                    case _ =>
                }
            }),format.raw/*35.14*/("""
        """),format.raw/*36.9*/("""</div>
    </div>
	<div data-ng-if="viewMode">
		"""),_display_(/*39.4*/{
            viewContext match {
                case ViewType.CREATE | ViewType.EDIT if !isEditable =>
                    staticLabelUiComponent.render(uiComponent)
                case ViewType.CREATE | ViewType.EDIT if isEditable =>
                    labelUiComponent.render(uiComponent)
                case ViewType.VIEW =>
                    staticLabelUiComponent.render(uiComponent)
            }
        }),format.raw/*48.10*/("""
    """),format.raw/*49.5*/("""</div>
</div>"""))
      }
    }
  }

  def render(uiComponent:UiComponent,viewContext:ViewType,context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent,viewContext,context,nadmSettings)

  def f:((UiComponent,ViewType,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent,viewContext,context,nadmSettings) => apply(uiComponent,viewContext,context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/tableform/uiComponent.scala.html
                  HASH: 56ee4a21eaa7a79ff5c657786a2c3ab59beb49fb
                  MATRIX: 687->1|731->39|784->86|945->241|1064->355|1439->388|1646->519|1667->531|1707->559|1726->569|1800->516|1830->557|1859->613|1888->615|1997->697|2769->1448|2805->1457|2881->1507|3321->1926|3353->1931
                  LINES: 24->1|25->2|26->3|27->4|28->6|33->8|42->15|42->15|42->16|42->16|43->13|45->15|46->16|48->18|51->21|65->35|66->36|69->39|78->48|79->49
                  -- GENERATED --
              */
          