
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.components.common.form.HelperFieldDrawingAttributes
/*5.2*/import na.nadm.views.html.components.common.detail.headerButtons
/*6.2*/import na.nadm.views.html.components.common.lateralNav
/*7.2*/import na.nadm.views.html.skeletons.mainSkel
/*8.2*/import na.naportalbase.views.html.components.selectsComp.{dynamicSelectOptionComp, selectComp}
/*9.2*/import na.naportalbase.views.tags.i18n
/*10.2*/import play.twirl.api
/*12.2*/import scala.collection.immutable

object serviceCreate extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,List[String],ViewType,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*14.2*/(context: String, serviceTypes: List[String], viewContext: ViewType, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*16.2*/navBar/*16.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*16.12*/("""
"""),_display_(/*17.2*/lateralNav/*17.12*/.render(context, NavContext.SERVICE, nadmSettings)),format.raw/*17.62*/("""
""")))};def /*41.2*/headerAction/*41.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*41.18*/("""
"""),_display_(/*42.2*/headerButtons/*42.15*/.render(context, viewContext)),format.raw/*42.44*/("""
""")))};def /*45.2*/rightSide/*45.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*45.15*/("""
    """),format.raw/*46.5*/("""<div id="fx-splitter-content" class="fx-splitter-content">
        <div class="fx-splitter-content-inner">
        </div>
    </div>
""")))};def /*53.2*/identificationBlock/*53.21*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*53.25*/("""
    """),format.raw/*54.5*/("""<div class="form-group">
        <label for="type" class="col-sm-2 control-label">"""),_display_(/*55.59*/i18n("na.portal.nadm.type")),format.raw/*55.86*/("""</label>
        <div class="col-sm-8">
        """),_display_(/*57.10*/selectComp/*57.20*/.render(
            serviceTypeSelectAttributes,
            null,
            null,
            serviceTypeSelectData
        )),format.raw/*62.10*/("""
        """),format.raw/*63.9*/("""</div>
    </div>
    <div class="form-group">
        <div class="col-sm-12 ">
            <a class="btn btn-default btn-sm disabled ng-scope pull-right" data-ng-click="characterizeService()" data-na-portal-nadm-commons-characterize-button id="button-characterize">
                <i class="fa fa-angle-double-down"></i>
                """),_display_(/*69.18*/i18n("na.portal.nadm.characterize.service")),format.raw/*69.61*/("""
            """),format.raw/*70.13*/("""</a>
        </div>
    </div>
""")))};def /*75.2*/characteristicsBlock/*75.22*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*75.26*/("""
    """),format.raw/*76.5*/("""<div id="characteristics">
        <div class="alert alert-info">
            <div class="fx-alert-icon">
            </div>
            <div class="fx-alert-desc">
                <strong>"""),_display_(/*81.26*/i18n("na.portal.nadm.characterize.after.fill")),format.raw/*81.72*/("""</strong>
            </div>
        </div>
    </div>
""")))};def /*20.2*/serviceTypeSelectAttributes/*20.29*/ = {{
    var map = HelperFieldDrawingAttributes.serviceTypeSelectAttributes("createCtrl")
    immutable.HashMap(map.toSeq: _*)
}};def /*25.2*/serviceTypeSelectData/*25.23*/ = {{
    var list = serviceTypes
    var stringBuilder = new StringBuilder
    for(serviceType <- list) {
        val optionLabel = i18n(context + "." + serviceType)
        val optionAttributes = immutable.HashMap[String, String](
            "name" -> serviceType,
            "id" -> serviceType,
            "value" -> serviceType
        )
        stringBuilder.append(dynamicSelectOptionComp.render(optionLabel, optionAttributes))
    }
    api.Html(stringBuilder.toString())
}};
Seq[Any](format.raw/*14.98*/("""

"""),format.raw/*18.2*/("""

"""),format.raw/*23.2*/("""

"""),format.raw/*38.2*/("""


"""),format.raw/*43.2*/("""

"""),format.raw/*50.2*/("""


"""),format.raw/*73.2*/("""

"""),format.raw/*85.2*/("""

"""),_display_(/*87.2*/mainSkel(context)/*87.19*/ {_display_(Seq[Any](format.raw/*87.21*/("""
    """),format.raw/*88.5*/("""<div class="layout--page-with-navbar">
        """),_display_(/*89.10*/navBar),format.raw/*89.16*/("""
        """),format.raw/*90.9*/("""<div data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-service-create-page >
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class="fa fa-cogs"></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1>
                        """),_display_(/*98.26*/i18n("na.portal.nadm.create.service")),format.raw/*98.63*/("""
                        """),format.raw/*99.25*/("""</h1>
                    </div>
                </div>
                <div class="fx-entity-header-actions">
                """),_display_(/*103.18*/headerAction),format.raw/*103.30*/("""
                """),format.raw/*104.17*/("""</div>
            </div>
            <div id="overLay">
                <div class="fx-entity-info" data-context="serviceModel">
                    <x-collapsible class="fx-section" header=""""),_display_(/*108.64*/i18n("nadm.detail.section.identification")),format.raw/*108.106*/("""">
                        <div class="form-horizontal">
                        """),_display_(/*110.26*/identificationBlock),format.raw/*110.45*/("""
                        """),format.raw/*111.25*/("""</div>
                    </x-collapsible >
                    <x-collapsible class="fx-section" header=""""),_display_(/*113.64*/i18n("nadm.detail.section.characteristics")),format.raw/*113.107*/("""">
                        <div class="form-horizontal">
                        """),_display_(/*115.26*/characteristicsBlock),format.raw/*115.46*/("""
                        """),format.raw/*116.25*/("""</div>
                    </x-collapsible>
                </div>
            </div>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(context:String,serviceTypes:List[String],viewContext:ViewType,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,serviceTypes,viewContext,nadmSettings)

  def f:((String,List[String],ViewType,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,serviceTypes,viewContext,nadmSettings) => apply(context,serviceTypes,viewContext,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/serviceCreate.scala.html
                  HASH: 2d712ab5f695d1d33bcc102e63b3e3657a882cec
                  MATRIX: 665->1|721->52|765->91|818->139|898->214|970->281|1032->338|1084->385|1186->482|1233->523|1263->549|1645->587|1820->688|1834->694|1915->698|1944->701|1963->711|2034->761|2060->1458|2081->1470|2162->1474|2191->1477|2213->1490|2263->1519|2289->1527|2307->1536|2388->1540|2421->1546|2582->1691|2610->1710|2691->1714|2724->1720|2835->1804|2883->1831|2961->1882|2980->1892|3135->2026|3172->2036|3545->2382|3609->2425|3651->2439|3709->2479|3738->2499|3819->2503|3852->2509|4074->2704|4141->2750|4224->769|4260->796|4406->933|4436->954|4964->683|4995->764|5026->928|5057->1451|5090->1522|5121->1684|5154->2474|5185->2810|5216->2815|5242->2832|5282->2834|5315->2840|5391->2889|5418->2895|5455->2905|5929->3352|5987->3389|6041->3415|6201->3547|6235->3559|6282->3577|6507->3774|6572->3816|6684->3900|6725->3919|6780->3945|6918->4055|6984->4098|7096->4182|7138->4202|7193->4228
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|33->10|34->12|39->14|43->16|43->16|45->16|46->17|46->17|46->17|47->41|47->41|49->41|50->42|50->42|50->42|51->45|51->45|53->45|54->46|58->53|58->53|60->53|61->54|62->55|62->55|64->57|64->57|69->62|70->63|76->69|76->69|77->70|80->75|80->75|82->75|83->76|88->81|88->81|92->20|92->20|95->25|95->25|109->14|111->18|113->23|115->38|118->43|120->50|123->73|125->85|127->87|127->87|127->87|128->88|129->89|129->89|130->90|138->98|138->98|139->99|143->103|143->103|144->104|148->108|148->108|150->110|150->110|151->111|153->113|153->113|155->115|155->115|156->116
                  -- GENERATED --
              */
          