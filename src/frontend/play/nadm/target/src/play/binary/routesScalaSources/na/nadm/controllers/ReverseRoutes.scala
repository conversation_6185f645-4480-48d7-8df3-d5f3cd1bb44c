// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/conf/nadm.routes
// @DATE:Wed Jul 02 14:15:43 WEST 2025

import play.api.mvc.Call


import _root_.controllers.Assets.Asset

// @LINE:2
package na.nadm.controllers {

  // @LINE:23
  class ReverseDetail(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:55
    def updateEquipment(context:String, clientId:String, equipmentId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/characteristics/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("equipmentId", equipmentId)))
    }
  
    // @LINE:69
    def createService(context:String, serviceType:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/service/create/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("serviceType", serviceType)))
    }
  
    // @LINE:43
    def createClientEquipment(context:String, manufacturer:String, model:String, version:String, clientId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/clientequipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)))
    }
  
    // @LINE:73
    def updateService(context:String, clientName:String, serviceName:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/service/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientName", clientName)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("serviceName", serviceName)))
    }
  
    // @LINE:36
    def updateClientEquipment(context:String, clientId:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:39
    def removeClientEquipment(context:String, clientId:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("DELETE", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:40
    def removeClient(context:String, clientId:String): Call = {
      
      Call("DELETE", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)))
    }
  
    // @LINE:56
    def updateEquipmentAssociation(context:String, equipmentId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/association/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("equipmentId", equipmentId)))
    }
  
    // @LINE:74
    def removeService(context:String, clientName:String, serviceName:String): Call = {
      
      Call("DELETE", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/service/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientName", clientName)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("serviceName", serviceName)))
    }
  
    // @LINE:23
    def getGlobalEquipmentDetails(context:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/globalequipment/details/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:24
    def updateGlobalEquipment(context:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/globalequipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:37
    def createClient(context:String, clientId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/create")
    }
  
    // @LINE:54
    def createEquipment(context:String, manufacturer:String, model:String, version:String, clientId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/create/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)))
    }
  
    // @LINE:25
    def updateGlobalEquipmentAndTemplate(context:String, manufacturer:String, model:String, version:String, firmwareFilename:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/globalequipment/template/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("firmwareFilename", firmwareFilename)))
    }
  
    // @LINE:62
    def removeEquipment(context:String, equipmentId:String): Call = {
      
      Call("DELETE", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("equipmentId", equipmentId)))
    }
  
    // @LINE:26
    def removeGlobalEquipment(context:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("DELETE", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/globalequipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
  }

  // @LINE:16
  class ReverseAssets(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:16
    def versioned(file:Asset): Call = {
      implicit lazy val _rrc = new play.core.routing.ReverseRouteContext(Map(("path", "/public"))); _rrc
      Call("GET", _prefix + { _defaultPrefix } + "nadm/assets/" + implicitly[play.api.mvc.PathBindable[Asset]].unbind("file", file))
    }
  
  }

  // @LINE:59
  class ReverseExport(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:59
    def getEquipmentsCsv(context:String, paramsOnBase64:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/csv/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("paramsOnBase64", paramsOnBase64)))
    }
  
  }

  // @LINE:63
  class ReverseOperation(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:114
    def deleteFirmwareFile(context:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/delete/firmwarefile/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:107
    def getEquipmentOperations(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/operation/operations/equipment")
    }
  
    // @LINE:106
    def getMappingModel(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/operation/mappingmodel")
    }
  
    // @LINE:115
    def executeOperation(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/operation")
    }
  
    // @LINE:113
    def uploadFirmwareFile(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/upload/firmwarefile")
    }
  
    // @LINE:110
    def getMassiveOperationForm(context:String, operationId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/operation/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("operationId", operationId)) + "/form/massive")
    }
  
    // @LINE:116
    def executeBulkOperation(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/bulkoperation")
    }
  
    // @LINE:108
    def getGlobalEquipmentOperations(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/operation/operations/globalequipment")
    }
  
    // @LINE:109
    def getOperationForm(context:String, operationId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/operation/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("operationId", operationId)) + "/form/individual")
    }
  
    // @LINE:63
    def executeBulkDelete(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/delete")
    }
  
  }

  // @LINE:84
  class ReverseTag(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:84
    def tags(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/tags")
    }
  
    // @LINE:85
    def equipmentTagSearch(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment-tags")
    }
  
    // @LINE:86
    def equipmentTagSearchAssociatedTagsContinuation(context:String, uuid:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment-tags/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("uuid", uuid)))
    }
  
    // @LINE:87
    def manageEquipmentsTags(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment-tags/manage")
    }
  
  }

  // @LINE:2
  class ReverseApplication(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:6
    def jsSettings(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/jsSettings")
    }
  
    // @LINE:2
    def index(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/home")
    }
  
    // @LINE:13
    def getModuleAngularScripts(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "nadm/imports/jsscripts")
    }
  
    // @LINE:5
    def resume(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/resume")
    }
  
    // @LINE:10
    def javascriptRoutes(): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + "nadm/assets/javascripts/routes")
    }
  
  }

  // @LINE:90
  class ReverseTableConfigs(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:91
    def globalEquipment(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/table-configs/globalequipment")
    }
  
    // @LINE:96
    def equipmentAssociationsTypeServices(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/table-configs/equipmentassociationtypeservices")
    }
  
    // @LINE:93
    def clientEquipmentModel(context:String, clientId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/table-configs/clientEquipmentModel" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("clientId", clientId)))))
    }
  
    // @LINE:92
    def client(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/table-configs/client")
    }
  
    // @LINE:90
    def equipment(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/table-configs/equipment")
    }
  
    // @LINE:94
    def service(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/table-configs/service")
    }
  
    // @LINE:95
    def equipmentassociations(context:String, equipmentId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/table-configs/equipmentassociations" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[String]].unbind("equipmentId", equipmentId)))))
    }
  
  }

  // @LINE:77
  class ReverseSearch(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:82
    def searchClientEquipmentModel(context:String, clientId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/searchEquipmentModel")
    }
  
    // @LINE:81
    def searchService(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/searchservice")
    }
  
    // @LINE:79
    def searchEquipment(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/searchequipment")
    }
  
    // @LINE:80
    def searchEquipmentAssociations(context:String, equipmentId:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/searchequipmentassociations/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("equipmentId", equipmentId)))
    }
  
    // @LINE:101
    def catalogModels(context:String, multipleChoices:Boolean): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/catalog/models" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[Boolean]].unbind("multipleChoices", multipleChoices)))))
    }
  
    // @LINE:77
    def searchGlobalEquipment(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/searchglobalequipment")
    }
  
    // @LINE:78
    def searchClient(context:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/searchclient")
    }
  
    // @LINE:83
    def clientNames(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/clients/names")
    }
  
    // @LINE:100
    def catalogManufacturers(context:String, multipleChoices:Boolean): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/catalog/manufacturers" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[Boolean]].unbind("multipleChoices", multipleChoices)))))
    }
  
    // @LINE:102
    def catalogVersions(context:String, multipleChoices:Boolean): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/catalog/versions" + play.core.routing.queryString(List(Some(implicitly[play.api.mvc.QueryStringBindable[Boolean]].unbind("multipleChoices", multipleChoices)))))
    }
  
  }

  // @LINE:119
  class ReverseFilters(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:120
    def editFilter(context:String, filterContext:String, filterName:String): Call = {
      
      Call("PUT", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/filters/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterContext", filterContext)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:119
    def saveFilter(context:String, filterContext:String): Call = {
      
      Call("POST", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/filters/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterContext", filterContext)))
    }
  
    // @LINE:122
    def getAllFilters(context:String, filterContext:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/filters/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterContext", filterContext)))
    }
  
    // @LINE:123
    def getSelectedFilter(context:String, filterContext:String, filterName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/filters/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterContext", filterContext)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
    // @LINE:121
    def deleteFilter(context:String, filterContext:String, filterName:String): Call = {
      
      Call("DELETE", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/filters/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterContext", filterContext)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("filterName", filterName)))
    }
  
  }

  // @LINE:20
  class ReverseNavigation(_prefix: => String) {
    def _defaultPrefix: String = {
      if (_prefix.endsWith("/")) "" else "/"
    }

  
    // @LINE:33
    def clientEquipmentDetailModal(context:String, clientId:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/modal")
    }
  
    // @LINE:52
    def equipmentEdit(context:String, manufacturer:String, model:String, version:String, clientId:String, equipmentId:String, tabContext:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("equipmentId", equipmentId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("tabContext", tabContext)) + "/edit")
    }
  
    // @LINE:48
    def equipmentCreate(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/create")
    }
  
    // @LINE:20
    def globalEquipment(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/globalequipment")
    }
  
    // @LINE:22
    def globalEquipmentEdit(context:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/globalequipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/edit")
    }
  
    // @LINE:32
    def clientEquipmentDetail(context:String, clientId:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:71
    def serviceEdit(context:String, clientName:String, serviceName:String, serviceType:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/service/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientName", clientName)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("serviceName", serviceName)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("serviceType", serviceType)) + "/edit")
    }
  
    // @LINE:51
    def equipmentDetail(context:String, manufacturer:String, model:String, version:String, clientId:String, equipmentId:String, tabContext:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("equipmentId", equipmentId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("tabContext", tabContext)))
    }
  
    // @LINE:50
    def equipmentCharacteristics(context:String, equipmentId:String, mode:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("equipmentId", equipmentId)) + "/characteristics/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("mode", mode)))
    }
  
    // @LINE:47
    def equipmentCreateTemplate(context:String, manufacturer:String, model:String, version:String, clientId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/createtemplate")
    }
  
    // @LINE:31
    def clientDetails(context:String, clientId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)))
    }
  
    // @LINE:66
    def serviceCreateTemplate(context:String, serviceType:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/service/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("serviceType", serviceType)) + "/createtemplate")
    }
  
    // @LINE:30
    def clientEquipmentCreateTemplate(context:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/createtemplate")
    }
  
    // @LINE:29
    def client(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client")
    }
  
    // @LINE:49
    def equipment(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/equipment")
    }
  
    // @LINE:68
    def serviceCreate(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/service/create")
    }
  
    // @LINE:34
    def clientEquipmentEditModal(context:String, clientId:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)) + "/editModal")
    }
  
    // @LINE:67
    def service(context:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/service")
    }
  
    // @LINE:70
    def serviceDetails(context:String, clientName:String, serviceName:String, serviceType:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/service/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientName", clientName)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("serviceName", serviceName)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("serviceType", serviceType)))
    }
  
    // @LINE:21
    def globalEquipmentDetails(context:String, manufacturer:String, model:String, version:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/globalequipment/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("manufacturer", manufacturer)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("model", model)) + "/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("version", version)))
    }
  
    // @LINE:35
    def clientEquipmentCreateModal(context:String, clientId:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/client/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientId", clientId)) + "/createModal")
    }
  
    // @LINE:72
    def serviceValues(context:String, clientName:String): Call = {
      
      Call("GET", _prefix + { _defaultPrefix } + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("context", context)) + "/nadm/service/" + play.core.routing.dynamicString(implicitly[play.api.mvc.PathBindable[String]].unbind("clientName", clientName)) + "/values")
    }
  
  }


}
