
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.views.html.skeletons.mainSkel
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import na.nadm.settings.NadmConstants.NavContext
/*4.2*/import na.nadm.settings.NadmSettings
/*5.2*/import na.nadm.views.html.components.globalEquip.leftSideForm
/*6.2*/import na.nadm.views.html.components.common.lateralNav

object globalEquipmentSearch extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*10.2*/rightSide/*10.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*10.15*/("""
		"""),format.raw/*11.3*/("""<div class="fx-splitter-content-inner">
			<div class="fx-entity-header">
				<div class="fx-entity-header-info">
					<span class="fx-entity-header-icon">
						<i class="fuxicons fuxicons-equipment-configuration"></i>
					</span>
					<div class="fx-entity-header-title">
						<h1>
						"""),_display_(/*19.8*/i18n("nadm.search.equipconfig.title")),format.raw/*19.45*/("""
						"""),format.raw/*20.7*/("""</h1>
					</div>
				</div>
		</div>

		<x-shadow-scroll vertical>
			<div id="entity-content" class="fx-entity-info" >
				<div data-na-portal-datatables-toolbar class="fx-bulk-actions fx-table-actions clearfix"></div>
				<div data-na-portal-toolbar-advanced-search data-table-id="datatableGlobalEquipment"></div>
				<table id="datatableGlobalEquipment" class="table table-striped table-hover"
					   data-na-portal-table
					   data-na-portal-table-datatable
					   data-na-portal-table-load-using-ajax
					   data-config-url=""""),_display_(/*33.27*/na/*33.29*/.nadm.controllers.routes.TableConfigs.globalEquipment(context)),format.raw/*33.91*/(""""
					   data-na-portal-nadm-global-equip-search-table>
				</table>
			</div>
			<div data-na-portal-nadm-dynamic-loading-bar></div>
		</x-shadow-scroll>
	</div>
""")))};
Seq[Any](format.raw/*8.47*/("""

"""),format.raw/*40.2*/("""

"""),_display_(/*42.2*/mainSkel(context)/*42.19*/ {_display_(Seq[Any](format.raw/*42.21*/("""
	"""),format.raw/*43.2*/("""<div class="fx-push-footer page--nadm-search-page fx-full-height"
		data-ng-controller="NaPortalNadmCommonController"
		data-na-portal-nadm-global-equip-page>
			"""),_display_(/*46.5*/lateralNav/*46.15*/.render(context, NavContext.GLOBAL_EQUIP, nadmSettings)),format.raw/*46.70*/("""
			"""),format.raw/*47.4*/("""<div data-na-portal-nadm-side-bar-search data-context="globalequip">
				<x-splitter class="splitter splitter--nadm-search">
					<form slot="left" class="search-sidebar">
					"""),_display_(/*50.7*/leftSideForm/*50.19*/.render(context)),format.raw/*50.35*/("""
					"""),format.raw/*51.6*/("""</form>
					<div id="fx-splitter-content" slot="right">
					"""),_display_(/*53.7*/rightSide),format.raw/*53.16*/("""
					"""),format.raw/*54.6*/("""</div>
					<div slot="collapsed-left">
						<div class="fx-info-sidebar-collapsed left">
							<div class="fx-sidebar-header search-sidebar__header">
								<span class="fx-entity-header-icon">
									<i class="fa fa-search"></i>
								</span>
							</div>
						</div>
					</div>
				</x-splitter>
			</div>
	</div>
""")))}))
      }
    }
  }

  def render(context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,nadmSettings)

  def f:((String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,nadmSettings) => apply(context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/globalEquipmentSearch.scala.html
                  HASH: e3084eb46c058b5b9b23798e1ed8cab92fffd1a1
                  MATRIX: 665->1|717->48|763->89|819->140|863->179|932->243|1320->302|1444->352|1462->361|1543->365|1574->369|1901->670|1959->707|1994->715|2571->1265|2582->1267|2665->1329|2876->347|2907->1501|2938->1506|2964->1523|3004->1525|3034->1528|3226->1694|3245->1704|3321->1759|3353->1764|3560->1945|3581->1957|3618->1973|3652->1980|3743->2045|3773->2054|3807->2061
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->10|38->10|40->10|41->11|49->19|49->19|50->20|63->33|63->33|63->33|71->8|73->40|75->42|75->42|75->42|76->43|79->46|79->46|79->46|80->47|83->50|83->50|83->50|84->51|86->53|86->53|87->54
                  -- GENERATED --
              */
          