
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.views.html.components.common.lateralNav
/*4.2*/import na.nadm.views.html.components.equipment.leftSideForm
/*5.2*/import na.nadm.views.html.components.operation
/*6.2*/import na.nadm.views.html.skeletons.mainSkel
/*7.2*/import na.naportalbase.views.tags.i18n
/*8.2*/import pt.alticelabs.nossis.security.views.html.authorized
/*9.2*/import na.nadm.settings.AAAPIResources

object equipmentSearch extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,java.util.Map[String, String],NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(context: String, operations: java.util.Map[String, String], nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*13.2*/bulkOperationsButton/*13.22*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*13.26*/("""
	"""),format.raw/*14.2*/("""<div class="table-header-actions clearfix">

	</div>
""")))};def /*19.2*/rightSide/*19.11*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*19.15*/("""
		"""),format.raw/*20.3*/("""<div class="fx-splitter-content-inner">
			<div class="fx-entity-header">
				<div class="fx-entity-header-info">
					<span class="fx-entity-header-icon">
						<i class="glyphicon glyphicon-hdd"></i>
					</span>
					<div class="fx-entity-header-title">
						<h1>
						"""),_display_(/*28.8*/i18n("nadm.search.equipment.title")),format.raw/*28.43*/("""
						"""),format.raw/*29.7*/("""</h1>
					</div>
				</div>
				<div class="fx-entity-header-actions">
				"""),_display_(/*33.6*/authorized(AAAPIResources.device(context).C.toString())/*33.61*/ {_display_(Seq[Any](format.raw/*33.63*/("""
					"""),format.raw/*34.6*/("""<button id="create-button" class="btn btn-primary fx-call-to-action" data-ng-click="createEquipment()">
						<i class="glyphicon glyphicon-plus"></i>
						"""),_display_(/*36.8*/i18n("na.portal.nadm.create")),format.raw/*36.37*/("""
					"""),format.raw/*37.6*/("""</button>
				""")))}),format.raw/*38.6*/("""
				"""),format.raw/*39.5*/("""</div>
			</div>

			<div class="table-header-actions">
				<div class="table-top-left-group pull-left">
				"""),_display_(/*44.6*/operation/*44.15*/.bulkOperationsButton.render(context, operations)),format.raw/*44.64*/("""
				"""),format.raw/*45.5*/("""</div>

				<div class="table-top-right-group pull-right">
					<div class="btn-group" data-na-portal-nadm-equipment-export-all-to-csv></div>
					<div class="btn-group fx-dropdown-reverse" data-na-portal-nadm-equipment-select-visible-columns></div>
				</div>

			</div>

			<x-shadow-scroll>
				<div id="entity-content" class="fx-entity-info">
					<div data-na-portal-nadm-equipment-table-error-mode-notification class="notification--nadm-equipments-error-mode"></div>
					<div data-na-portal-nadm-warning-about-max-selectable-equipments class="notification--max-selectable-equip"></div>
					<div data-na-portal-nadm-equipment-select-all-from-filter-notification class="notification--select-all-from-filter"></div>
					<div data-na-portal-toolbar-advanced-search data-table-id="datatableEquipment"></div>
					<table id="datatableEquipmentModel" class="table table-striped table-hover"
					data-na-portal-nadm-operations-dropdown
					data-na-portal-table
					data-na-portal-table-datatable
					data-na-portal-table-load-using-ajax
					data-config-url=""""),_display_(/*65.24*/na/*65.26*/.nadm.controllers.routes.TableConfigs.equipment(context)),format.raw/*65.82*/(""""
					data-na-portal-nadm-equip-search-table>
					</table>
				</div>
			</x-shadow-scroll>
		</div>
""")))};
Seq[Any](format.raw/*11.90*/("""

"""),format.raw/*17.2*/("""

"""),format.raw/*71.2*/("""

"""),_display_(/*73.2*/mainSkel(context)/*73.19*/ {_display_(Seq[Any](format.raw/*73.21*/("""
	"""),format.raw/*74.2*/("""<div class="fx-push-footer page--nadm-search-page fx-full-height"
		data-ng-controller="NaPortalNadmCommonController"
		data-na-portal-nadm-equipment-page>
			"""),_display_(/*77.5*/lateralNav/*77.15*/.render(context, NavContext.EQUIPMENT, nadmSettings)),format.raw/*77.67*/("""
			"""),format.raw/*78.4*/("""<div data-na-portal-nadm-side-bar-search data-context="equipment">
				<x-splitter class="splitter splitter--nadm-search">
					<form slot="left" class="search-sidebar">
						"""),_display_(/*81.8*/leftSideForm/*81.20*/.render(context)),format.raw/*81.36*/("""
					"""),format.raw/*82.6*/("""</form>
					<div id="fx-splitter-content" slot="right">
						"""),_display_(/*84.8*/rightSide),format.raw/*84.17*/("""
					"""),format.raw/*85.6*/("""</div>
					<div slot="collapsed-left">
						<div class="fx-info-sidebar-collapsed left">
							<div class="fx-sidebar-header search-sidebar__header">
								<span class="fx-entity-header-icon">
									<i class="fa fa-search"></i>
								</span>
							</div>
						</div>
					</div>
				</x-splitter>
			</div>
	</div>
""")))}))
      }
    }
  }

  def render(context:String,operations:java.util.Map[String, String],nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,operations,nadmSettings)

  def f:((String,java.util.Map[String, String],NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,operations,nadmSettings) => apply(context,operations,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/equipmentSearch.scala.html
                  HASH: b825a4376a9d5ffdbfd9a23e6365320c97b7e2c1
                  MATRIX: 665->1|721->52|765->91|827->148|894->210|948->259|1000->306|1046->347|1112->408|1509->451|1676->544|1705->564|1786->568|1816->571|1896->633|1914->642|1995->646|2026->650|2335->933|2391->968|2426->976|2533->1057|2597->1112|2637->1114|2671->1121|2857->1281|2907->1310|2941->1317|2987->1333|3020->1339|3161->1454|3179->1463|3249->1512|3282->1518|4394->2603|4405->2605|4482->2661|4632->539|4663->628|4694->2771|4725->2776|4751->2793|4791->2795|4821->2798|5010->2961|5029->2971|5102->3023|5134->3028|5340->3208|5361->3220|5398->3236|5432->3243|5524->3309|5554->3318|5588->3325
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|37->11|41->13|41->13|43->13|44->14|47->19|47->19|49->19|50->20|58->28|58->28|59->29|63->33|63->33|63->33|64->34|66->36|66->36|67->37|68->38|69->39|74->44|74->44|74->44|75->45|95->65|95->65|95->65|102->11|104->17|106->71|108->73|108->73|108->73|109->74|112->77|112->77|112->77|113->78|116->81|116->81|116->81|117->82|119->84|119->84|120->85
                  -- GENERATED --
              */
          