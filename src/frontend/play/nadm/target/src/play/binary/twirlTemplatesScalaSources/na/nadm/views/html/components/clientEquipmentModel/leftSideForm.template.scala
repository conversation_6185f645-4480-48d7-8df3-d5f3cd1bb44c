
package na.nadm.views.html.components.clientEquipmentModel

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.views.html.components.common.form.{manufacturerField, modelField, versionField}
/*2.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
/*3.2*/import na.naportalbase.views.tags.i18n
/*5.2*/import scala.collection.mutable

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*7.2*/(context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*9.2*/searchButtonAttributes/*9.24*/ = {{
	var map = mutable.HashMap[String, String]()
	map.put("data-ng-click", "searchCtrl.applySearch()")
	map.put("id", "searchButton")
	map.put("type","submit")
	map.put("value",i18n("na.buttons.search"))

	map
}};def /*19.2*/clearButtonAttributes/*19.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.clearForm($event)")
    map.put("id", "clearSearchButton")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))

    map
}};
Seq[Any](format.raw/*7.19*/("""

"""),format.raw/*17.2*/("""

"""),format.raw/*27.2*/("""

"""),format.raw/*29.1*/("""<header class="fx-sidebar-header search-sidebar__header">
	<div class="fx-sidebar-header-inner">
		<p>
			<span class="fx-entity-header-icon">
				<i class="fa fa-search"></i>
			</span>
			<x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
		</p>
	</div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
	<x-shadow-scroll vertical>
		"""),_display_(/*41.4*/manufacturerField/*41.21*/.render(context)),format.raw/*41.37*/("""
		"""),_display_(/*42.4*/modelField/*42.14*/.render(context)),format.raw/*42.30*/("""
		"""),_display_(/*43.4*/versionField/*43.16*/.render(context)),format.raw/*43.32*/("""
	"""),format.raw/*44.2*/("""</x-shadow-scroll>
</div>
<div class="form-group search-sidebar__button-list">
	<div class="fx-splitter-sidebar-buttons">
		<div class="fx-splitter-sidebar-buttons-inner">
		"""),_display_(/*49.4*/leftSideFormInputButtonsComp/*49.32*/.render(searchButtonAttributes, clearButtonAttributes)),format.raw/*49.86*/("""
		"""),format.raw/*50.3*/("""</div>
	</div>
</div>
"""))
      }
    }
  }

  def render(context:String): play.twirl.api.HtmlFormat.Appendable = apply(context)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (context) => apply(context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/clientEquipmentModel/leftSideForm.scala.html
                  HASH: 4925191605b6434ce04c869c751d96fb44f193f3
                  MATRIX: 691->1|793->97|886->184|932->225|1275->259|1370->279|1400->301|1627->517|1657->538|1927->276|1956->514|1985->778|2014->780|2412->1152|2438->1169|2475->1185|2505->1189|2524->1199|2561->1215|2591->1219|2612->1231|2649->1247|2678->1249|2879->1424|2916->1452|2991->1506|3021->1509
                  LINES: 24->1|25->2|26->3|27->5|32->7|36->9|36->9|44->19|44->19|53->7|55->17|57->27|59->29|71->41|71->41|71->41|72->42|72->42|72->42|73->43|73->43|73->43|74->44|79->49|79->49|79->49|80->50
                  -- GENERATED --
              */
          