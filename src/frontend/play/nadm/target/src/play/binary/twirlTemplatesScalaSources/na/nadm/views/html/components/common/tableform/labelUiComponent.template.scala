
package na.nadm.views.html.components.common.tableform

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.settings.DateFormats
/*2.2*/import na.naportalbase.views.tags.i18n
/*3.2*/import pt.ptinovacao.naportal.components.{DateUiComponent, TextUiComponent, UiComponent}

object labelUiComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[UiComponent,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(uiComponent: UiComponent):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*7.2*/timeFormat/*7.12*/ = {{
	uiComponent match {
		case dateUiComponent: DateUiComponent => dateUiComponent.getType match {
			case DateUiComponent.Type.DateTimePicker => i18n(DateFormats.DATETIME_SHORT.key)
			case DateUiComponent.Type.TimePicker => i18n(DateFormats.TIME_SHORT.key)
			case DateUiComponent.Type.DatePicker => i18n(DateFormats.DATE_SHORT.key)
		}
		case _ => null
	}
}};def /*18.2*/isPassword/*18.12*/ = {{
	uiComponent match {
		case textComponent : TextUiComponent if textComponent.getType == TextUiComponent.Type.passwordField => true
		case _ => false
	}
}};
Seq[Any](format.raw/*5.28*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*23.2*/("""
"""),format.raw/*24.1*/("""<div class="fx-form-control-group-preview """),_display_(/*24.44*/{("password").when(isPassword)}),format.raw/*24.75*/("""" """),_display_(/*24.78*/{(s"""data-time-format=${timeFormat} """).when(timeFormat != null)}),format.raw/*24.145*/(""" """),format.raw/*24.146*/("""data-na-portal-nadm-value-label>
	<a title=""""),_display_(/*25.13*/i18n("na.portal.nadm.editValue")),format.raw/*25.45*/("""" class="editable-field-link" href="#">
		<div class="fx-form-control-group-preview-info">
			<i data-ng-show="model.isInherit" data-ng-class="icon.classes" title=""""),format.raw/*27.74*/("""{"""),format.raw/*27.75*/("""{"""),format.raw/*27.76*/("""icon.title"""),format.raw/*27.86*/("""}"""),format.raw/*27.87*/("""}"""),format.raw/*27.88*/(""""></i>
			<span class="content"></span>
		</div>
		<div class="fx-form-control-group-preview-editable-icon">
			<i class="glyphicon glyphicon-pencil"></i>
		</div>
	</a>
</div>


"""))
      }
    }
  }

  def render(uiComponent:UiComponent): play.twirl.api.HtmlFormat.Appendable = apply(uiComponent)

  def f:((UiComponent) => play.twirl.api.HtmlFormat.Appendable) = (uiComponent) => apply(uiComponent)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/tableform/labelUiComponent.scala.html
                  HASH: 67029fd17bf8c7bf42bbba19d0385d471caa3ef3
                  MATRIX: 687->1|738->46|784->86|1193->177|1297->206|1315->216|1692->582|1711->592|1900->203|1929->579|1958->751|1986->752|2056->795|2108->826|2138->829|2227->896|2257->897|2329->942|2382->974|2574->1138|2603->1139|2632->1140|2670->1150|2699->1151|2728->1152
                  LINES: 24->1|25->2|26->3|31->5|35->7|35->7|44->18|44->18|50->5|52->16|54->23|55->24|55->24|55->24|55->24|55->24|55->24|56->25|56->25|58->27|58->27|58->27|58->27|58->27|58->27
                  -- GENERATED --
              */
          