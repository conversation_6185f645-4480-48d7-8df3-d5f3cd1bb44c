
package na.nadm.views.html.components.common.form

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.models.ProvisionStatus
/*2.2*/import na.naportalbase.views.tags.i18n

object provisionStateField extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template0[play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply():play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*4.1*/("""<div class="form-group">
	<label class="control-label">"""),_display_(/*5.32*/{i18n("na.portal.nadm.state")}),format.raw/*5.62*/("""</label>
	<ul class="list-unstyled">
		<li>
			<input id="state-field-"""),_display_(/*8.28*/{ProvisionStatus.ALL}),format.raw/*8.49*/(""""
				data-is-default="true"
				checked
				type="radio"
				name="state-field"
				value=""""),_display_(/*13.13*/{ProvisionStatus.ALL}),format.raw/*13.34*/(""""
				data-field="state"/>
			<label for="state-field-"""),_display_(/*15.29*/{ProvisionStatus.ALL}),format.raw/*15.50*/("""">"""),_display_(/*15.53*/{i18n(s"na.portal.nadm.provisionStatus.${ProvisionStatus.ALL}")}),format.raw/*15.117*/("""</label>
		</li>
		<li>
			<input id="state-field-"""),_display_(/*18.28*/{ProvisionStatus.PROVISIONED}),format.raw/*18.57*/(""""
				type="radio"
				name="state-field"
				value=""""),_display_(/*21.13*/{ProvisionStatus.PROVISIONED}),format.raw/*21.42*/(""""
				data-field="state"/>
			<label for="state-field-"""),_display_(/*23.29*/{ProvisionStatus.PROVISIONED}),format.raw/*23.58*/("""">"""),_display_(/*23.61*/{i18n(s"na.portal.nadm.provisionStatus.${ProvisionStatus.PROVISIONED}")}),format.raw/*23.133*/("""</label>
		</li>
		<li>
			<input id="state-field-"""),_display_(/*26.28*/{ProvisionStatus.NOT_PROVISIONED}),format.raw/*26.61*/(""""
				type="radio"
				name="state-field"
				value=""""),_display_(/*29.13*/{ProvisionStatus.NOT_PROVISIONED}),format.raw/*29.46*/(""""
				data-field="state"/>
			<label for="state-field-"""),_display_(/*31.29*/{ProvisionStatus.NOT_PROVISIONED}),format.raw/*31.62*/("""">"""),_display_(/*31.65*/{i18n(s"na.portal.nadm.provisionStatus.${ProvisionStatus.NOT_PROVISIONED}")}),format.raw/*31.141*/("""</label>
		</li>
	</ul>
</div>"""))
      }
    }
  }

  def render(): play.twirl.api.HtmlFormat.Appendable = apply()

  def f:(() => play.twirl.api.HtmlFormat.Appendable) = () => apply()

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/form/provisionStateField.scala.html
                  HASH: dbd8b08c73502c3140cae4eb675fe4831af35c45
                  MATRIX: 682->1|727->40|1166->80|1248->136|1298->166|1395->237|1436->258|1556->351|1598->372|1680->427|1722->448|1752->451|1838->515|1916->566|1966->595|2047->649|2097->678|2179->733|2229->762|2259->765|2353->837|2431->888|2485->921|2566->975|2620->1008|2702->1063|2756->1096|2786->1099|2884->1175
                  LINES: 24->1|25->2|35->4|36->5|36->5|39->8|39->8|44->13|44->13|46->15|46->15|46->15|46->15|49->18|49->18|52->21|52->21|54->23|54->23|54->23|54->23|57->26|57->26|60->29|60->29|62->31|62->31|62->31|62->31
                  -- GENERATED --
              */
          