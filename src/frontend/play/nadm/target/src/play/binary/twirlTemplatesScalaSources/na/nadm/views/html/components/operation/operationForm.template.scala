
package na.nadm.views.html.components.operation

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.go.views.html.components.operationcreate
/*2.2*/import play.libs.Json
/*3.2*/import pt.ptinovacao.na.portal.webui.restful.go.entities.catalog.Mapping

object operationForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[List[Mapping],List[Mapping],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*5.2*/(requiredMappings: List[Mapping], optionalMappings: List[Mapping]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*5.68*/("""

"""),format.raw/*7.1*/("""<div class="optional-fields-form">
    """),_display_(/*8.6*/operationcreate/*8.21*/.configureOptionalFields(optionalMappings)),format.raw/*8.63*/("""
    """),format.raw/*9.5*/("""<form class="form-horizontal" data-na-portal-go-dynamic-form data-na-portal-go-dynamic-form-mappings=""""),_display_(/*9.108*/Json/*9.112*/.toJson(requiredMappings)),format.raw/*9.137*/("""">
    </form>
</div>
"""))
      }
    }
  }

  def render(requiredMappings:List[Mapping],optionalMappings:List[Mapping]): play.twirl.api.HtmlFormat.Appendable = apply(requiredMappings,optionalMappings)

  def f:((List[Mapping],List[Mapping]) => play.twirl.api.HtmlFormat.Appendable) = (requiredMappings,optionalMappings) => apply(requiredMappings,optionalMappings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/operation/operationForm.scala.html
                  HASH: d9645ecfc35fcb2a218311ed1cb747a542b617ab
                  MATRIX: 680->1|738->53|767->76|1173->151|1334->217|1362->219|1427->259|1450->274|1512->316|1543->321|1673->424|1686->428|1732->453
                  LINES: 24->1|25->2|26->3|31->5|36->5|38->7|39->8|39->8|39->8|40->9|40->9|40->9|40->9
                  -- GENERATED --
              */
          