
package na.nadm.views.html.components.common.form

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.naportalbase.views.tags.i18n

object versionField extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*3.2*/(context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*3.19*/("""

"""),format.raw/*5.1*/("""<div class="form-group">
	<label for="version" class="control-label">"""),_display_(/*6.46*/i18n("na.portal.nadm.version")),format.raw/*6.76*/("""</label><br/>
	<x-multi-version-field
		id="version"
		data-field="version"
		full-width
		hide-selected-values
		placeholder=""""),_display_(/*12.17*/i18n("na.portal.nadm.field.version.allOptions")),format.raw/*12.64*/("""">
	</x-multi-version-field>
</div>"""))
      }
    }
  }

  def render(context:String): play.twirl.api.HtmlFormat.Appendable = apply(context)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (context) => apply(context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/common/form/versionField.scala.html
                  HASH: 1b2d4873ef0f97e235c07a62612a008bf267a6fc
                  MATRIX: 682->1|1032->42|1144->59|1172->61|1268->131|1318->161|1473->289|1541->336
                  LINES: 24->1|29->3|34->3|36->5|37->6|37->6|43->12|43->12
                  -- GENERATED --
              */
          