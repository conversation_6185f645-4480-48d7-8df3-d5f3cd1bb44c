
package na.nadm.views.html.components.service

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*3.2*/import na.naportalbase.settings.BaseMappings
/*4.2*/import na.naportalbase.views.tags.i18n
/*5.2*/import pt.ptinovacao.naportal.nadm.ServiceConfigurations
/*6.2*/import na.nadm.views.html.components.service.serviceFormBlock

object serviceForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[ServiceConfigurations,ViewType,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(configurations: ServiceConfigurations, viewContext: ViewType, context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*10.2*/jsonConfigurations/*10.20*/ = {{ BaseMappings.defaultObjectMapper.valueToTree(configurations.getValues).toString }};def /*12.2*/formBlockLabel/*12.16*/ = {{i18n("na.portal.nadm.blockname.characteristics")}};
Seq[Any](format.raw/*8.109*/("""

"""),format.raw/*10.107*/("""

"""),format.raw/*12.70*/("""

"""),format.raw/*14.1*/("""<div id="serviceForm" class="fx-form-content fx-expandable-form-blocks tab-pane" data-context-value="services" data-json-configs-value=""""),_display_(/*14.138*/jsonConfigurations),format.raw/*14.156*/("""" data-na-portal-nadm-service-details-form >
    <x-collapsible class="fx-section" header=""""),_display_(/*15.48*/formBlockLabel),format.raw/*15.62*/("""">
        <div class="form-horizontal">
            """),_display_(/*17.14*/serviceFormBlock/*17.30*/.render(configurations, viewContext, context, nadmSettings)),format.raw/*17.89*/("""
        """),format.raw/*18.9*/("""</div>
    </x-collapsible>
</div>
"""))
      }
    }
  }

  def render(configurations:ServiceConfigurations,viewContext:ViewType,context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(configurations,viewContext,context,nadmSettings)

  def f:((ServiceConfigurations,ViewType,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (configurations,viewContext,context,nadmSettings) => apply(configurations,viewContext,context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/service/serviceForm.scala.html
                  HASH: fff1d9a7dfaa0c16bf0adcbf1b647a583b49ddc7
                  MATRIX: 678->1|722->40|775->88|827->135|873->176|937->235|1353->301|1539->413|1566->431|1667->523|1690->537|1775->408|1808->518|1840->591|1871->595|2036->732|2076->750|2196->843|2231->857|2314->913|2339->929|2419->988|2456->998
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|34->8|38->10|38->10|38->12|38->12|39->8|41->10|43->12|45->14|45->14|45->14|46->15|46->15|48->17|48->17|48->17|49->18
                  -- GENERATED --
              */
          