
package na.nadm.views.html.components.service

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmSettings
/*2.2*/import na.nadm.utils.FormBuilderUtil
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.html.components.common.tableform.{dateUiComponent, textUiComponent}
/*5.2*/import na.nadm.views.html.components.service.{selectableUiComponent, staticLabelUiComponent}
/*6.2*/import play.twirl.api
/*7.2*/import pt.ptinovacao.naportal.components.{Component, DateUiComponent, GroupComponent, SelectableUiComponent, TextUiComponent, UiComponent}
/*8.2*/import pt.ptinovacao.naportal.nadm.ServiceConfigurations
/*9.2*/import na.naportalbase.views.tags.i18n

object serviceFormBlock extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[ServiceConfigurations,ViewType,String,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(configurations: ServiceConfigurations, viewContext: ViewType, context: String, nadmSettings: NadmSettings):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*14.2*/renderUiComponent/*14.19*/(uiComponent: UiComponent):play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*14.49*/("""
    """),format.raw/*15.5*/("""<div class="form-group">
        <label class="col-sm-2 control-label """),_display_(/*16.47*/{
            if(uiComponent.getRequired != null && uiComponent.getRequired) api.Html("fx-required")
        }),format.raw/*18.10*/(""" """),format.raw/*18.11*/("""">"""),_display_(/*18.14*/FormBuilderUtil/*18.29*/.parseI18n(uiComponent.getI18n)),_display_(/*18.61*/if(uiComponent.getRequired != null && uiComponent.getRequired)/*18.123*/ {_display_(Seq[Any](format.raw/*18.125*/("""<abbr title=""""),_display_(/*18.139*/i18n("na.basemodule.validations.mandatory")),format.raw/*18.182*/(""""></abbr>
            """)))}),format.raw/*19.14*/("""</label>
        <div class="col-sm-10">
        """),_display_(/*21.10*/{
            val angularModel = "formData[\"" + uiComponent.getElementId + "\"]"

            if(viewContext == ViewType.EDIT || viewContext == ViewType.CREATE) {
                uiComponent match {
                    case textComp : TextUiComponent => textUiComponent.render(textComp, angularModel, context, nadmSettings)
                    case dateComp : DateUiComponent => dateUiComponent.render(dateComp, angularModel)
                    case selectableComp: SelectableUiComponent => selectableUiComponent.render(selectableComp, angularModel)
                    case _ =>
                }
            } else {
                staticLabelUiComponent.render(uiComponent)
            }

        }),format.raw/*35.10*/("""
        """),format.raw/*36.9*/("""</div>
    </div>
""")))};
Seq[Any](format.raw/*11.109*/("""


"""),format.raw/*38.2*/("""

"""),_display_(/*40.2*/for(component: Component <- configurations.getForm.getComponentList) yield /*40.70*/ {_display_(Seq[Any](format.raw/*40.72*/("""
    """),_display_(/*41.6*/component/*41.15*/ match/*41.21*/ {/*42.9*/case uiComponent: UiComponent =>/*42.41*/ {_display_(_display_(/*42.44*/renderUiComponent(uiComponent)))}/*43.9*/case groupComponent: GroupComponent =>/*43.47*/ {_display_(Seq[Any](format.raw/*43.49*/("""
            """),format.raw/*44.13*/("""<span class="nadm-group-component">
            """),_display_(/*45.14*/for(uiComponent: UiComponent <- groupComponent.getUiComponentList) yield /*45.80*/{_display_(Seq[Any](format.raw/*45.81*/("""
                """),_display_(/*46.18*/renderUiComponent(uiComponent)),format.raw/*46.48*/("""
            """)))}),format.raw/*47.14*/("""
            """),format.raw/*48.13*/("""</span>
        """)))}}),format.raw/*50.6*/("""
""")))}))
      }
    }
  }

  def render(configurations:ServiceConfigurations,viewContext:ViewType,context:String,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(configurations,viewContext,context,nadmSettings)

  def f:((ServiceConfigurations,ViewType,String,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (configurations,viewContext,context,nadmSettings) => apply(configurations,viewContext,context,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/service/serviceFormBlock.scala.html
                  HASH: d12b3582f0793b23bf590ff0da924af3619d4134
                  MATRIX: 678->1|722->40|766->79|819->127|915->218|1015->313|1044->337|1190->478|1254->537|1653->580|1839->694|1865->711|1972->741|2005->747|2104->819|2237->931|2266->932|2296->935|2320->950|2372->982|2444->1044|2485->1046|2527->1060|2592->1103|2647->1127|2726->1179|3465->1897|3502->1907|3564->687|3597->1928|3628->1933|3712->2001|3752->2003|3785->2010|3803->2019|3818->2025|3828->2037|3869->2069|3900->2072|3941->2113|3988->2151|4028->2153|4070->2167|4147->2217|4229->2283|4268->2284|4314->2303|4365->2333|4411->2348|4453->2362|4502->2387
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|37->11|41->14|41->14|43->14|44->15|45->16|47->18|47->18|47->18|47->18|47->18|47->18|47->18|47->18|47->18|48->19|50->21|64->35|65->36|68->11|71->38|73->40|73->40|73->40|74->41|74->41|74->41|74->42|74->42|74->42|74->43|74->43|74->43|75->44|76->45|76->45|76->45|77->46|77->46|78->47|79->48|80->50
                  -- GENERATED --
              */
          