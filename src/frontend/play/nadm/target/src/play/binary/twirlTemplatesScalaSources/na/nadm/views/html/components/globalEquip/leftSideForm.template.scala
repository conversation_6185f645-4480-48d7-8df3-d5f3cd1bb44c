
package na.nadm.views.html.components.globalEquip

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.views.html.components.common.form.{manufacturerField, modelField, versionField}
/*2.2*/import na.naportalbase.views.html.components.buttonsComp.leftSideFormInputButtonsComp
/*3.2*/import na.naportalbase.views.tags.i18n
/*5.2*/import scala.collection.mutable
/*6.2*/import pt.ptinovacao.na.portal.web.ui.naem.schemas.Context

object leftSideForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*8.2*/(context: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*10.2*/searchButtonAttributes/*10.24*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.applySearch()")
    map.put("id", "searchButton")
    map.put("type", "submit")
    map.put("value", i18n("na.buttons.search"))

    map
}};def /*20.2*/clearButtonAttributes/*20.23*/ = {{
    var map = mutable.HashMap[String, String]()
    map.put("data-ng-click", "searchCtrl.clearForm($event)")
    map.put("id", "clearSearchButton")
    map.put("type", "reset")
    map.put("value", i18n("na.buttons.clear"))

    map
}};
Seq[Any](format.raw/*8.19*/("""

"""),format.raw/*18.2*/("""

"""),format.raw/*28.2*/("""

"""),format.raw/*30.1*/("""<header class="fx-sidebar-header search-sidebar__header">
    <div class="fx-sidebar-header-inner">
        <p>
            <span class="fx-entity-header-icon">
                <i class="fa fa-search"></i>
            </span>
            <x-i18n key="na.basemodule.lateralSearch.title"></x-i18n>
        </p>
    </div>
</header>
<div class="fx-sidebar-content-wrapper search-sidebar__field-list">
    <x-nadm-filter data-filter-context=""""),_display_(/*41.42*/Context/*41.49*/.NA_GLOBALEQUIPMENTS),format.raw/*41.69*/("""">
        <x-shadow-scroll>
            """),_display_(/*43.14*/manufacturerField/*43.31*/.render(context)),format.raw/*43.47*/("""
            """),_display_(/*44.14*/modelField/*44.24*/.render(context)),format.raw/*44.40*/("""
            """),_display_(/*45.14*/versionField/*45.26*/.render(context)),format.raw/*45.42*/("""
        """),format.raw/*46.9*/("""</x-shadow-scroll>
    </x-nadm-filter>
</div>
<div class="form-group search-sidebar__button-list">
    """),_display_(/*50.6*/leftSideFormInputButtonsComp/*50.34*/.render(searchButtonAttributes,clearButtonAttributes)),format.raw/*50.87*/("""
"""),format.raw/*51.1*/("""</div>"""))
      }
    }
  }

  def render(context:String): play.twirl.api.HtmlFormat.Appendable = apply(context)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (context) => apply(context)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/components/globalEquip/leftSideForm.scala.html
                  HASH: 73e7f4174d475df1bd90f7dbc26804694ca72543
                  MATRIX: 682->1|784->97|877->184|923->225|962->258|1332->319|1428->339|1459->361|1706->597|1736->618|2006->336|2035->594|2064->858|2093->860|2559->1299|2575->1306|2616->1326|2685->1368|2711->1385|2748->1401|2789->1415|2808->1425|2845->1441|2886->1455|2907->1467|2944->1483|2980->1492|3111->1597|3148->1625|3222->1678|3250->1679
                  LINES: 24->1|25->2|26->3|27->5|28->6|33->8|37->10|37->10|45->20|45->20|54->8|56->18|58->28|60->30|71->41|71->41|71->41|73->43|73->43|73->43|74->44|74->44|74->44|75->45|75->45|75->45|76->46|80->50|80->50|80->50|81->51
                  -- GENERATED --
              */
          