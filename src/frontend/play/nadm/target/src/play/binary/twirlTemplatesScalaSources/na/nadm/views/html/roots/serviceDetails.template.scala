
package na.nadm.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import na.nadm.settings.NadmConstants.NavContext
/*2.2*/import na.nadm.settings.NadmSettings
/*3.2*/import na.nadm.utils.FormBuilderUtil.ViewType
/*4.2*/import na.nadm.views.html.components.common.detail.headerButtons
/*5.2*/import na.nadm.views.html.components.common.lateralNav
/*6.2*/import na.nadm.views.html.components.service.serviceForm
/*7.2*/import na.nadm.views.html.skeletons.mainSkel
/*8.2*/import na.naportalbase.views.tags.i18n
/*9.2*/import pt.ptinovacao.naportal.nadm.ServiceConfigurations

object serviceDetails extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template7[String,String,String,String,ServiceConfigurations,ViewType,NadmSettings,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*11.2*/(
        context: String,
        clientName: String,
        serviceName: String,
        serviceType: String,
        configurations: ServiceConfigurations,
        viewContext: ViewType,
        nadmSettings: NadmSettings
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {

def /*22.2*/navBar/*22.8*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*22.12*/("""
"""),_display_(/*23.2*/lateralNav/*23.12*/.render(context, NavContext.SERVICE, nadmSettings)),format.raw/*23.62*/("""
""")))};def /*26.2*/headerAction/*26.14*/:play.twirl.api.HtmlFormat.Appendable = {_display_(

Seq[Any](format.raw/*26.18*/("""
"""),_display_(/*27.2*/headerButtons/*27.15*/.render(context, viewContext)),format.raw/*27.44*/("""
""")))};
Seq[Any](format.raw/*19.2*/("""


"""),format.raw/*24.2*/("""

"""),format.raw/*28.2*/("""

"""),_display_(/*30.2*/mainSkel(context)/*30.19*/ {_display_(Seq[Any](format.raw/*30.21*/("""
    """),format.raw/*31.5*/("""<div class="layout--page-with-navbar">
        """),_display_(/*32.10*/navBar),format.raw/*32.16*/("""
        """),format.raw/*33.9*/("""<div class="fx-main-content-wrapper nadm-service__service-view-page fx-with-main-nav" data-ng-controller="NaPortalNadmCommonController" data-na-portal-nadm-service-details-page data-service-name=""""),_display_(/*33.206*/serviceName),format.raw/*33.217*/("""" data-service-type=""""),_display_(/*33.239*/serviceType),format.raw/*33.250*/("""" data-client-name=""""),_display_(/*33.271*/clientName),format.raw/*33.281*/("""">
            <div class="fx-entity-header">
                <div class="fx-entity-header-info">
                    <span class="fx-entity-header-icon">
                        <i class="fa fa-cogs"></i>
                    </span>
                    <div class="fx-entity-header-title">
                        <h1>
                            <span is="x-dynamic-span" data-truncated-at="middle" id="services.service.name">"""),_display_(/*41.110*/serviceName),format.raw/*41.121*/("""</span>
                            <span>&nbsp-&nbsp</span> <span is="x-dynamic-span" data-truncated-at="middle" id="services.service.clientName">"""),_display_(/*42.141*/clientName),format.raw/*42.151*/("""</span>
                            <span>&nbsp-&nbsp"""),_display_(/*43.47*/i18n("NADM.services.service." + serviceType)),format.raw/*43.91*/("""</span>
                        </h1>
                    </div>
                </div>
                <div class="fx-entity-header-actions">
                """),_display_(/*48.18*/headerAction),format.raw/*48.30*/("""
                """),format.raw/*49.17*/("""</div>
            </div>
            <div class="fx-entity-info">
                <x-tab-container>
                    <x-tab label=""""),_display_(/*53.36*/i18n("na.portal.nadm.tabname.characteristics")),format.raw/*53.82*/("""">
                    """),_display_(/*54.22*/serviceForm/*54.33*/.render(configurations, viewContext, context, nadmSettings)),format.raw/*54.92*/("""
                    """),format.raw/*55.21*/("""</x-tab>
                </x-tab-container>
            </div>
        </div>
    </div>
""")))}))
      }
    }
  }

  def render(context:String,clientName:String,serviceName:String,serviceType:String,configurations:ServiceConfigurations,viewContext:ViewType,nadmSettings:NadmSettings): play.twirl.api.HtmlFormat.Appendable = apply(context,clientName,serviceName,serviceType,configurations,viewContext,nadmSettings)

  def f:((String,String,String,String,ServiceConfigurations,ViewType,NadmSettings) => play.twirl.api.HtmlFormat.Appendable) = (context,clientName,serviceName,serviceType,configurations,viewContext,nadmSettings) => apply(context,clientName,serviceName,serviceType,configurations,viewContext,nadmSettings)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:44 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/nadm/target/TwirlSource/na/nadm/views/roots/serviceDetails.scala.html
                  HASH: 3a81264b3f158e2712b99d16507a58d530f9898c
                  MATRIX: 665->1|721->52|765->91|818->139|890->206|952->263|1016->322|1068->369|1114->410|1550->471|1864->713|1878->719|1959->723|1988->726|2007->736|2078->786|2104->794|2125->806|2206->810|2235->813|2257->826|2307->855|2349->706|2382->789|2413->858|2444->863|2470->880|2510->882|2543->888|2619->937|2646->943|2683->953|2908->1150|2941->1161|2991->1183|3024->1194|3073->1215|3105->1225|3570->1662|3603->1673|3780->1822|3812->1832|3894->1887|3959->1931|4151->2096|4184->2108|4230->2126|4397->2266|4464->2312|4516->2337|4536->2348|4616->2407|4666->2429
                  LINES: 24->1|25->2|26->3|27->4|28->5|29->6|30->7|31->8|32->9|37->11|49->22|49->22|51->22|52->23|52->23|52->23|53->26|53->26|55->26|56->27|56->27|56->27|58->19|61->24|63->28|65->30|65->30|65->30|66->31|67->32|67->32|68->33|68->33|68->33|68->33|68->33|68->33|68->33|76->41|76->41|77->42|77->42|78->43|78->43|83->48|83->48|84->49|88->53|88->53|89->54|89->54|89->54|90->55
                  -- GENERATED --
              */
          