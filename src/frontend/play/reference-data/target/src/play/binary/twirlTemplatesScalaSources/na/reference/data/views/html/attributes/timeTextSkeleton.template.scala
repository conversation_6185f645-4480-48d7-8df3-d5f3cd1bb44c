
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object timeTextSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template7[String,java.util.Map[String, String],java.util.Map[String, String],String,String,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        label: String = "",
        divAttributes: java.util.Map[String, String],
        inputAttributes: java.util.Map[String, String],
        dateTime: String = "",
        timeFormatI18n: String = "",
        uiElement: pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,
        action: pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*11.2*/import na.catalog.basemodule.utils.InputUtils
/*12.2*/import na.naportalbase.utils.TemplateUtils

def /*14.2*/id/*14.4*/ = {{
    InputUtils.getElementId(uiElement)
}};def /*18.2*/labelClass/*18.12*/ = {{
    InputUtils.colClassForLabel(uiElement.getUIElementWidth, action)
}};def /*22.2*/divClass/*22.10*/ = {{
    InputUtils.colClassForDiv(uiElement.getUIElementWidth, action)
}};def /*26.2*/inputWidth/*26.12*/ = {{
    InputUtils.widthSizeForInput(uiElement.getUIComponentWidth)
}};
Seq[Any](format.raw/*9.2*/("""

"""),format.raw/*13.1*/("""
"""),format.raw/*16.2*/("""

"""),format.raw/*20.2*/("""

"""),format.raw/*24.2*/("""

"""),format.raw/*28.2*/("""

"""),format.raw/*30.1*/("""<label class=""""),_display_(/*30.16*/labelClass),format.raw/*30.26*/(""" """),format.raw/*30.27*/("""control-label">"""),_display_(/*30.43*/label),format.raw/*30.48*/("""</label>

<div class=""""),_display_(/*32.14*/divClass),format.raw/*32.22*/("""" """),_display_(/*32.25*/TemplateUtils/*32.38*/.dynamicElementsAttributes(divAttributes)),format.raw/*32.79*/(""">
    <x-i18n-attr-container>
        <p id=""""),_display_(/*34.17*/id),format.raw/*34.19*/("""" class="form-control-static """),_display_(/*34.49*/inputWidth),format.raw/*34.59*/("""" """),_display_(/*34.62*/TemplateUtils/*34.75*/.dynamicElementsAttributes(inputAttributes)),format.raw/*34.118*/("""><x-time datetime=""""),_display_(/*34.138*/dateTime),format.raw/*34.146*/("""" data-i18n-format=""""),_display_(/*34.167*/timeFormatI18n),format.raw/*34.181*/(""""></x-time></p>
    </x-i18n-attr-container>
</div>"""))
      }
    }
  }

  def render(label:String,divAttributes:java.util.Map[String, String],inputAttributes:java.util.Map[String, String],dateTime:String,timeFormatI18n:String,uiElement:pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,action:pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action): play.twirl.api.HtmlFormat.Appendable = apply(label,divAttributes,inputAttributes,dateTime,timeFormatI18n,uiElement,action)

  def f:((String,java.util.Map[String, String],java.util.Map[String, String],String,String,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,pt.ptinovacao.netwin.kernel.catalog.client.model.UIContext.Action) => play.twirl.api.HtmlFormat.Appendable) = (label,divAttributes,inputAttributes,dateTime,timeFormatI18n,uiElement,action) => apply(label,divAttributes,inputAttributes,dateTime,timeFormatI18n,uiElement,action)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:16:11 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/timeTextSkeleton.scala.html
                  HASH: d9c3fbd7b1e8881fd8df3a702220077d4dfd611f
                  MATRIX: 1187->1|1631->374|1685->421|1741->466|1751->468|1811->517|1830->527|1920->606|1937->614|2025->691|2044->701|2144->371|2173->464|2201->514|2230->603|2259->688|2288->772|2317->774|2359->789|2390->799|2419->800|2462->816|2488->821|2538->844|2567->852|2597->855|2619->868|2681->909|2754->955|2777->957|2834->987|2865->997|2895->1000|2917->1013|2982->1056|3030->1076|3060->1084|3109->1105|3145->1119
                  LINES: 28->1|39->11|40->12|42->14|42->14|44->18|44->18|46->22|46->22|48->26|48->26|51->9|53->13|54->16|56->20|58->24|60->28|62->30|62->30|62->30|62->30|62->30|62->30|64->32|64->32|64->32|64->32|64->32|66->34|66->34|66->34|66->34|66->34|66->34|66->34|66->34|66->34|66->34|66->34
                  -- GENERATED --
              */
          