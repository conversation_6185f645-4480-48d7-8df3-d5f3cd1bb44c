
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputTextSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,String,java.util.Map[String, String],Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        uiElement: pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,
        label: String = "",
        inputAttributes: java.util.Map[String, String],
        regInfoHtml: Html = play.twirl.api.HtmlFormat.empty
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*9.2*/import na.catalog.basemodule.utils.InputUtils
/*10.2*/import na.naportalbase.utils.TemplateUtils

def /*12.2*/inputId/*12.9*/ = {{
    InputUtils.getElementId(uiElement)
}};def /*16.2*/action/*16.8*/ = {{
    uiContainer.getUiContext.getAction
}};def /*20.2*/labelCss/*20.10*/ = {{
    InputUtils.colClassForLabel(uiElement.getUIElementWidth, action)
}};def /*24.2*/divCss/*24.8*/ = {{
    InputUtils.colClassForDiv(uiElement.getUIElementWidth, action)
}};def /*28.2*/inputWidth/*28.12*/ = {{
    InputUtils.widthSizeForInput(uiElement.getUIComponentWidth)
}};def /*32.2*/required/*32.10*/ = {{
    InputUtils.requiredLabelClass(uiElement, action)
}};
Seq[Any](format.raw/*7.2*/("""

"""),format.raw/*11.1*/("""
"""),format.raw/*14.2*/("""

"""),format.raw/*18.2*/("""

"""),format.raw/*22.2*/("""

"""),format.raw/*26.2*/("""

"""),format.raw/*30.2*/("""

"""),format.raw/*34.2*/("""

"""),format.raw/*36.1*/("""<label for=""""),_display_(/*36.14*/inputId),format.raw/*36.21*/("""" is="x-label" class=""""),_display_(/*36.44*/labelCss),format.raw/*36.52*/(""" """),_display_(/*36.54*/required),format.raw/*36.62*/(""" """),format.raw/*36.63*/("""control-label">"""),_display_(/*36.79*/label),format.raw/*36.84*/("""</label>

<div class=""""),_display_(/*38.14*/divCss),format.raw/*38.20*/("""">
    <input
    type="text"
    id=""""),_display_(/*41.10*/inputId),format.raw/*41.17*/(""""
    class="form-control input-sm """),_display_(/*42.35*/inputWidth),format.raw/*42.45*/(""""
    """),_display_(/*43.6*/TemplateUtils/*43.19*/.dynamicElementsAttributes(inputAttributes)),format.raw/*43.62*/("""
    """),format.raw/*44.5*/(""">
    """),_display_(/*45.6*/regInfoHtml),format.raw/*45.17*/("""
"""),format.raw/*46.1*/("""</div>"""))
      }
    }
  }

  def render(uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,uiElement:pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,label:String,inputAttributes:java.util.Map[String, String],regInfoHtml:Html): play.twirl.api.HtmlFormat.Appendable = apply(uiContainer,uiElement,label,inputAttributes,regInfoHtml)

  def f:((na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,String,java.util.Map[String, String],Html) => play.twirl.api.HtmlFormat.Appendable) = (uiContainer,uiElement,label,inputAttributes,regInfoHtml) => apply(uiContainer,uiElement,label,inputAttributes,regInfoHtml)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:16:11 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/inputTextSkeleton.scala.html
                  HASH: 0908c93346a8df46c5ca104af86811ce46cfcf10
                  MATRIX: 1145->1|1528->314|1582->361|1638->406|1653->413|1713->462|1727->468|1787->517|1804->525|1894->604|1908->610|1996->687|2015->697|2100->771|2117->779|2206->311|2235->404|2263->459|2292->514|2321->601|2350->684|2379->768|2408->839|2437->841|2477->854|2505->861|2555->884|2584->892|2613->894|2642->902|2671->903|2714->919|2740->924|2790->947|2817->953|2883->992|2911->999|2974->1035|3005->1045|3038->1052|3060->1065|3124->1108|3156->1113|3189->1120|3221->1131|3249->1132
                  LINES: 28->1|37->9|38->10|40->12|40->12|42->16|42->16|44->20|44->20|46->24|46->24|48->28|48->28|50->32|50->32|53->7|55->11|56->14|58->18|60->22|62->26|64->30|66->34|68->36|68->36|68->36|68->36|68->36|68->36|68->36|68->36|68->36|68->36|70->38|70->38|73->41|73->41|74->42|74->42|75->43|75->43|75->43|76->44|77->45|77->45|78->46
                  -- GENERATED --
              */
          