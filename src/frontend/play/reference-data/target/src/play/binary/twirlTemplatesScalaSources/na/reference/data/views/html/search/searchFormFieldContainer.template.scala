
package na.reference.data.views.html.search

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object searchFormFieldContainer extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[Html,na.reference.data.utils.views.FormFieldContainerLocation,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        content: Html = Html(""),
        fieldLocation: na.reference.data.utils.views.FormFieldContainerLocation
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import na.reference.data.utils.views.FormFieldContainerLocation

def /*8.2*/containerElementClasses/*8.25*/ = {{
    fieldLocation match {
        case FormFieldContainerLocation.DASHBOARD => "col-xs-12 col-md-4 field-container field-container--dashboard-form"
        case FormFieldContainerLocation.SEARCH_SIDEBAR => "col-xs-12 field-container field-container--sidebar-form"
        case _ => "col-xs-12 field-container"
    }
}};
Seq[Any](format.raw/*4.2*/("""

"""),format.raw/*7.1*/("""
"""),format.raw/*14.2*/("""

"""),format.raw/*16.1*/("""<div class=""""),_display_(/*16.14*/containerElementClasses),format.raw/*16.37*/("""">
    <div class="form-group">
    """),_display_(/*18.6*/content),format.raw/*18.13*/("""
    """),format.raw/*19.5*/("""</div>
</div>"""))
      }
    }
  }

  def render(content:Html,fieldLocation:na.reference.data.utils.views.FormFieldContainerLocation): play.twirl.api.HtmlFormat.Appendable = apply(content,fieldLocation)

  def f:((Html,na.reference.data.utils.views.FormFieldContainerLocation) => play.twirl.api.HtmlFormat.Appendable) = (content,fieldLocation) => apply(content,fieldLocation)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:16:11 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/search/searchFormFieldContainer.scala.html
                  HASH: ae7608e710e5bdea1a89b571150af458d4cded4b
                  MATRIX: 1047->1|1237->121|1313->187|1344->210|1696->118|1724->185|1752->533|1781->535|1821->548|1865->571|1928->608|1956->615|1988->620
                  LINES: 28->1|34->6|36->8|36->8|43->4|45->7|46->14|48->16|48->16|48->16|50->18|50->18|51->19
                  -- GENERATED --
              */
          