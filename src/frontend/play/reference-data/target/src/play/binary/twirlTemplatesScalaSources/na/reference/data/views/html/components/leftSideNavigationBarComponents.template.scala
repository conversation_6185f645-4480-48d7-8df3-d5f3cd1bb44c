
package na.reference.data.views.html.components

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object leftSideNavigationBarComponents extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template1[String,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(activeContext: String):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.models.lateralNav.LateralNavigationComponentItem
/*4.2*/import na.naportalbase.views.html.components.lateralNavComp.lateralNavigationComp
/*5.2*/import na.reference.data.settings.ReferenceDataConstants

def /*7.2*/navElements/*7.13*/ = {{

    val map = scala.collection.mutable.LinkedHashMap[String, LateralNavigationComponentItem]()

    var searchNavElementAttributes = new LateralNavigationComponentItem()
            .setClickAction("home")
            .setTabIcon("glyphicon glyphicon-search")

    var catalogNavElementAttributes = new LateralNavigationComponentItem()
            .setClickAction("search")
            .setTabIcon("fuxicons fuxicons-catalogue")

    activeContext match {
        case ReferenceDataConstants.CONTEXT_SEARCH => searchNavElementAttributes.setActive(true)
        case ReferenceDataConstants.CONTEXT_CATALOG => catalogNavElementAttributes.setActive(true)
    }

    map.put("na.reference.data.nav.search", searchNavElementAttributes)
    map.put("na.reference.data.title", catalogNavElementAttributes)
    map
}};
Seq[Any](format.raw/*1.25*/("""

"""),format.raw/*6.1*/("""
"""),format.raw/*27.2*/("""

"""),_display_(/*29.2*/lateralNavigationComp/*29.23*/.render(navElements, "data-na-portal-reference-data-navigation-bar")))
      }
    }
  }

  def render(activeContext:String): play.twirl.api.HtmlFormat.Appendable = apply(activeContext)

  def f:((String) => play.twirl.api.HtmlFormat.Appendable) = (activeContext) => apply(activeContext)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:16:11 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/components/leftSideNavigationBarComponents.scala.html
                  HASH: b354d6190eb7796c68275dbe396bfa858645c66b
                  MATRIX: 1003->1|1099->27|1178->100|1267->183|1336->242|1355->253|2200->24|2228->240|2256->1068|2285->1071|2315->1092
                  LINES: 28->1|31->3|32->4|33->5|35->7|35->7|56->1|58->6|59->27|61->29|61->29
                  -- GENERATED --
              */
          