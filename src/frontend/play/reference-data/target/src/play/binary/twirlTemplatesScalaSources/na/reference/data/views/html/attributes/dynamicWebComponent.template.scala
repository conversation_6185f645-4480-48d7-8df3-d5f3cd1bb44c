
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object dynamicWebComponent extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[Html,na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,na.catalog.basemodule.models.nossisinv.attributes.UICatAttribute[_$1] forSome { 
   type _$1
},play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        content: Html = Html(""),
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        uiElement: pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,
        uiAttribute: na.catalog.basemodule.models.nossisinv.attributes.UICatAttribute[_]
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*8.2*/import na.catalog.basemodule.utils.InputUtils

def /*10.2*/action/*10.8*/ = {{
    uiContainer.getUiContext.getAction
}};def /*14.2*/divCss/*14.8*/ = {{
    InputUtils.colClassForDiv(uiElement.getUIElementWidth, action)
}};def /*18.2*/labelCss/*18.10*/ = {{
    InputUtils.colClassForLabel(uiElement.getUIElementWidth, action)
}};def /*22.2*/required/*22.10*/ = {{
    InputUtils.requiredLabelClass(uiElement, action)
}};def /*26.2*/label/*26.7*/ = {{
    InputUtils.getLabel(uiAttribute)
}};
Seq[Any](format.raw/*6.2*/("""

"""),format.raw/*9.1*/("""
"""),format.raw/*12.2*/("""

"""),format.raw/*16.2*/("""

"""),format.raw/*20.2*/("""

"""),format.raw/*24.2*/("""

"""),format.raw/*28.2*/("""

"""),format.raw/*30.1*/("""<label is="x-label" class=""""),_display_(/*30.29*/labelCss),format.raw/*30.37*/(""" """),_display_(/*30.39*/required),format.raw/*30.47*/(""" """),format.raw/*30.48*/("""control-label">"""),_display_(/*30.64*/label),format.raw/*30.69*/("""</label>

<div class=""""),_display_(/*32.14*/divCss),format.raw/*32.20*/("""">"""),_display_(/*32.23*/content),format.raw/*32.30*/("""</div>"""))
      }
    }
  }

  def render(content:Html,uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,uiElement:pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,uiAttribute:na.catalog.basemodule.models.nossisinv.attributes.UICatAttribute[_$1] forSome { 
   type _$1
}): play.twirl.api.HtmlFormat.Appendable = apply(content,uiContainer,uiElement,uiAttribute)

  def f:((Html,na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,na.catalog.basemodule.models.nossisinv.attributes.UICatAttribute[_$1] forSome { 
   type _$1
}) => play.twirl.api.HtmlFormat.Appendable) = (content,uiContainer,uiElement,uiAttribute) => apply(content,uiContainer,uiElement,uiAttribute)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:16:11 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/dynamicWebComponent.scala.html
                  HASH: e82d09b13e9b57ef969c83d0ae31318c3908e401
                  MATRIX: 1205->1|1567->293|1626->341|1640->347|1700->396|1714->402|1802->479|1819->487|1909->566|1926->574|2000->637|2013->642|2086->290|2114->339|2142->393|2171->476|2200->563|2229->634|2258->686|2287->688|2342->716|2371->724|2400->726|2429->734|2458->735|2501->751|2527->756|2577->779|2604->785|2634->788|2662->795
                  LINES: 30->1|38->8|40->10|40->10|42->14|42->14|44->18|44->18|46->22|46->22|48->26|48->26|51->6|53->9|54->12|56->16|58->20|60->24|62->28|64->30|64->30|64->30|64->30|64->30|64->30|64->30|64->30|66->32|66->32|66->32|66->32
                  -- GENERATED --
              */
          