
package na.reference.data.views.html.attributes

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputTextAreaSkeleton extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template6[String,na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,String,String,java.util.Map[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        inputId: String = "",
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        uiElement: pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,
        label: String = "",
        value: String = "",
        inputAttributes: java.util.Map[String, String]
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*10.2*/import na.naportalbase.utils.TemplateUtils
/*11.2*/import na.catalog.basemodule.utils.InputUtils

def /*13.2*/action/*13.8*/ = {{
    uiContainer.getUiContext.getAction
}};def /*17.2*/uiComponentWidth/*17.18*/ = {{
    uiElement.getUIComponentWidth
}};def /*21.2*/uiElementWidth/*21.16*/ = {{
    uiElement.getUIElementWidth
}};def /*25.2*/divCss/*25.8*/ = {{
    InputUtils.colClassForDiv(uiElementWidth, action)
}};def /*29.2*/labelCss/*29.10*/ = {{
    InputUtils.colClassForLabel(uiElementWidth, action)
}};def /*33.2*/required/*33.10*/ = {{
    InputUtils.requiredLabelClass(uiElement, action)
}};
Seq[Any](format.raw/*8.2*/("""

"""),format.raw/*12.1*/("""
"""),format.raw/*15.2*/("""

"""),format.raw/*19.2*/("""

"""),format.raw/*23.2*/("""

"""),format.raw/*27.2*/("""

"""),format.raw/*31.2*/("""

"""),format.raw/*35.2*/("""

"""),format.raw/*37.1*/("""<label for=""""),_display_(/*37.14*/inputId),format.raw/*37.21*/("""" is="x-label" class=""""),_display_(/*37.44*/labelCss),format.raw/*37.52*/(""" """),_display_(/*37.54*/required),format.raw/*37.62*/(""" """),format.raw/*37.63*/("""control-label">"""),_display_(/*37.79*/label),format.raw/*37.84*/("""</label>

<div class=""""),_display_(/*39.14*/divCss),format.raw/*39.20*/("""">
    <textarea
    id =""""),_display_(/*41.11*/inputId),format.raw/*41.18*/(""""
    class="form-control full-width """),_display_(/*42.37*/InputUtils/*42.47*/.widthSizeForInput(uiComponentWidth)),format.raw/*42.83*/(""""
    """),_display_(/*43.6*/TemplateUtils/*43.19*/.dynamicElementsAttributes(inputAttributes)),format.raw/*43.62*/("""
    """),format.raw/*44.5*/(""">"""),_display_(/*44.7*/value),format.raw/*44.12*/("""</textarea>
</div>"""))
      }
    }
  }

  def render(inputId:String,uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,uiElement:pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,label:String,value:String,inputAttributes:java.util.Map[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(inputId,uiContainer,uiElement,label,value,inputAttributes)

  def f:((String,na.catalog.basemodule.models.nossisinv.attributes.UIContainer,pt.ptinovacao.netwin.kernel.catalog.client.model.UIElement,String,String,java.util.Map[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (inputId,uiContainer,uiElement,label,value,inputAttributes) => apply(inputId,uiContainer,uiElement,label,value,inputAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:16:11 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/attributes/inputTextAreaSkeleton.scala.html
                  HASH: 4c096c599953e6129bf0d4a2e22f00d0592ce640
                  MATRIX: 1158->1|1539->311|1590->355|1649->403|1663->409|1723->458|1748->474|1803->518|1826->532|1879->574|1893->580|1968->644|1985->652|2062->718|2079->726|2168->308|2197->401|2225->455|2254->515|2283->571|2312->641|2341->715|2370->786|2399->788|2439->801|2467->808|2517->831|2546->839|2575->841|2604->849|2633->850|2676->866|2702->871|2752->894|2779->900|2833->927|2861->934|2926->972|2945->982|3002->1018|3035->1025|3057->1038|3121->1081|3153->1086|3181->1088|3207->1093
                  LINES: 28->1|38->10|39->11|41->13|41->13|43->17|43->17|45->21|45->21|47->25|47->25|49->29|49->29|51->33|51->33|54->8|56->12|57->15|59->19|61->23|63->27|65->31|67->35|69->37|69->37|69->37|69->37|69->37|69->37|69->37|69->37|69->37|69->37|71->39|71->39|73->41|73->41|74->42|74->42|74->42|75->43|75->43|75->43|76->44|76->44|76->44
                  -- GENERATED --
              */
          