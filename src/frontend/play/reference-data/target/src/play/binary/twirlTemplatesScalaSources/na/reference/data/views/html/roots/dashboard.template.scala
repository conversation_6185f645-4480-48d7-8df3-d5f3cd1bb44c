
package na.reference.data.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object dashboard extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,String,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        entityName: String,
        context: String,
        element: na.catalog.basemodule.ui.WebComponent,
        headerSkeletonHelper: na.reference.data.utils.views.skeletons.HeaderSkeletonHelper
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*8.2*/import na.naportalbase.views.tags.i18n
/*9.2*/import na.reference.data.views.html.components.leftSideNavigationBarComponents
/*10.2*/import na.reference.data.views.html.skeletons.{mainSkeleton, search}


Seq[Any](format.raw/*6.2*/("""

"""),format.raw/*11.1*/("""
"""),_display_(/*12.2*/mainSkeleton()/*12.16*/ {_display_(Seq[Any](format.raw/*12.18*/("""
"""),format.raw/*13.1*/("""<div class="fx-push-footer fx-full-height" data-na-portal-reference-data-dashboard>
    <div data-fuxi-header></div>
    """),_display_(/*15.6*/leftSideNavigationBarComponents/*15.37*/.render(context)),format.raw/*15.53*/("""
    """),format.raw/*16.5*/("""<div class="main-content-wrapper" data-na-portal-reference-data-home>
        <div class="fx-entity-header page__search-results-header">
            <div class="fx-entity-header-info">
                <span class="fx-entity-header-icon">
                    <i class="glyphicon glyphicon-search"></i>
                </span>
                <div class="fx-entity-header-title">
                    <h1>
                        """),_display_(/*24.26*/i18n("na.reference.data.module.title")),format.raw/*24.64*/("""
                    """),format.raw/*25.21*/("""</h1>
                </div>
            </div>
            <div class="fx-entity-header-actions">
                """),_display_(/*29.18*/headerSkeletonHelper/*29.38*/.createEntityButtonComponent()),format.raw/*29.68*/("""
            """),format.raw/*30.13*/("""</div>
        </div>
        """),_display_(/*32.10*/search/*32.16*/.render(
        entityName = entityName,
        element = element
        )),format.raw/*35.10*/("""
    """),format.raw/*36.5*/("""</div>
</div>
""")))}))
      }
    }
  }

  def render(entityName:String,context:String,element:na.catalog.basemodule.ui.WebComponent,headerSkeletonHelper:na.reference.data.utils.views.skeletons.HeaderSkeletonHelper): play.twirl.api.HtmlFormat.Appendable = apply(entityName,context,element,headerSkeletonHelper)

  def f:((String,String,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper) => play.twirl.api.HtmlFormat.Appendable) = (entityName,context,element,headerSkeletonHelper) => apply(entityName,context,element,headerSkeletonHelper)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:16:10 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/roots/dashboard.scala.html
                  HASH: ed849639f68e9d6b68bb8d86ff494ee0bbeec5d3
                  MATRIX: 1082->1|1358->207|1404->247|1491->327|1588->204|1617->396|1645->398|1668->412|1708->414|1736->415|1884->537|1924->568|1961->584|1993->589|2448->1017|2507->1055|2556->1076|2699->1192|2728->1212|2779->1242|2820->1255|2878->1286|2893->1292|2991->1369|3023->1374
                  LINES: 28->1|36->8|37->9|38->10|41->6|43->11|44->12|44->12|44->12|45->13|47->15|47->15|47->15|48->16|56->24|56->24|57->25|61->29|61->29|61->29|62->30|64->32|64->32|67->35|68->36
                  -- GENERATED --
              */
          