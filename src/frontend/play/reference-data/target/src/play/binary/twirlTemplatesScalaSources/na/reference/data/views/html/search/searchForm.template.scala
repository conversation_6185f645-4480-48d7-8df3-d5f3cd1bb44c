
package na.reference.data.views.html.search

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object searchForm extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.reference.data.services.UIAttributesService,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        uiAttributesService: na.reference.data.services.UIAttributesService
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*6.2*/import pt.ptinovacao.netwin.kernel.catalog.client.model.{UIGroup, UISubGroup}


Seq[Any](format.raw/*4.2*/("""

"""),format.raw/*7.1*/("""
"""),format.raw/*8.1*/("""<div class="fx-main-searchbox-item">
"""),_display_(/*9.2*/for((indexUiGroup, uiGroup: UIGroup) <- uiContainer.getUiContext.getUIGroups) yield /*9.79*/ {_display_(Seq[Any](format.raw/*9.81*/("""
    """),_display_(/*10.6*/for((indexUiSubGroup, uiSubGroup: UISubGroup) <- uiGroup.getUISubGroups) yield /*10.78*/ {_display_(Seq[Any](format.raw/*10.80*/("""
        """),_display_(/*11.10*/uiAttributesService/*11.29*/.drawEntityAttributes(
            uiContainer,
            uiSubGroup
        )),format.raw/*14.10*/("""
    """)))}),format.raw/*15.6*/("""
""")))}),format.raw/*16.2*/("""
"""),format.raw/*17.1*/("""</div>"""))
      }
    }
  }

  def render(uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,uiAttributesService:na.reference.data.services.UIAttributesService): play.twirl.api.HtmlFormat.Appendable = apply(uiContainer,uiAttributesService)

  def f:((na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.reference.data.services.UIAttributesService) => play.twirl.api.HtmlFormat.Appendable) = (uiContainer,uiAttributesService) => apply(uiContainer,uiAttributesService)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:16:11 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/search/searchForm.scala.html
                  HASH: 67bbf4b0d70394bcc09e013f49eb4ab5b5a01f07
                  MATRIX: 1080->1|1316->167|1422->164|1450->245|1477->246|1540->284|1632->361|1671->363|1703->369|1791->441|1831->443|1868->453|1896->472|1997->552|2033->558|2065->560|2093->561
                  LINES: 28->1|34->6|37->4|39->7|40->8|41->9|41->9|41->9|42->10|42->10|42->10|43->11|43->11|46->14|47->15|48->16|49->17
                  -- GENERATED --
              */
          