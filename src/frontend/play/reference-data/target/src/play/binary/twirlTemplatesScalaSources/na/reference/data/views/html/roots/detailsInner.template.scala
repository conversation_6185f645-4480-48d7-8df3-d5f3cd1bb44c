
package na.reference.data.views.html.roots

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object detailsInner extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.CatalogInstanceDetails,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,na.reference.data.services.UIAttributesService,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(
        uiContainer: na.catalog.basemodule.models.nossisinv.attributes.UIContainer,
        element: na.catalog.basemodule.ui.WebComponent,
        catalogInstanceDetails: na.reference.data.utils.views.CatalogInstanceDetails,
        headerSkeletonHelper: na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,
        uiAttributesService: na.reference.data.services.UIAttributesService
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*7.2*/("""

"""),_display_(/*9.2*/na/*9.4*/.reference.data.views.html.skeletons.detailsInnerSkeleton.render(
    uiContainer = uiContainer,
    element = element,
    catalogInstanceDetails = catalogInstanceDetails,
    headerSkeletonHelper = headerSkeletonHelper,
    uiAttributesService = uiAttributesService
)))
      }
    }
  }

  def render(uiContainer:na.catalog.basemodule.models.nossisinv.attributes.UIContainer,element:na.catalog.basemodule.ui.WebComponent,catalogInstanceDetails:na.reference.data.utils.views.CatalogInstanceDetails,headerSkeletonHelper:na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,uiAttributesService:na.reference.data.services.UIAttributesService): play.twirl.api.HtmlFormat.Appendable = apply(uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService)

  def f:((na.catalog.basemodule.models.nossisinv.attributes.UIContainer,na.catalog.basemodule.ui.WebComponent,na.reference.data.utils.views.CatalogInstanceDetails,na.reference.data.utils.views.skeletons.HeaderSkeletonHelper,na.reference.data.services.UIAttributesService) => play.twirl.api.HtmlFormat.Appendable) = (uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService) => apply(uiContainer,element,catalogInstanceDetails,headerSkeletonHelper,uiAttributesService)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:16:10 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/reference-data/target/TwirlSource/na/reference/data/views/roots/detailsInner.scala.html
                  HASH: 02c74f998a8ab480b5ddbc149bc0219024b9edc2
                  MATRIX: 1233->1|1724->398|1752->401|1761->403
                  LINES: 28->1|39->7|41->9|41->9
                  -- GENERATED --
              */
          