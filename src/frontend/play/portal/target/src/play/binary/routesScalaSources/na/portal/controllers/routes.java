// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/portal/conf/naportal.routes
// @DATE:Wed Jul 02 14:16:07 WEST 2025

package na.portal.controllers;

import naportal.RoutesPrefix;

public class routes {
  
  public static final na.portal.controllers.ReverseAssets Assets = new na.portal.controllers.ReverseAssets(RoutesPrefix.byNamePrefix());
  public static final na.portal.controllers.ReverseApplication Application = new na.portal.controllers.ReverseApplication(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final na.portal.controllers.javascript.ReverseAssets Assets = new na.portal.controllers.javascript.ReverseAssets(RoutesPrefix.byNamePrefix());
    public static final na.portal.controllers.javascript.ReverseApplication Application = new na.portal.controllers.javascript.ReverseApplication(RoutesPrefix.byNamePrefix());
  }

}
