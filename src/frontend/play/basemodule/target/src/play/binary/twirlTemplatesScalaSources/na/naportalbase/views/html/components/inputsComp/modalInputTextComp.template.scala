
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object modalInputTextComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,String,String,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(label: String, inputId: String, placeHolder: String, inputAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.122*/("""

"""),format.raw/*3.1*/("""<label for=""""),_display_(/*3.14*/inputId),format.raw/*3.21*/("""" class="control-label col-sm-3">"""),_display_(/*3.55*/label),format.raw/*3.60*/("""</label>
<div class="col-sm-9">
    """),_display_(/*5.6*/na/*5.8*/.naportalbase.views.html.components.inputsComp.inputTextComp.render(inputId, placeHolder, inputAttributes)),format.raw/*5.114*/("""
"""),format.raw/*6.1*/("""</div>"""))
      }
    }
  }

  def render(label:String,inputId:String,placeHolder:String,inputAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,placeHolder,inputAttributes)

  def f:((String,String,String,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,placeHolder,inputAttributes) => apply(label,inputId,placeHolder,inputAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/modalInputTextComp.scala.html
                  HASH: dbbcf1d9e82c466648c210ef04f6de63fe9ebfa7
                  MATRIX: 1062->1|1278->121|1306->123|1345->136|1372->143|1432->177|1457->182|1519->219|1528->221|1655->327|1682->328
                  LINES: 28->1|33->1|35->3|35->3|35->3|35->3|35->3|37->5|37->5|37->5|38->6
                  -- GENERATED --
              */
          