// @GENERATOR:play-routes-compiler
// @SOURCE:/home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/conf/naportalbase.routes
// @DATE:Wed Jul 02 14:15:14 WEST 2025

package pt.alticelabs.nossis.security.controllers;

import naportalbase.RoutesPrefix;

public class routes {
  
  public static final pt.alticelabs.nossis.security.controllers.ReverseNossisAuthenticator NossisAuthenticator = new pt.alticelabs.nossis.security.controllers.ReverseNossisAuthenticator(RoutesPrefix.byNamePrefix());

  public static class javascript {
    
    public static final pt.alticelabs.nossis.security.controllers.javascript.ReverseNossisAuthenticator NossisAuthenticator = new pt.alticelabs.nossis.security.controllers.javascript.ReverseNossisAuthenticator(RoutesPrefix.byNamePrefix());
  }

}
