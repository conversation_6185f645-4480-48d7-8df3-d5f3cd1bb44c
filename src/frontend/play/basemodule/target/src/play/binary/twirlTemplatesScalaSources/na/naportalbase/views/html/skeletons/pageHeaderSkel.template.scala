
package na.naportalbase.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object pageHeaderSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[Html,Html,Html,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(header: Html, headerActions: Html, additionalHeaderHtml1: Html, additionalHeaderHtml2: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.95*/("""

"""),format.raw/*3.1*/("""<div class="fx-entity-header">
    <div class="fx-entity-header-info">
        """),_display_(/*5.10*/header),format.raw/*5.16*/("""
    """),format.raw/*6.5*/("""</div>
    <div class="fx-entity-header-actions">
        """),_display_(/*8.10*/headerActions),format.raw/*8.23*/("""
    """),format.raw/*9.5*/("""</div>
</div>

<!-- todo: MPT/GO uses this to render top of the page tab... this is not part of the header. do it somewhere else -->
"""),_display_(/*13.2*/additionalHeaderHtml1),format.raw/*13.23*/("""
"""),_display_(/*14.2*/additionalHeaderHtml2))
      }
    }
  }

  def render(header:Html,headerActions:Html,additionalHeaderHtml1:Html,additionalHeaderHtml2:Html): play.twirl.api.HtmlFormat.Appendable = apply(header,headerActions,additionalHeaderHtml1,additionalHeaderHtml2)

  def f:((Html,Html,Html,Html) => play.twirl.api.HtmlFormat.Appendable) = (header,headerActions,additionalHeaderHtml1,additionalHeaderHtml2) => apply(header,headerActions,additionalHeaderHtml1,additionalHeaderHtml2)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/pageHeaderSkel.scala.html
                  HASH: 99a0de07e7d35d36a0e16756704f86c4a0e24e84
                  MATRIX: 996->1|1184->94|1212->96|1318->176|1344->182|1375->187|1460->246|1493->259|1524->264|1684->398|1726->419|1754->421
                  LINES: 28->1|33->1|35->3|37->5|37->5|38->6|40->8|40->8|41->9|45->13|45->13|46->14
                  -- GENERATED --
              */
          