
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object wizardInputTextComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[String,String,String,scala.collection.mutable.HashMap[String, String],Boolean,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(label: String, inputId: String, placeHolder: String, inputAttributes: scala.collection.mutable.HashMap[String, String], required: Boolean):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.141*/("""

"""),format.raw/*3.1*/("""<label for=""""),_display_(/*3.14*/inputId),format.raw/*3.21*/("""" class="col-sm-2 control-label """),_display_(/*3.54*/if(required)/*3.66*/{_display_(Seq[Any](format.raw/*3.67*/("""fx-required-field""")))}),format.raw/*3.85*/("""" >"""),_display_(/*3.89*/label),format.raw/*3.94*/("""</label>
<div class="col-sm-10">
    """),_display_(/*5.6*/na/*5.8*/.naportalbase.views.html.components.inputsComp.inputTextComp.render(inputId, placeHolder, inputAttributes)),format.raw/*5.114*/("""
"""),format.raw/*6.1*/("""</div>"""))
      }
    }
  }

  def render(label:String,inputId:String,placeHolder:String,inputAttributes:scala.collection.mutable.HashMap[String, String],required:Boolean): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,placeHolder,inputAttributes,required)

  def f:((String,String,String,scala.collection.mutable.HashMap[String, String],Boolean) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,placeHolder,inputAttributes,required) => apply(label,inputId,placeHolder,inputAttributes,required)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/wizardInputTextComp.scala.html
                  HASH: a21565aed3b458e02cd1445e4f6472df39ce0232
                  MATRIX: 1071->1|1306->140|1336->144|1375->157|1402->164|1461->197|1481->209|1519->210|1567->228|1597->232|1622->237|1687->277|1696->279|1823->385|1851->387
                  LINES: 28->1|33->1|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|37->5|37->5|37->5|38->6
                  -- GENERATED --
              */
          