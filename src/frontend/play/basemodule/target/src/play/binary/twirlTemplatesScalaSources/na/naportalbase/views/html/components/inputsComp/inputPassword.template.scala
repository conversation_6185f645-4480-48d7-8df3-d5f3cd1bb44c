
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputPassword extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(inputId: String, placeHolder: String, inputAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.107*/("""
"""),format.raw/*3.1*/("""<input id=""""),_display_(/*3.13*/inputId),format.raw/*3.20*/("""" type="password" """),_display_(/*3.39*/if(!inputAttributes.contains("class"))/*3.77*/{_display_(Seq[Any](format.raw/*3.78*/("""class="form-control input-sm"""")))}),format.raw/*3.108*/(""" """),format.raw/*3.109*/("""placeholder=""""),_display_(/*3.123*/placeHolder),format.raw/*3.134*/("""" """),_display_(/*3.137*/TemplateUtils/*3.150*/.dynamicElementsAttributes(inputAttributes)),format.raw/*3.193*/("""/>




"""))
      }
    }
  }

  def render(inputId:String,placeHolder:String,inputAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(inputId,placeHolder,inputAttributes)

  def f:((String,String,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (inputId,placeHolder,inputAttributes) => apply(inputId,placeHolder,inputAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/inputPassword.scala.html
                  HASH: 1da69c030b7a956321c82f4f1c92a8f9463b7e45
                  MATRIX: 1050->1|1228->109|1301->106|1329->153|1367->165|1394->172|1439->191|1485->229|1523->230|1584->260|1613->261|1654->275|1686->286|1716->289|1738->302|1802->345
                  LINES: 28->1|31->2|34->1|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3|35->3
                  -- GENERATED --
              */
          