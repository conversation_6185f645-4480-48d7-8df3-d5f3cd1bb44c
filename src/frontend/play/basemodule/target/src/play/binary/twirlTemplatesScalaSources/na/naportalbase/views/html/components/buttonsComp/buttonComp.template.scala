
package na.naportalbase.views.html.components.buttonsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object buttonComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(buttonTitle: String, buttonAttributes : scala.collection.mutable.HashMap[String,String], iAttributes: scala.collection.mutable.HashMap[String,String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.153*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<button """),_display_(/*5.10*/TemplateUtils/*5.23*/.dynamicElementsAttributes(buttonAttributes)),format.raw/*5.67*/(""">
    <i """),_display_(/*6.9*/TemplateUtils/*6.22*/.dynamicElementsAttributes(iAttributes)),format.raw/*6.61*/("""></i>
    """),_display_(/*7.6*/buttonTitle),format.raw/*7.17*/("""
"""),format.raw/*8.1*/("""</button>"""))
      }
    }
  }

  def render(buttonTitle:String,buttonAttributes:scala.collection.mutable.HashMap[String, String],iAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(buttonTitle,buttonAttributes,iAttributes)

  def f:((String,scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (buttonTitle,buttonAttributes,iAttributes) => apply(buttonTitle,buttonAttributes,iAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/buttonsComp/buttonComp.scala.html
                  HASH: 6a0fb5de0c1fdd16118e132b1f83cf2fbe9ac712
                  MATRIX: 1090->1|1314->155|1387->152|1415->198|1442->199|1477->208|1498->221|1562->265|1597->275|1618->288|1677->327|1713->338|1744->349|1771->350
                  LINES: 28->1|31->3|34->1|36->4|37->5|37->5|37->5|37->5|38->6|38->6|38->6|39->7|39->7|40->8
                  -- GENERATED --
              */
          