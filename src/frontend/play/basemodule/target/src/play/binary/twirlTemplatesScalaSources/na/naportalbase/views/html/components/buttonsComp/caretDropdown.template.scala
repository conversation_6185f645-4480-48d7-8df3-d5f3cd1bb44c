
package na.naportalbase.views.html.components.buttonsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object caretDropdown extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String, scala.collection.mutable.HashMap[String, String]]],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(buttonTitle: String, options:scala.collection.mutable.LinkedHashMap[String,scala.collection.mutable.HashMap[String,scala.collection.mutable.HashMap[String,String]]]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.168*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<div class="btn-group">
    <button class="btn fx-btn-action dropdown-toggle" data-toggle="dropdown" type="button" id="caretSettings">
        <i class="glyphicon glyphicon-cog" title=""""),_display_(/*7.52*/buttonTitle),format.raw/*7.63*/(""""></i>
        <i class="caret"></i>
    </button>
    <ul class="dropdown-menu" role="menu">

    """),_display_(/*12.6*/for((key,value) <- options) yield /*12.33*/{_display_(Seq[Any](format.raw/*12.34*/("""
        """),format.raw/*13.9*/("""<li>
            <a """),_display_(/*14.17*/TemplateUtils/*14.30*/.dynamicElementsAttributes(value.get("a").get)),format.raw/*14.76*/("""> <i """),_display_(/*14.82*/TemplateUtils/*14.95*/.dynamicElementsAttributes(value.get("i").get)),format.raw/*14.141*/("""></i> """),_display_(/*14.148*/key),format.raw/*14.151*/("""</a>
        </li>
    """)))}),format.raw/*16.6*/("""
    """),format.raw/*17.5*/("""</ul>
</div>"""))
      }
    }
  }

  def render(buttonTitle:String,options:scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String, scala.collection.mutable.HashMap[String, String]]]): play.twirl.api.HtmlFormat.Appendable = apply(buttonTitle,options)

  def f:((String,scala.collection.mutable.LinkedHashMap[String, scala.collection.mutable.HashMap[String, scala.collection.mutable.HashMap[String, String]]]) => play.twirl.api.HtmlFormat.Appendable) = (buttonTitle,options) => apply(buttonTitle,options)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/buttonsComp/caretDropdown.scala.html
                  HASH: 53720b4800dcdee49cc0ca62219f391b9bf333f5
                  MATRIX: 1134->1|1373->170|1446->167|1474->213|1501->214|1713->400|1744->411|1870->511|1913->538|1952->539|1988->548|2036->569|2058->582|2125->628|2158->634|2180->647|2248->693|2283->700|2308->703|2362->727|2394->732
                  LINES: 28->1|31->3|34->1|36->4|37->5|39->7|39->7|44->12|44->12|44->12|45->13|46->14|46->14|46->14|46->14|46->14|46->14|46->14|46->14|48->16|49->17
                  -- GENERATED --
              */
          