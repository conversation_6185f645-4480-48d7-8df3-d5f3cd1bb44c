
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object sidebarInputTextComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template5[String,String,String,scala.collection.mutable.HashMap[String, String],Boolean,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(label: String, inputId: String, placeHolder: String, inputAttributes: scala.collection.mutable.HashMap[String, String]
        ,notification: Boolean):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*2.32*/("""

"""),format.raw/*4.1*/("""<label for=""""),_display_(/*4.14*/inputId),format.raw/*4.21*/("""">"""),_display_(/*4.24*/label),format.raw/*4.29*/("""</label>
"""),_display_(/*5.2*/na/*5.4*/.naportalbase.views.html.components.inputsComp.inputTextComp.render(inputId, placeHolder, inputAttributes)),format.raw/*5.110*/("""
"""))
      }
    }
  }

  def render(label:String,inputId:String,placeHolder:String,inputAttributes:scala.collection.mutable.HashMap[String, String],notification:Boolean): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,placeHolder,inputAttributes,notification)

  def f:((String,String,String,scala.collection.mutable.HashMap[String, String],Boolean) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,placeHolder,inputAttributes,notification) => apply(label,inputId,placeHolder,inputAttributes,notification)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/sidebarInputTextComp.scala.html
                  HASH: dea739cdef311a320c77bbe8a27dfe7ab3f8b7c4
                  MATRIX: 1072->1|1319->153|1349->157|1388->170|1415->177|1444->180|1469->185|1505->196|1514->198|1641->304
                  LINES: 28->1|34->2|36->4|36->4|36->4|36->4|36->4|37->5|37->5|37->5
                  -- GENERATED --
              */
          