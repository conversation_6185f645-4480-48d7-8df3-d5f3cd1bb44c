
package na.naportalbase.views.html.components.selectsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object dynamicSelectOptionComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[String,scala.collection.immutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(dynamicOptionValue: String, dynamicOptionAttributes: scala.collection.immutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.107*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<option """),_display_(/*5.10*/TemplateUtils/*5.23*/.dynamicElementsAttributes(dynamicOptionAttributes)),format.raw/*5.74*/(""">
    """),_display_(/*6.6*/dynamicOptionValue),format.raw/*6.24*/("""
"""),format.raw/*7.1*/("""</option>"""))
      }
    }
  }

  def render(dynamicOptionValue:String,dynamicOptionAttributes:scala.collection.immutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(dynamicOptionValue,dynamicOptionAttributes)

  def f:((String,scala.collection.immutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (dynamicOptionValue,dynamicOptionAttributes) => apply(dynamicOptionValue,dynamicOptionAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/selectsComp/dynamicSelectOptionComp.scala.html
                  HASH: 7a72ca35c7991ffb4b5029d16f8a7d3f66cd7eb1
                  MATRIX: 1056->1|1234->109|1307->106|1335->152|1362->153|1397->162|1418->175|1489->226|1521->233|1559->251|1586->252
                  LINES: 28->1|31->3|34->1|36->4|37->5|37->5|37->5|37->5|38->6|38->6|39->7
                  -- GENERATED --
              */
          