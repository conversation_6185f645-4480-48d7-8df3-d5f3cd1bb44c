
package na.naportalbase.views.html.components.selectsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object selectComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[scala.collection.immutable.HashMap[String, String],String,scala.collection.immutable.HashMap[String, String],Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(selectAttributes       : scala.collection.immutable.HashMap[String, String]
, initialOptionValue     : String
, initialOptionAttributes: scala.collection.immutable.HashMap[String, String]
, dynamicOptions         : Html
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*7.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*5.2*/("""

"""),format.raw/*8.1*/("""
"""),format.raw/*9.1*/("""<select """),_display_(/*9.10*/TemplateUtils/*9.23*/.dynamicElementsAttributes(selectAttributes)),format.raw/*9.67*/(""">
    """),_display_(/*10.6*/if(initialOptionAttributes != null)/*10.41*/ {_display_(Seq[Any](format.raw/*10.43*/("""
        """),format.raw/*11.9*/("""<option """),_display_(/*11.18*/{if(initialOptionAttributes != null) TemplateUtils.dynamicElementsAttributes(initialOptionAttributes)}),format.raw/*11.120*/(""">
            """),_display_(/*12.14*/initialOptionValue),format.raw/*12.32*/("""
        """),format.raw/*13.9*/("""</option>
    """)))}/*14.6*/else/*14.10*/{_display_(Seq[Any](format.raw/*14.11*/("""
        """),format.raw/*15.9*/("""<option value=""""),_display_(/*15.25*/{selectAttributes.getOrElse("value","")}),format.raw/*15.65*/(""""></option>
    """)))}),format.raw/*16.6*/("""
    """),_display_(/*17.6*/dynamicOptions),format.raw/*17.20*/("""
"""),format.raw/*18.1*/("""</select>"""))
      }
    }
  }

  def render(selectAttributes:scala.collection.immutable.HashMap[String, String],initialOptionValue:String,initialOptionAttributes:scala.collection.immutable.HashMap[String, String],dynamicOptions:Html): play.twirl.api.HtmlFormat.Appendable = apply(selectAttributes,initialOptionValue,initialOptionAttributes,dynamicOptions)

  def f:((scala.collection.immutable.HashMap[String, String],String,scala.collection.immutable.HashMap[String, String],Html) => play.twirl.api.HtmlFormat.Appendable) = (selectAttributes,initialOptionValue,initialOptionAttributes,dynamicOptions) => apply(selectAttributes,initialOptionValue,initialOptionAttributes,dynamicOptions)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/selectsComp/selectComp.scala.html
                  HASH: 046e6b85f2fa55fd80be5c11fdfabb3a6a023b4e
                  MATRIX: 1099->1|1394->226|1465->223|1493->269|1520->270|1555->279|1576->292|1640->336|1673->343|1717->378|1757->380|1793->389|1829->398|1953->500|1995->515|2034->533|2070->542|2103->557|2116->561|2155->562|2191->571|2234->587|2295->627|2342->644|2374->650|2409->664|2437->665
                  LINES: 28->1|35->7|38->5|40->8|41->9|41->9|41->9|41->9|42->10|42->10|42->10|43->11|43->11|43->11|44->12|44->12|45->13|46->14|46->14|46->14|47->15|47->15|47->15|48->16|49->17|49->17|50->18
                  -- GENERATED --
              */
          