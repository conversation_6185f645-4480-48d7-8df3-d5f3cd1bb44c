
package na.naportalbase.views.html.skeletons.wizard

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object wizardSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template9[Html,Html,String,String,String,String,String,String,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(steps : Html, initialContent : Html, previousButtonMessage : String, nextButtonMessage : String, finalizeButtonMessage : String,  cancelButtonMessage : String, fullCtrlName : String, ctrlAbbreviation : String, sectionAttributes: scala.collection.mutable.HashMap[String, String] ):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.282*/("""

"""),format.raw/*4.1*/("""

"""),format.raw/*6.1*/("""<div class="tab-pane active" id="tab-car" """),_display_(/*6.44*/if(ctrlAbbreviation != null && !ctrlAbbreviation.isEmpty)/*6.101*/{_display_(Seq[Any](format.raw/*6.102*/(""" """),format.raw/*6.103*/("""data-ng-controller=""""),_display_(/*6.124*/fullCtrlName),format.raw/*6.136*/(""" """),format.raw/*6.137*/("""as """),_display_(/*6.141*/ctrlAbbreviation),format.raw/*6.157*/(""""""")))}/*6.159*/else/*6.163*/{_display_(Seq[Any](format.raw/*6.164*/("""data-ng-controller=""""),_display_(/*6.185*/fullCtrlName),format.raw/*6.197*/(""""""")))}),format.raw/*6.199*/(""">
    <div id="wizard" data-na-portal-wizard class="fx-wizard-wrapper">
        <nav>
            <ul class="list-unstyled fx-nav-items">
                """),_display_(/*10.18*/steps),format.raw/*10.23*/("""
            """),format.raw/*11.13*/("""</ul>
        </nav>

        <section id="wiz-content" class="fx-wizardsteps-content" data-na-portal-wizard-template-loader """),_display_(/*14.105*/TemplateUtils/*14.118*/.dynamicElementsAttributes(sectionAttributes)),format.raw/*14.163*/(""">
            <div data-na-portal-wizard-initial-content class="wizard-initial-content">
                """),_display_(/*16.18*/initialContent),format.raw/*16.32*/("""
            """),format.raw/*17.13*/("""</div>
        </section>

        <div class="fx-wizard-buttons-wrapper">
            <div class="fx-wizard-buttons-wrapper-inner">
                <button id="wiz-prev" class="btn btn-default btn-sm" data-na-portal-wizard-previous-step type="button">
                    <i class="glyphicon glyphicon-chevron-left"></i>"""),_display_(/*23.70*/previousButtonMessage),format.raw/*23.91*/("""
                """),format.raw/*24.17*/("""</button>
                <button id="wiz-next" class="btn btn-sm" data-na-portal-wizard-next-step type="button">
                    """),_display_(/*26.22*/nextButtonMessage),format.raw/*26.39*/("""<i class="glyphicon glyphicon-chevron-right"></i>
                </button>
                <button id="wiz-complete" class="btn btn-sm" data-na-portal-wizard-complete type="button">
                    """),_display_(/*29.22*/finalizeButtonMessage),format.raw/*29.43*/("""
                """),format.raw/*30.17*/("""</button>
                <button id="wiz-cancel" class="btn btn-default btn-sm" data-na-portal-wizard-cancel type="button">
                    """),_display_(/*32.22*/cancelButtonMessage),format.raw/*32.41*/("""
                """),format.raw/*33.17*/("""</button>
            </div>
        </div>
    </div>
</div>



"""))
      }
    }
  }

  def render(steps:Html,initialContent:Html,previousButtonMessage:String,nextButtonMessage:String,finalizeButtonMessage:String,cancelButtonMessage:String,fullCtrlName:String,ctrlAbbreviation:String,sectionAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(steps,initialContent,previousButtonMessage,nextButtonMessage,finalizeButtonMessage,cancelButtonMessage,fullCtrlName,ctrlAbbreviation,sectionAttributes)

  def f:((Html,Html,String,String,String,String,String,String,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (steps,initialContent,previousButtonMessage,nextButtonMessage,finalizeButtonMessage,cancelButtonMessage,fullCtrlName,ctrlAbbreviation,sectionAttributes) => apply(steps,initialContent,previousButtonMessage,nextButtonMessage,finalizeButtonMessage,cancelButtonMessage,fullCtrlName,ctrlAbbreviation,sectionAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/wizard/wizardSkel.scala.html
                  HASH: a93177b48c7f6d2d453d52c8ee3d11402571f4e7
                  MATRIX: 1080->1|1433->284|1506->281|1534->327|1562->329|1631->372|1697->429|1736->430|1765->431|1813->452|1846->464|1875->465|1906->469|1943->485|1964->487|1977->491|2016->492|2064->513|2097->525|2130->527|2312->682|2338->687|2379->700|2533->826|2556->839|2623->884|2756->990|2791->1004|2832->1017|3181->1339|3223->1360|3268->1377|3430->1512|3468->1529|3699->1733|3741->1754|3786->1771|3959->1917|3999->1936|4044->1953
                  LINES: 28->1|31->3|34->1|36->4|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|38->6|42->10|42->10|43->11|46->14|46->14|46->14|48->16|48->16|49->17|55->23|55->23|56->24|58->26|58->26|61->29|61->29|62->30|64->32|64->32|65->33
                  -- GENERATED --
              */
          