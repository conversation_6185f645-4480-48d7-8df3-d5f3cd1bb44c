
package na.naportalbase.views.html.components.checkBoxComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object LeftSideLabelcheckBoxComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(label: String, inputId: String, inputAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.101*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<div>"""),_display_(/*5.7*/label),format.raw/*5.12*/(""" """),format.raw/*5.13*/("""<input id=""""),_display_(/*5.25*/inputId),format.raw/*5.32*/("""" type="checkbox" """),_display_(/*5.51*/TemplateUtils/*5.64*/.dynamicElementsAttributes(inputAttributes)),format.raw/*5.107*/("""></div>


"""))
      }
    }
  }

  def render(label:String,inputId:String,inputAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,inputAttributes)

  def f:((String,String,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,inputAttributes) => apply(label,inputId,inputAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/checkBoxComp/LeftSideLabelcheckBoxComp.scala.html
                  HASH: 642852ab7d90d9fc47ec79d156cfd00b747aa0f7
                  MATRIX: 1064->1|1236->105|1309->100|1339->149|1367->151|1398->157|1423->162|1451->163|1489->175|1516->182|1561->201|1582->214|1646->257
                  LINES: 28->1|31->3|34->1|36->4|37->5|37->5|37->5|37->5|37->5|37->5|37->5|37->5|37->5
                  -- GENERATED --
              */
          