
package na.naportalbase.views.html.skeletons.search

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object horizontalFormSkel extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[scala.collection.mutable.HashMap[String, String],Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(formAttributes: scala.collection.mutable.HashMap[String, String], form: Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.80*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<form class="form-horizontal" role="form" """),_display_(/*5.44*/TemplateUtils/*5.57*/.dynamicElementsAttributes(formAttributes)),format.raw/*5.99*/(""">
        """),_display_(/*6.10*/form),format.raw/*6.14*/("""
"""),format.raw/*7.1*/("""</form>"""))
      }
    }
  }

  def render(formAttributes:scala.collection.mutable.HashMap[String, String],form:Html): play.twirl.api.HtmlFormat.Appendable = apply(formAttributes,form)

  def f:((scala.collection.mutable.HashMap[String, String],Html) => play.twirl.api.HtmlFormat.Appendable) = (formAttributes,form) => apply(formAttributes,form)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/search/horizontalFormSkel.scala.html
                  HASH: 3abd01d42492d999ab71b7d3fe71d7438511e021
                  MATRIX: 1041->1|1192->82|1264->79|1292->125|1319->126|1388->169|1409->182|1471->224|1508->235|1532->239|1559->240
                  LINES: 28->1|31->3|34->1|36->4|37->5|37->5|37->5|37->5|38->6|38->6|39->7
                  -- GENERATED --
              */
          