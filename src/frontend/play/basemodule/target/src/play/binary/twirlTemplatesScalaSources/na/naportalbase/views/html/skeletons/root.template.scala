
package na.naportalbase.views.html.skeletons

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._
/*1.2*/import play.mvc.Http

object root extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,Html,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*2.2*/(title: String = "")(body: Html = Html(""))(bodyScripts: Html = Html("")):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*2.75*/("""
"""),format.raw/*3.1*/("""<!DOCTYPE html>
<html lang=""""),_display_(/*4.14*/{Http.Context.current().lang().code()}),format.raw/*4.52*/("""" data-app-theme="nossis-one">
    <head>
        <style>
            /* Hide the body initially */
            body """),format.raw/*8.18*/("""{"""),format.raw/*8.19*/(""" """),format.raw/*8.20*/("""display: none; """),format.raw/*8.35*/("""}"""),format.raw/*8.36*/("""
        """),format.raw/*9.9*/("""</style>
        <meta charset="utf-8">
        <title>"""),_display_(/*11.17*/title),format.raw/*11.22*/("""</title>
    </head>
    <body>
        """),_display_(/*14.10*/body),format.raw/*14.14*/("""
        """),format.raw/*15.9*/("""<!-- BASE MODULE SCRIPTS IMPORTS START -->
        """),_display_(/*16.10*/na/*16.12*/.naportalbase.views.html.imports.scripts.render()),format.raw/*16.61*/("""
        """),format.raw/*17.9*/("""<!-- BASE MODULE SCRIPTS IMPORTS END -->
        <!-- CUSTOM BODY SCRIPTS START -->
        """),_display_(/*19.10*/bodyScripts),format.raw/*19.21*/("""
        """),format.raw/*20.9*/("""<!-- CUSTOM BODY SCRIPTS END -->
    </body>
</html>"""))
      }
    }
  }

  def render(title:String,body:Html,bodyScripts:Html): play.twirl.api.HtmlFormat.Appendable = apply(title)(body)(bodyScripts)

  def f:((String) => (Html) => (Html) => play.twirl.api.HtmlFormat.Appendable) = (title) => (body) => (bodyScripts) => apply(title)(body)(bodyScripts)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/skeletons/root.scala.html
                  HASH: 2df937a158c71acf5aa11fca6a43a956c3292c3d
                  MATRIX: 677->1|1011->23|1179->96|1206->97|1261->126|1319->164|1463->281|1491->282|1519->283|1561->298|1589->299|1624->308|1707->364|1733->369|1801->410|1826->414|1862->423|1941->475|1952->477|2022->526|2058->535|2178->628|2210->639|2246->648
                  LINES: 24->1|29->2|34->2|35->3|36->4|36->4|40->8|40->8|40->8|40->8|40->8|41->9|43->11|43->11|46->14|46->14|47->15|48->16|48->16|48->16|49->17|51->19|51->19|52->20
                  -- GENERATED --
              */
          