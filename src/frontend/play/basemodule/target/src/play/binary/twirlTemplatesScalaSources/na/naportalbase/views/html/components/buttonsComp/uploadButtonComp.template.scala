
package na.naportalbase.views.html.components.buttonsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object uploadButtonComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template3[String,String,scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(inputId: String, placeHolder: String, buttonAttributes: scala.collection.mutable.HashMap[String, String]):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*3.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.108*/("""

"""),format.raw/*4.1*/("""
"""),format.raw/*5.1*/("""<input id=""""),_display_(/*5.13*/inputId),format.raw/*5.20*/("""" file-model="files" type="file" placeholder=""""),_display_(/*5.67*/placeHolder),format.raw/*5.78*/("""" """),_display_(/*5.81*/TemplateUtils/*5.94*/.dynamicElementsAttributes(buttonAttributes)),format.raw/*5.138*/("""/>"""))
      }
    }
  }

  def render(inputId:String,placeHolder:String,buttonAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(inputId,placeHolder,buttonAttributes)

  def f:((String,String,scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (inputId,placeHolder,buttonAttributes) => apply(inputId,placeHolder,buttonAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/buttonsComp/uploadButtonComp.scala.html
                  HASH: 19042163321d0e13e97cf0549c090119745514ab
                  MATRIX: 1054->1|1233->110|1306->107|1334->153|1361->154|1399->166|1426->173|1499->220|1530->231|1559->234|1580->247|1645->291
                  LINES: 28->1|31->3|34->1|36->4|37->5|37->5|37->5|37->5|37->5|37->5|37->5|37->5
                  -- GENERATED --
              */
          