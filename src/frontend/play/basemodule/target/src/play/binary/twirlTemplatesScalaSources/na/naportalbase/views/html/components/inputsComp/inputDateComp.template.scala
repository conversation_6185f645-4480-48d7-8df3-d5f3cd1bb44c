
package na.naportalbase.views.html.components.inputsComp

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object inputDateComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template8[String,String,String,String,String,scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(label: String,
	inputId: String,
    datePickerId: String,
	dateFormat: String,
    extension: String,
    datePickerAttributes: scala.collection.mutable.HashMap[String, String],
    inputAttributes: scala.collection.mutable.HashMap[String, String],
    inputNotificationAttributes: scala.collection.mutable.HashMap[String, String]
):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*11.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*9.2*/("""

"""),format.raw/*12.1*/("""
    """),format.raw/*13.5*/("""<label for=""""),_display_(/*13.18*/datePickerId),format.raw/*13.30*/("""">"""),_display_(/*13.33*/label),format.raw/*13.38*/("""</label>
    <div class='input-group datetime' data-datetimepicker-ext="""),_display_(/*14.64*/extension),format.raw/*14.73*/("""
		"""),format.raw/*15.3*/("""data-java-date-format=""""),_display_(/*15.27*/dateFormat),format.raw/*15.37*/("""" """),_display_(/*15.40*/TemplateUtils/*15.53*/.dynamicElementsAttributes(datePickerAttributes)),format.raw/*15.101*/(""">
        <input class="form-control input-sm" type="text" """),_display_(/*16.59*/TemplateUtils/*16.72*/.dynamicElementsAttributes(inputAttributes)),format.raw/*16.115*/("""
        """),_display_(/*17.10*/if(inputNotificationAttributes != null)/*17.49*/ {_display_(Seq[Any](format.raw/*17.51*/("""
            """),_display_(/*18.14*/TemplateUtils/*18.27*/.dynamicElementsAttributes(inputNotificationAttributes)),format.raw/*18.82*/("""
        """)))}),format.raw/*19.10*/("""
        """),format.raw/*20.9*/("""/>
        <!--<span class="input-group-addon"><span class="glyphicon glyphicon-remove" data-ng-click="clearInputCalendar()"></span></span>-->
        <span class="input-group-addon datepickerbutton"><span class="glyphicon glyphicon-calendar"></span></span>
    </div>
"""))
      }
    }
  }

  def render(label:String,inputId:String,datePickerId:String,dateFormat:String,extension:String,datePickerAttributes:scala.collection.mutable.HashMap[String, String],inputAttributes:scala.collection.mutable.HashMap[String, String],inputNotificationAttributes:scala.collection.mutable.HashMap[String, String]): play.twirl.api.HtmlFormat.Appendable = apply(label,inputId,datePickerId,dateFormat,extension,datePickerAttributes,inputAttributes,inputNotificationAttributes)

  def f:((String,String,String,String,String,scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String],scala.collection.mutable.HashMap[String, String]) => play.twirl.api.HtmlFormat.Appendable) = (label,inputId,datePickerId,dateFormat,extension,datePickerAttributes,inputAttributes,inputNotificationAttributes) => apply(label,inputId,datePickerId,dateFormat,extension,datePickerAttributes,inputAttributes,inputNotificationAttributes)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/inputsComp/inputDateComp.scala.html
                  HASH: e87ce1cbdef319520f350ef434f404376eb8c014
                  MATRIX: 1169->1|1577->338|1648->335|1677->381|1709->386|1749->399|1782->411|1812->414|1838->419|1937->491|1967->500|1997->503|2048->527|2079->537|2109->540|2131->553|2201->601|2288->661|2310->674|2375->717|2412->727|2460->766|2500->768|2541->782|2563->795|2639->850|2680->860|2716->869
                  LINES: 28->1|39->11|42->9|44->12|45->13|45->13|45->13|45->13|45->13|46->14|46->14|47->15|47->15|47->15|47->15|47->15|47->15|48->16|48->16|48->16|49->17|49->17|49->17|50->18|50->18|50->18|51->19|52->20
                  -- GENERATED --
              */
          