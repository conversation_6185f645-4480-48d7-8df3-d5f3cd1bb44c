
package na.naportalbase.views.html.components.wizard

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object wizardContentComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template2[Html,Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(headContent : Html, blockContent : Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {


Seq[Any](format.raw/*1.43*/("""


"""),format.raw/*4.1*/("""<x-shadow-scroll>
    """),_display_(/*5.6*/headContent),format.raw/*5.17*/("""
    """),format.raw/*6.5*/("""<div class="fx-wizardsteps-content-block">
        <div class="form-horizontal">
        """),_display_(/*8.10*/blockContent),format.raw/*8.22*/("""
        """),format.raw/*9.9*/("""</div>
    </div>
</x-shadow-scroll>

"""))
      }
    }
  }

  def render(headContent:Html,blockContent:Html): play.twirl.api.HtmlFormat.Appendable = apply(headContent,blockContent)

  def f:((Html,Html) => play.twirl.api.HtmlFormat.Appendable) = (headContent,blockContent) => apply(headContent,blockContent)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/wizard/wizardContentComp.scala.html
                  HASH: 1ebd643919c70261015715ff2e866a2f7bdae994
                  MATRIX: 997->1|1133->42|1162->45|1210->68|1241->79|1272->84|1388->174|1420->186|1455->195
                  LINES: 28->1|33->1|36->4|37->5|37->5|38->6|40->8|40->8|41->9
                  -- GENERATED --
              */
          