
package na.naportalbase.views.html.components.tabs

import _root_.play.twirl.api.TwirlFeatureImports._
import _root_.play.twirl.api.TwirlHelperImports._
import _root_.play.twirl.api.Html
import _root_.play.twirl.api.JavaScript
import _root_.play.twirl.api.Txt
import _root_.play.twirl.api.Xml
import models._
import controllers._
import play.api.i18n._
import play.api.templates.PlayMagic._
import java.lang._
import java.util._
import scala.collection.JavaConverters._
import play.core.j.PlayMagicForJava._
import play.mvc._
import play.api.data.Field
import play.mvc.Http.Context.Implicit._
import play.data._
import play.core.j.PlayFormsMagicForJava._
import views.html._

object contentSectionComp extends _root_.play.twirl.api.BaseScalaTemplate[play.twirl.api.HtmlFormat.Appendable,_root_.play.twirl.api.Format[play.twirl.api.HtmlFormat.Appendable]](play.twirl.api.HtmlFormat) with _root_.play.twirl.api.Template4[String,Boolean,scala.collection.mutable.Map[String, String],Html,play.twirl.api.HtmlFormat.Appendable] {

  /**/
  def apply/*1.2*/(id: String, isActive: Boolean, listAttributes: scala.collection.mutable.Map[String, String])(content : Html):play.twirl.api.HtmlFormat.Appendable = {
    _display_ {
      {
/*2.2*/import na.naportalbase.utils.TemplateUtils


Seq[Any](format.raw/*1.111*/("""
"""),format.raw/*3.1*/("""
"""),format.raw/*4.1*/("""<div """),_display_(/*4.7*/TemplateUtils/*4.20*/.dynamicElementsAttributes(listAttributes)),format.raw/*4.62*/(""" """),format.raw/*4.63*/("""id=""""),_display_(/*4.68*/id),format.raw/*4.70*/("""" class="tab-pane """),_display_(/*4.89*/("active".when(isActive))),format.raw/*4.114*/("""">

    """),_display_(/*6.6*/content),format.raw/*6.13*/("""

"""),format.raw/*8.1*/("""</div>"""))
      }
    }
  }

  def render(id:String,isActive:Boolean,listAttributes:scala.collection.mutable.Map[String, String],content:Html): play.twirl.api.HtmlFormat.Appendable = apply(id,isActive,listAttributes)(content)

  def f:((String,Boolean,scala.collection.mutable.Map[String, String]) => (Html) => play.twirl.api.HtmlFormat.Appendable) = (id,isActive,listAttributes) => (content) => apply(id,isActive,listAttributes)(content)

  def ref: this.type = this

}


              /*
                  -- GENERATED --
                  DATE: Wed Jul 02 14:15:15 WEST 2025
                  SOURCE: /home/<USER>/Desktop/workspaces/na-portal/branches/10.3.0-NAPORTAL-26712/src/frontend/play/basemodule/target/TwirlSource/na/naportalbase/views/components/tabs/contentSectionComp.scala.html
                  HASH: 579ef7a9f39c3221c6c5faea1183d9bd2108b4f2
                  MATRIX: 1051->1|1233->113|1306->110|1334->157|1362->159|1393->165|1414->178|1476->220|1504->221|1535->226|1557->228|1602->247|1648->272|1684->283|1711->290|1741->294
                  LINES: 28->1|31->2|34->1|35->3|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|36->4|38->6|38->6|40->8
                  -- GENERATED --
              */
          