package pt.ptinovacao.na.portal.core.mpt.dispatcher._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import pt.ptinovacao.na.portal.webui.restful.commons.model.ExecutionStates;
import org.jboss.logging.DelegatingBasicLogger;
import pt.ptinovacao.na.portal.core.request.engine.model.request.Request;
import java.lang.String;
import org.jboss.logging.Logger;
import pt.ptinovacao.na.portal.webui.restful.commons.model.OrderStatus;
import org.jboss.logging.BasicLogger;
import java.lang.Long;
import java.lang.Throwable;
import java.lang.Object;
import pt.ptinovacao.na.portal.core.mpt.dispatcher._private.DispatcherLogger.ASYNCH_TYPES;


import static org.jboss.logging.Logger.Level.ERROR;
import static org.jboss.logging.Logger.Level.INFO;
import static org.jboss.logging.Logger.Level.FATAL;
import static org.jboss.logging.Logger.Level.DEBUG;
import static org.jboss.logging.Logger.Level.WARN;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-02T14:14:52+0100")
public class DispatcherLogger_$logger extends DelegatingBasicLogger implements DispatcherLogger, BasicLogger, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = DispatcherLogger_$logger.class.getName();
    public DispatcherLogger_$logger(final Logger log) {
        super(log);
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    @Override
    public final void orderStatusTransition(final String orderId, final ExecutionStates states) {
        super.log.logf(FQCN, INFO, null, orderStatusTransition$str(), orderId, states);
    }
    private static final String orderStatusTransition = "MPTDISPATCHER000001: Order transition request - Order id:%s - Status:%s";
    protected String orderStatusTransition$str() {
        return orderStatusTransition;
    }
    @Override
    public final void urlResponseWebserviceAbsence(final String mbeanObjectName) {
        super.log.logf(FQCN, FATAL, null, urlResponseWebserviceAbsence$str(), mbeanObjectName);
    }
    private static final String urlResponseWebserviceAbsence = "MPTDISPATCHER000002: Absent NA response webservice url while contacting platform mbean server. Used the following objectName [%s]";
    protected String urlResponseWebserviceAbsence$str() {
        return urlResponseWebserviceAbsence;
    }
    @Override
    public final void orderNotYetStartedDataManagerError(final Throwable e, final String orderId) {
        super.log.logf(FQCN, FATAL, e, orderNotYetStartedDataManagerError$str(), orderId);
    }
    private static final String orderNotYetStartedDataManagerError = "MPTDISPATCHER000003: OrderHandler monitor guard for order not started yet error(Possible database connectivity error). EventOrder ID [%s]";
    protected String orderNotYetStartedDataManagerError$str() {
        return orderNotYetStartedDataManagerError;
    }
    @Override
    public final void onNewOrderInfo(final String prefixInfo, final Object orderKey, final Object clientId, final Object dateCreated, final Object orderResultString, final Object orderResult, final Object orderException) {
        super.log.logf(FQCN, DEBUG, null, onNewOrderInfo$str(), prefixInfo, orderKey, clientId, dateCreated, orderResultString, orderResult, orderException);
    }
    private static final String onNewOrderInfo = "MPTDISPATCHER000004: [[%s]] handling order result:\n=============================== order event payload ===============================\norderKey          = %s\nclientId          = %s\ndateCreated       = %s\norderResultString = %s\norderResult       = %s\norderException    = %s\n===================================================================================";
    protected String onNewOrderInfo$str() {
        return onNewOrderInfo;
    }
    @Override
    public final void retryOrderNotYetStarted(final String orderId, final ExecutionStates state, final int retries) {
        super.log.logf(FQCN, WARN, null, retryOrderNotYetStarted$str(), orderId, state, retries);
    }
    private static final String retryOrderNotYetStarted = "MPTDISPATCHER000005: After some waiting, order id '%s' did not yet start for final processing at state '%s'. Flushing EM and refiring the CDI event... Number of retries [%s]";
    protected String retryOrderNotYetStarted$str() {
        return retryOrderNotYetStarted;
    }
    @Override
    public final void xsltTransformationError(final Throwable e, final String orderId, final Long bulkId, final Long milis) {
        super.log.logf(FQCN, ERROR, e, xsltTransformationError$str(), orderId, bulkId, milis);
    }
    private static final String xsltTransformationError = "MPTDISPATCHER000006: Error while applying XSLT and setting response to order [%s] belonging to bulk [%s]. Took '%d' ms";
    protected String xsltTransformationError$str() {
        return xsltTransformationError;
    }
    @Override
    public final void orderResultParsingError(final Throwable e, final String orderId, final Long bulkId) {
        super.log.logf(FQCN, ERROR, e, orderResultParsingError$str(), orderId, bulkId);
    }
    private static final String orderResultParsingError = "MPTDISPATCHER000007: Error while parsing order result and setting output line to order [%s] belonging to bulk [%s]";
    protected String orderResultParsingError$str() {
        return orderResultParsingError;
    }
    @Override
    public final void failureUnregisteringOrderFromFamily(final String orderId, final String family, final long bulkId) {
        super.log.logf(FQCN, WARN, null, failureUnregisteringOrderFromFamily$str(), orderId, family, bulkId);
    }
    private static final String failureUnregisteringOrderFromFamily = "MPTDISPATCHER000008: ResourceManager failed to unregister order [%s] family/bulkId [%s/%s]";
    protected String failureUnregisteringOrderFromFamily$str() {
        return failureUnregisteringOrderFromFamily;
    }
    @Override
    public final void publishingRequestToNA(final String prefixInfo, final Object clientId, final Object vlientUrl, final Object timeout, final Object description, final Object payload) {
        super.log.logf(FQCN, DEBUG, null, publishingRequestToNA$str(), prefixInfo, clientId, vlientUrl, timeout, description, payload);
    }
    private static final String publishingRequestToNA = "MPTDISPATCHER000009: [[%s]] dispatching request:\n=============================== request info ===============================\nclientId    = %s\nclientURL   = %s\ntimeout     = %s ms\ndescription = %s\n=========================== request payload start ==========================\n%s\n============================ request payload end  ==========================";
    protected String publishingRequestToNA$str() {
        return publishingRequestToNA;
    }
    @Override
    public final void dispatchAsyncEvent(final ASYNCH_TYPES type, final Request request) {
        super.log.logf(FQCN, INFO, null, dispatchAsyncEvent$str(), type, request);
    }
    private static final String dispatchAsyncEvent = "MPTDISPATCHER000010: Dispatching %s Event for request [%s]";
    protected String dispatchAsyncEvent$str() {
        return dispatchAsyncEvent;
    }
    @Override
    public final void signalNextFamilyMonitorWaitingThreadsFailure(final Throwable e) {
        super.log.logf(FQCN, ERROR, e, signalNextFamilyMonitorWaitingThreadsFailure$str());
    }
    private static final String signalNextFamilyMonitorWaitingThreadsFailure = "MPTDISPATCHER000011: Cache listener failed to signal next family monitor waiting threads";
    protected String signalNextFamilyMonitorWaitingThreadsFailure$str() {
        return signalNextFamilyMonitorWaitingThreadsFailure;
    }
    @Override
    public final void dispatchedAsyncEvent(final ASYNCH_TYPES type, final String opk, final Long milis) {
        super.log.logf(FQCN, INFO, null, dispatchedAsyncEvent$str(), type, opk, milis);
    }
    private static final String dispatchedAsyncEvent = "MPTDISPATCHER000012: Dispatched %s Event with OPK[%s] in %d ms";
    protected String dispatchedAsyncEvent$str() {
        return dispatchedAsyncEvent;
    }
    @Override
    public final void orderProcessorReception(final String orderId) {
        super.log.logf(FQCN, DEBUG, null, orderProcessorReception$str(), orderId);
    }
    private static final String orderProcessorReception = "MPTDISPATCHER000013: Order processor received order to process with id '%s'";
    protected String orderProcessorReception$str() {
        return orderProcessorReception;
    }
    @Override
    public final void orderProcessorReceptionEnd(final String orderId, final Long milis) {
        super.log.logf(FQCN, INFO, null, orderProcessorReceptionEnd$str(), orderId, milis);
    }
    private static final String orderProcessorReceptionEnd = "MPTDISPATCHER000014: Order processor processed order with id '%s' in %s ms";
    protected String orderProcessorReceptionEnd$str() {
        return orderProcessorReceptionEnd;
    }
    @Override
    public final void mptAdapterXmlGenerate(final String orderId, final Long bulkId) {
        super.log.logf(FQCN, DEBUG, null, mptAdapterXmlGenerate$str(), orderId, bulkId);
    }
    private static final String mptAdapterXmlGenerate = "MPTDISPATCHER000015: MPT Adapter going to generate XML payload for order id '%s' from bulk id '%s'";
    protected String mptAdapterXmlGenerate$str() {
        return mptAdapterXmlGenerate;
    }
    @Override
    public final void mptAdapterXmlGenerateEnd(final String orderId, final Long bulkId, final Long milis) {
        super.log.logf(FQCN, DEBUG, null, mptAdapterXmlGenerateEnd3$str(), orderId, bulkId, milis);
    }
    private static final String mptAdapterXmlGenerateEnd3 = "MPTDISPATCHER000016: MPT Adapter generated XML payload for order id '%s' from bulk id '%s' in %d ms";
    protected String mptAdapterXmlGenerateEnd3$str() {
        return mptAdapterXmlGenerateEnd3;
    }
    @Override
    public final void mptAdapterXmlGenerateEnd(final String orderId, final Long bulkId, final Long milis, final String xmlPayload) {
        super.log.logf(FQCN, DEBUG, null, mptAdapterXmlGenerateEnd4$str(), orderId, bulkId, milis, xmlPayload);
    }
    private static final String mptAdapterXmlGenerateEnd4 = "MPTDISPATCHER000017: MPT Adapter generated XML payload for order id '%s' from bulk id '%s' in %d ms. XML Payload -> \n %s \n";
    protected String mptAdapterXmlGenerateEnd4$str() {
        return mptAdapterXmlGenerateEnd4;
    }
    @Override
    public final void mptAdapterNAResponseXmlGeneration() {
        super.log.logf(FQCN, INFO, null, mptAdapterNAResponseXmlGeneration$str());
    }
    private static final String mptAdapterNAResponseXmlGeneration = "MPTDISPATCHER000018: MPT Adapter is transforming NA Response to XML...";
    protected String mptAdapterNAResponseXmlGeneration$str() {
        return mptAdapterNAResponseXmlGeneration;
    }
    @Override
    public final void mptAdapterNAResponseXmlGenerationEnd(final Long milis) {
        super.log.logf(FQCN, INFO, null, mptAdapterNAResponseXmlGenerationEnd$str(), milis);
    }
    private static final String mptAdapterNAResponseXmlGenerationEnd = "MPTDISPATCHER000019: MPT Adapter is transformed NA Response to XML in %d ms";
    protected String mptAdapterNAResponseXmlGenerationEnd$str() {
        return mptAdapterNAResponseXmlGenerationEnd;
    }
    @Override
    public final void orderProcessorGenerateRequest(final String orderId, final Long bulkId) {
        super.log.logf(FQCN, INFO, null, orderProcessorGenerateRequest$str(), orderId, bulkId);
    }
    private static final String orderProcessorGenerateRequest = "MPTDISPATCHER000020: Order processor will generate request for order id '%s' from bulk id '%s'...";
    protected String orderProcessorGenerateRequest$str() {
        return orderProcessorGenerateRequest;
    }
    @Override
    public final void orderProcessorGenerateRequestEnd(final String orderId, final Long bulkId, final Long milis) {
        super.log.logf(FQCN, INFO, null, orderProcessorGenerateRequestEnd$str(), orderId, bulkId, milis);
    }
    private static final String orderProcessorGenerateRequestEnd = "MPTDISPATCHER000021: Order processor generated request for order id '%s' from bulk id '%s' in %d ms";
    protected String orderProcessorGenerateRequestEnd$str() {
        return orderProcessorGenerateRequestEnd;
    }
    @Override
    public final void orderProcessorWaitOrderCompletion(final String orderId, final Long timeout) {
        super.log.logf(FQCN, INFO, null, orderProcessorWaitOrderCompletion$str(), orderId, timeout);
    }
    private static final String orderProcessorWaitOrderCompletion = "MPTDISPATCHER000022: Order processor will wait for order id '%s' to complete... Order defined timeout is '%d' ms";
    protected String orderProcessorWaitOrderCompletion$str() {
        return orderProcessorWaitOrderCompletion;
    }
    @Override
    public final void orderProcessorOrderCompletion(final String orderId, final Long milis) {
        super.log.logf(FQCN, INFO, null, orderProcessorOrderCompletion$str(), orderId, milis);
    }
    private static final String orderProcessorOrderCompletion = "MPTDISPATCHER000023: Order processor waited for order id '%s' to complete '%d' ms";
    protected String orderProcessorOrderCompletion$str() {
        return orderProcessorOrderCompletion;
    }
    @Override
    public final void orderProcessorWaitOrderCompletionFailure(final Throwable e, final String orderId, final String reason) {
        super.log.logf(FQCN, ERROR, e, orderProcessorWaitOrderCompletionFailure$str(), orderId, reason);
    }
    private static final String orderProcessorWaitOrderCompletionFailure = "MPTDISPATCHER000024: Order processor failed while waiting for order id '%s' to complete due to: %s";
    protected String orderProcessorWaitOrderCompletionFailure$str() {
        return orderProcessorWaitOrderCompletionFailure;
    }
    @Override
    public final void orderProcessorWaitOrderCompletionTimeout(final String orderId) {
        super.log.logf(FQCN, WARN, null, orderProcessorWaitOrderCompletionTimeout$str(), orderId);
    }
    private static final String orderProcessorWaitOrderCompletionTimeout = "MPTDISPATCHER000025: Order processor timed out while waiting for order id '%s'";
    protected String orderProcessorWaitOrderCompletionTimeout$str() {
        return orderProcessorWaitOrderCompletionTimeout;
    }
    @Override
    public final void orderProcessorWaitOrderCompletionOk(final Long milis, final String orderId) {
        super.log.logf(FQCN, INFO, null, orderProcessorWaitOrderCompletionOk$str(), milis, orderId);
    }
    private static final String orderProcessorWaitOrderCompletionOk = "MPTDISPATCHER000026: Order processor waited for '%d' ms for order id '%s' to complete... Firing CDI event to Order Handler...";
    protected String orderProcessorWaitOrderCompletionOk$str() {
        return orderProcessorWaitOrderCompletionOk;
    }
    @Override
    public final void orderProcessorOrderTimeoutEvalFailure(final String orderId) {
        super.log.logf(FQCN, ERROR, null, orderProcessorOrderTimeoutEvalFailure$str(), orderId);
    }
    private static final String orderProcessorOrderTimeoutEvalFailure = "MPTDISPATCHER000027: Order processor failed evaluating order id '%s' for timeout...";
    protected String orderProcessorOrderTimeoutEvalFailure$str() {
        return orderProcessorOrderTimeoutEvalFailure;
    }
    @Override
    public final void orderHandlerNewOrder(final String orderId) {
        super.log.logf(FQCN, INFO, null, orderHandlerNewOrder$str(), orderId);
    }
    private static final String orderHandlerNewOrder = "MPTDISPATCHER000028: Order handler will handle now order id '%s'";
    protected String orderHandlerNewOrder$str() {
        return orderHandlerNewOrder;
    }
    @Override
    public final void orderHandledNewOrder(final String orderId, final Long milis) {
        super.log.logf(FQCN, INFO, null, orderHandledNewOrder$str(), orderId, milis);
    }
    private static final String orderHandledNewOrder = "MPTDISPATCHER000029: Order handler handled order id '%s' in %d ms";
    protected String orderHandledNewOrder$str() {
        return orderHandledNewOrder;
    }
    @Override
    public final void orderHandlerOrderEvaluator(final String orderId, final OrderStatus status, final ExecutionStates state, final Long milis) {
        super.log.logf(FQCN, INFO, null, orderHandlerOrderEvaluator$str(), orderId, status, state, milis);
    }
    private static final String orderHandlerOrderEvaluator = "MPTDISPATCHER000030: Order handler evaluated order id '%s' to the following Status/ExecutionStatus [%s/%s] and took '%d' ms";
    protected String orderHandlerOrderEvaluator$str() {
        return orderHandlerOrderEvaluator;
    }
    @Override
    public final void orderHandlerApplyXSLTAndSetResponseToOrder(final String orderId) {
        super.log.logf(FQCN, INFO, null, orderHandlerApplyXSLTAndSetResponseToOrder$str(), orderId);
    }
    private static final String orderHandlerApplyXSLTAndSetResponseToOrder = "MPTDISPATCHER000031: Order handler will apply XSLT and set parsed response to order id '%s'";
    protected String orderHandlerApplyXSLTAndSetResponseToOrder$str() {
        return orderHandlerApplyXSLTAndSetResponseToOrder;
    }
    @Override
    public final void orderHandlerAppliedXSLT(final String orderId, final Long milis) {
        super.log.logf(FQCN, INFO, null, orderHandlerAppliedXSLT$str(), orderId, milis);
    }
    private static final String orderHandlerAppliedXSLT = "MPTDISPATCHER000032: Order handler applied XSLT and set parsed response to order id '%s' in '%d' ms";
    protected String orderHandlerAppliedXSLT$str() {
        return orderHandlerAppliedXSLT;
    }
    @Override
    public final void orderProviderHolderInit() {
        super.log.logf(FQCN, INFO, null, orderProviderHolderInit$str());
    }
    private static final String orderProviderHolderInit = "MPTDISPATCHER000033: Order Provider Holder initializing... Registering family resources cache Listener...";
    protected String orderProviderHolderInit$str() {
        return orderProviderHolderInit;
    }
    @Override
    public final void orderProviderHolderInitRegister() {
        super.log.logf(FQCN, INFO, null, orderProviderHolderInitRegister$str());
    }
    private static final String orderProviderHolderInitRegister = "MPTDISPATCHER000034: Order Provider Holder registered a fresh instance of a FamilyResources Listener to FamilyResources Cache";
    protected String orderProviderHolderInitRegister$str() {
        return orderProviderHolderInitRegister;
    }
    @Override
    public final void orderProviderHolderInitSuccess() {
        super.log.logf(FQCN, INFO, null, orderProviderHolderInitSuccess$str());
    }
    private static final String orderProviderHolderInitSuccess = "MPTDISPATCHER000035: Order Provider Holder initialized successfully...";
    protected String orderProviderHolderInitSuccess$str() {
        return orderProviderHolderInitSuccess;
    }
    @Override
    public final void orderProviderGet(final long operCatId) {
        super.log.logf(FQCN, INFO, null, orderProviderGet1$str(), operCatId);
    }
    private static final String orderProviderGet1 = "MPTDISPATCHER000036: Order Provider Holder getting provider for operation catalog id %d";
    protected String orderProviderGet1$str() {
        return orderProviderGet1;
    }
    @Override
    public final void orderProviderGet(final long operCatId, final long milis) {
        super.log.logf(FQCN, INFO, null, orderProviderGet2$str(), operCatId, milis);
    }
    private static final String orderProviderGet2 = "MPTDISPATCHER000037: Order Provider Holder fetched provider for operation catalog id %d and took %d ms";
    protected String orderProviderGet2$str() {
        return orderProviderGet2;
    }
    @Override
    public final void orderProviderRemove(final long operCatId) {
        super.log.logf(FQCN, WARN, null, orderProviderRemove1$str(), operCatId);
    }
    private static final String orderProviderRemove1 = "MPTDISPATCHER000038: Order Provider Holder is going to remove provider for operation catalog id %d";
    protected String orderProviderRemove1$str() {
        return orderProviderRemove1;
    }
    @Override
    public final void orderProviderRemove(final long operCatId, final long milis) {
        super.log.logf(FQCN, WARN, null, orderProviderRemove2$str(), operCatId, milis);
    }
    private static final String orderProviderRemove2 = "MPTDISPATCHER000039: Order Provider Holder removed provider for operation catalog id %d and took %d ms";
    protected String orderProviderRemove2$str() {
        return orderProviderRemove2;
    }
    @Override
    public final void orderProviderRemoveExclusiveKey(final String orderId) {
        super.log.logf(FQCN, INFO, null, orderProviderRemoveExclusiveKey1$str(), orderId);
    }
    private static final String orderProviderRemoveExclusiveKey1 = "MPTDISPATCHER000040: Order Provider Holder is going to remove exclusive key from cache for order id '%s'...";
    protected String orderProviderRemoveExclusiveKey1$str() {
        return orderProviderRemoveExclusiveKey1;
    }
    @Override
    public final void orderProviderRemoveExclusiveKey(final String orderId, final long milis) {
        super.log.logf(FQCN, INFO, null, orderProviderRemoveExclusiveKey2$str(), orderId, milis);
    }
    private static final String orderProviderRemoveExclusiveKey2 = "MPTDISPATCHER000041: Order Provider Holder is removed exclusive key from cache for order id '%s' and took %d ms";
    protected String orderProviderRemoveExclusiveKey2$str() {
        return orderProviderRemoveExclusiveKey2;
    }
    @Override
    public final void orderProviderSignalGuard() {
        super.log.logf(FQCN, INFO, null, orderProviderSignalGuard$str());
    }
    private static final String orderProviderSignalGuard = "MPTDISPATCHER000042: Order Provider Holder signalled next ExclusiveOrderFetcherGuard waiting thread...";
    protected String orderProviderSignalGuard$str() {
        return orderProviderSignalGuard;
    }
    @Override
    public final void orderProviderDestroy() {
        super.log.logf(FQCN, INFO, null, orderProviderDestroy$str());
    }
    private static final String orderProviderDestroy = "MPTDISPATCHER000043: Order Provider Holder is shutting down... Removing family resources cache listener...";
    protected String orderProviderDestroy$str() {
        return orderProviderDestroy;
    }
    @Override
    public final void orderProviderDestroyFinish() {
        super.log.logf(FQCN, INFO, null, orderProviderDestroyFinish$str());
    }
    private static final String orderProviderDestroyFinish = "MPTDISPATCHER000044: Order Provider Holder finished shutting down... Removed family resources cache listener and flushed cache!";
    protected String orderProviderDestroyFinish$str() {
        return orderProviderDestroyFinish;
    }
    @Override
    public final void orderProviderRemoveBulk(final String orderKey, final Long bulkId) {
        super.log.logf(FQCN, INFO, null, orderProviderRemoveBulk$str(), orderKey, bulkId);
    }
    private static final String orderProviderRemoveBulk = "MPTDISPATCHER000045: Order Provider found no more bulks for orderKey '%s'. Destroying instance OrderKeyOrders while removing bulk '%s'";
    protected String orderProviderRemoveBulk$str() {
        return orderProviderRemoveBulk;
    }
    @Override
    public final void orderProviderAddBulk(final Long bulkId, final int batchSize) {
        super.log.logf(FQCN, INFO, null, orderProviderAddBulk2$str(), bulkId, batchSize);
    }
    private static final String orderProviderAddBulk2 = "MPTDISPATCHER000046: Order Provider is adding a new instance for bulk id '%d' with a batch size of %d";
    protected String orderProviderAddBulk2$str() {
        return orderProviderAddBulk2;
    }
    @Override
    public final void orderProviderAddBulk(final int orders, final Long bulkId, final long milis) {
        super.log.logf(FQCN, INFO, null, orderProviderAddBulk3$str(), orders, bulkId, milis);
    }
    private static final String orderProviderAddBulk3 = "MPTDISPATCHER000047: Order Provider stored in local cache %s orders from bulk id '%d' query iterator. Took %s ms to initialize...";
    protected String orderProviderAddBulk3$str() {
        return orderProviderAddBulk3;
    }
    @Override
    public final void orderProviderAlreadyAddedBulk(final Long bulkId) {
        super.log.logf(FQCN, WARN, null, orderProviderAlreadyAddedBulk$str(), bulkId);
    }
    private static final String orderProviderAlreadyAddedBulk = "MPTDISPATCHER000048: Order Provider already processed and stored bulk id '%d'!";
    protected String orderProviderAlreadyAddedBulk$str() {
        return orderProviderAlreadyAddedBulk;
    }
    @Override
    public final void orderProviderNextOrderForBulk(final Long bulkId) {
        super.log.logf(FQCN, INFO, null, orderProviderNextOrderForBulk1$str(), bulkId);
    }
    private static final String orderProviderNextOrderForBulk1 = "MPTDISPATCHER000049: Order Provider is fetching next available order from bulk id '%d'...";
    protected String orderProviderNextOrderForBulk1$str() {
        return orderProviderNextOrderForBulk1;
    }
    @Override
    public final void orderProviderNextOrderForBulk(final Long bulkId, final long milis) {
        super.log.logf(FQCN, INFO, null, orderProviderNextOrderForBulk2$str(), bulkId, milis);
    }
    private static final String orderProviderNextOrderForBulk2 = "MPTDISPATCHER000050: Order Provider finished trying to fetch next available order from bulk id '%d' and took %d ms";
    protected String orderProviderNextOrderForBulk2$str() {
        return orderProviderNextOrderForBulk2;
    }
    @Override
    public final void orderProviderNextOrderForBulkMismatch(final String orderId) {
        super.log.logf(FQCN, WARN, null, orderProviderNextOrderForBulkMismatch$str(), orderId);
    }
    private static final String orderProviderNextOrderForBulkMismatch = "MPTDISPATCHER000051: Order Provider fetched an already processed order id '%s' (should be in a NOT_STARTED state!!!)";
    protected String orderProviderNextOrderForBulkMismatch$str() {
        return orderProviderNextOrderForBulkMismatch;
    }
    @Override
    public final void orderProviderExclusiveGuardNoResult(final String query, final String cause) {
        super.log.logf(FQCN, ERROR, null, orderProviderExclusiveGuardNoResult2$str(), query, cause);
    }
    private static final String orderProviderExclusiveGuardNoResult2 = "MPTDISPATCHER000052: Order Provider exclusive order fetcher guard had no result for query [%s]. Cause: %s";
    protected String orderProviderExclusiveGuardNoResult2$str() {
        return orderProviderExclusiveGuardNoResult2;
    }
    @Override
    public final void orderProviderExclusiveGuardResult(final Long bulkId, final String orderId) {
        super.log.logf(FQCN, INFO, null, orderProviderExclusiveGuardResult$str(), bulkId, orderId);
    }
    private static final String orderProviderExclusiveGuardResult = "MPTDISPATCHER000053: Order Provider exclusive order fetcher guard found order in exclusivity for bulkid %d. Order id  found '%s'";
    protected String orderProviderExclusiveGuardResult$str() {
        return orderProviderExclusiveGuardResult;
    }
    @Override
    public final void orderProviderExclusiveGuardFailure(final Throwable e, final String cause) {
        super.log.logf(FQCN, ERROR, e, orderProviderExclusiveGuardFailure$str(), cause);
    }
    private static final String orderProviderExclusiveGuardFailure = "MPTDISPATCHER000054: Order Provider exclusive order fetcher guard failed due to: %s";
    protected String orderProviderExclusiveGuardFailure$str() {
        return orderProviderExclusiveGuardFailure;
    }
    @Override
    public final void orderProviderExclusiveGuardNoResult(final Long bulkId) {
        super.log.logf(FQCN, WARN, null, orderProviderExclusiveGuardNoResult1$str(), bulkId);
    }
    private static final String orderProviderExclusiveGuardNoResult1 = "MPTDISPATCHER000055: Order Provider exclusive order fetcher guard found no order in exclusivity for bulk id %d";
    protected String orderProviderExclusiveGuardNoResult1$str() {
        return orderProviderExclusiveGuardNoResult1;
    }
}
