package pt.ptinovacao.na.portal.core.mpt._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import javax.naming.InsufficientResourcesException;
import pt.ptinovacao.na.portal.webui.restful.commons.model.TransitionStates;
import java.lang.String;
import javax.persistence.EntityNotFoundException;
import java.lang.Exception;
import java.lang.Integer;
import pt.ptinovacao.na.portal.core.mpt.engine.exceptions.MptEngineException;
import java.lang.Long;
import java.lang.Throwable;
import java.util.Arrays;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageBundleImplementor", date = "2025-07-02T14:14:53+0100")
public class EngineMessages_$bundle implements EngineMessages, Serializable {
    private static final long serialVersionUID = 1L;
    protected EngineMessages_$bundle() {}
    public static final EngineMessages_$bundle INSTANCE = new EngineMessages_$bundle();
    protected Object readResolve() {
        return INSTANCE;
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    private static final String dataManagerBatchStepException = "MPTENGINE000200: Failed sending entity %s to database during batch step %s";
    protected String dataManagerBatchStepException$str() {
        return dataManagerBatchStepException;
    }
    @Override
    public final CommonDataManagerException dataManagerBatchStepException(final Throwable e, final String entityName, final String stepName) {
        final CommonDataManagerException result = new CommonDataManagerException(String.format(getLoggingLocale(), dataManagerBatchStepException$str(), entityName, stepName), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String webDavException = "MPTENGINE000201: Failed uploading requested file [%s] to webdav path [%s]";
    protected String webDavException$str() {
        return webDavException;
    }
    @Override
    public final Exception webDavException(final Throwable e, final String filename, final String path) {
        final Exception result = new Exception(String.format(getLoggingLocale(), webDavException$str(), filename, path), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String deciderException = "MPTENGINE000202: Unpredicted workflow error while making a decision on state [%s]";
    protected String deciderException$str() {
        return deciderException;
    }
    @Override
    public final Exception deciderException(final Throwable e, final TransitionStates state) {
        final Exception result = new Exception(String.format(getLoggingLocale(), deciderException$str(), state), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String bulkAbsenceException = "MPTENGINE000203: Bulk with id [%s] does not exist or has been removed. Operation [%s]. Family [%s]";
    protected String bulkAbsenceException$str() {
        return bulkAbsenceException;
    }
    @Override
    public final EntityNotFoundException bulkAbsenceException(final Throwable e, final Long bulkId, final String operation, final String family) {
        final EntityNotFoundException result = new EntityNotFoundException(String.format(getLoggingLocale(), bulkAbsenceException$str(), bulkId, operation, family));
        result.initCause(e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String bulkInsufficientResourcesException = "MPTENGINE000204: Bulk with id [%s] could not start due to insufficient resources. Please adjust the configuration parameter 'batchGlobalThreads'. Actual max size is %s! Info: [Operation [%s]. Family [%s]]";
    protected String bulkInsufficientResourcesException$str() {
        return bulkInsufficientResourcesException;
    }
    @Override
    public final InsufficientResourcesException bulkInsufficientResourcesException(final Throwable e, final Long bulkId, final int size, final String operation, final String family) {
        final InsufficientResourcesException result = new InsufficientResourcesException(String.format(getLoggingLocale(), bulkInsufficientResourcesException$str(), bulkId, size, operation, family));
        result.initCause(e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String bulkRecoverException = "MPTENGINE000205: Could not recover bulk with id [%s] using strategy [%s]. Last known recover summary: %s";
    protected String bulkRecoverException$str() {
        return bulkRecoverException;
    }
    @Override
    public final MptEngineException bulkRecoverException(final Throwable e, final Long bulkId, final String strategy, final String summary) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), bulkRecoverException$str(), bulkId, strategy, summary), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String batchAfterJobListenerFailure = "MPTENGINE000206: Failure in batch listener after job execution";
    protected String batchAfterJobListenerFailure$str() {
        return batchAfterJobListenerFailure;
    }
    @Override
    public final MptEngineException batchAfterJobListenerFailure(final Throwable e) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), batchAfterJobListenerFailure$str()), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String getRequestFailure = "MPTENGINE000207: Failure while retrieving the cached request";
    protected String getRequestFailure$str() {
        return getRequestFailure;
    }
    @Override
    public final MptEngineException getRequestFailure(final Throwable e) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), getRequestFailure$str()), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String responseAgentFailure = "MPTENGINE000208: Response agent batchlet found missing mandatory parameters. Agent port [%s] - Agent host [%s] - Bulk id [%s]";
    protected String responseAgentFailure$str() {
        return responseAgentFailure;
    }
    @Override
    public final MptEngineException responseAgentFailure(final Integer port, final String host, final Long id) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), responseAgentFailure$str(), port, host, id));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String eventBusFailure = "MPTENGINE000209: MPT Engine Event BUS injected with null while building batchlet '%s'";
    protected String eventBusFailure$str() {
        return eventBusFailure;
    }
    @Override
    public final MptEngineException eventBusFailure(final String batchletName) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), eventBusFailure$str(), batchletName));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String resourcesEngineInitFailure = "MPTENGINE000210: MPT Engine Resources build failure due to: ACCESS FAILURE TO PersistenceStore/ResourceManager MODULE(S)";
    protected String resourcesEngineInitFailure$str() {
        return resourcesEngineInitFailure;
    }
    @Override
    public final MptEngineException resourcesEngineInitFailure(final Throwable e) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), resourcesEngineInitFailure$str()), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String engineSchedulerTaskStopFailure = "MPTENGINE000211: Engine scheduler task failed/timed out while stopping batch workflow execution id[%s] for bulkId[%s]";
    protected String engineSchedulerTaskStopFailure$str() {
        return engineSchedulerTaskStopFailure;
    }
    @Override
    public final MptEngineException engineSchedulerTaskStopFailure(final Long batchExecutionId, final Long bulkId) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), engineSchedulerTaskStopFailure$str(), batchExecutionId, bulkId));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String engineSchedulerTaskStartFailure = "MPTENGINE000212: Engine scheduler task failed starting batch workflow execution id[%s] for bulkId[%s]";
    protected String engineSchedulerTaskStartFailure$str() {
        return engineSchedulerTaskStartFailure;
    }
    @Override
    public final MptEngineException engineSchedulerTaskStartFailure(final Long batchExecutionId, final Long bulkId) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), engineSchedulerTaskStartFailure$str(), batchExecutionId, bulkId));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String engineSchedulerTaskRegisterFailure = "MPTENGINE000213: Engine scheduler task failed while registering request to resource manager for batch workflow execution id[%s] with bulkId[%s]";
    protected String engineSchedulerTaskRegisterFailure$str() {
        return engineSchedulerTaskRegisterFailure;
    }
    @Override
    public final MptEngineException engineSchedulerTaskRegisterFailure(final Long batchExecutionId, final Long bulkId) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), engineSchedulerTaskRegisterFailure$str(), batchExecutionId, bulkId));
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String recoverEngineInitFailure = "MPTENGINE000214: Recover initializion failure";
    protected String recoverEngineInitFailure$str() {
        return recoverEngineInitFailure;
    }
    @Override
    public final MptEngineException recoverEngineInitFailure(final Throwable e) {
        final MptEngineException result = new MptEngineException(String.format(getLoggingLocale(), recoverEngineInitFailure$str()), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
}
