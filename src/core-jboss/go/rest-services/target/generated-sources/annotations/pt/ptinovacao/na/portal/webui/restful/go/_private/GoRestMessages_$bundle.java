package pt.ptinovacao.na.portal.webui.restful.go._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import pt.ptinovacao.na.portal.db.commons.persistence.CommonDataManagerException;
import java.lang.Throwable;
import java.lang.String;
import java.util.Arrays;
import java.lang.Exception;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageBundleImplementor", date = "2025-07-02T14:14:53+0100")
public class GoRestMessages_$bundle implements GoRestMessages, Serializable {
    private static final long serialVersionUID = 1L;
    protected GoRestMessages_$bundle() {}
    public static final GoRestMessages_$bundle INSTANCE = new GoRestMessages_$bundle();
    protected Object readResolve() {
        return INSTANCE;
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    private static final String TemplateNotFound = "GOSERVICE000101: Failed to find template [%s]";
    protected String TemplateNotFound$str() {
        return TemplateNotFound;
    }
    @Override
    public final CommonDataManagerException TemplateNotFound(final Throwable e, final String template) {
        final CommonDataManagerException result = new CommonDataManagerException(String.format(getLoggingLocale(), TemplateNotFound$str(), template), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String xmlTemplateAbsent = "GOSERVICE000102: Failed to find xml from template [%s]";
    protected String xmlTemplateAbsent$str() {
        return xmlTemplateAbsent;
    }
    @Override
    public final CommonDataManagerException xmlTemplateAbsent(final Throwable e, final String template) {
        final CommonDataManagerException result = new CommonDataManagerException(String.format(getLoggingLocale(), xmlTemplateAbsent$str(), template), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
    private static final String receivedInvalidOrderkey = "GOSERVICE000103: Received invalid order key [%s], aborting operation...";
    protected String receivedInvalidOrderkey$str() {
        return receivedInvalidOrderkey;
    }
    @Override
    public final Exception receivedInvalidOrderkey(final Throwable e, final String orderkey) {
        final Exception result = new Exception(String.format(getLoggingLocale(), receivedInvalidOrderkey$str(), orderkey), e);
        final StackTraceElement[] st = result.getStackTrace();
        result.setStackTrace(Arrays.copyOfRange(st, 1, st.length));
        return result;
    }
}
