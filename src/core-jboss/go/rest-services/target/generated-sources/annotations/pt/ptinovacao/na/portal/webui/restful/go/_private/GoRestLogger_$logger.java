package pt.ptinovacao.na.portal.webui.restful.go._private;

import java.util.Locale;
import java.io.Serializable;
import javax.annotation.Generated;
import org.jboss.logging.DelegatingBasicLogger;
import org.jboss.logging.BasicLogger;
import java.lang.Object;
import java.lang.String;
import org.jboss.logging.Logger;


import static org.jboss.logging.Logger.Level.ERROR;
import static org.jboss.logging.Logger.Level.INFO;

/**
 * Warning this class consists of generated code.
 */
@Generated(value = "org.jboss.logging.processor.generator.model.MessageLoggerImplementor", date = "2025-07-02T14:14:53+0100")
public class GoRestLogger_$logger extends DelegatingBasicLogger implements GoRestLogger, BasicLogger, Serializable {
    private static final long serialVersionUID = 1L;
    private static final String FQCN = GoRestLogger_$logger.class.getName();
    public GoRestLogger_$logger(final Logger log) {
        super(log);
    }
    private static final Locale LOCALE = Locale.ROOT;
    protected Locale getLoggingLocale() {
        return LOCALE;
    }
    @Override
    public final void templateNotFound(final String operation, final String family, final String rfs) {
        super.log.logf(FQCN, ERROR, null, templateNotFound$str(), operation, family, rfs);
    }
    private static final String templateNotFound = "GOSERVICE000010: No template named [%s] of rs \"[%s]\" and family \"[%s]\" found in database";
    protected String templateNotFound$str() {
        return templateNotFound;
    }
    @Override
    public final void xmlTemplateNotFound(final String operation, final String family, final String rfs) {
        super.log.logf(FQCN, ERROR, null, xmlTemplateNotFound$str(), operation, family, rfs);
    }
    private static final String xmlTemplateNotFound = "GOSERVICE000011: No xml content found on template [%s] in database. {family: [%s], rfs: [%s] }";
    protected String xmlTemplateNotFound$str() {
        return xmlTemplateNotFound;
    }
    @Override
    public final void receivedOrderKey(final String prefixInfo, final String OrderKey) {
        super.log.logf(FQCN, ERROR, null, receivedOrderKey$str(), prefixInfo, OrderKey);
    }
    private static final String receivedOrderKey = "GOSERVICE000012: [1m[34m[[%s]][0m received order Key: [%s] ";
    protected String receivedOrderKey$str() {
        return receivedOrderKey;
    }
    @Override
    public final void exceptionThrown(final String prefixInfo, final String exceptionData) {
        super.log.logf(FQCN, ERROR, null, exceptionThrown$str(), prefixInfo, exceptionData);
    }
    private static final String exceptionThrown = "GOSERVICE000019: [1m[31m[[%s]][0m \n %s";
    protected String exceptionThrown$str() {
        return exceptionThrown;
    }
    @Override
    public final void publishingRequestToNA(final String prefixInfo, final Object clientId, final Object clientUrl, final Object timeout, final Object description, final Object payload) {
        super.log.logf(FQCN, INFO, null, publishingRequestToNA$str(), prefixInfo, clientId, clientUrl, timeout, description, payload);
    }
    private static final String publishingRequestToNA = "GOSERVICE000020: [1m[34m[[%s]][0m  publishing request to NA Core:\n[1m[34m================ request info         ===============[0m\n[1m[34mclientId    = [0m%s\n[1m[34mclientURL   = [0m%s\n[1m[34mtimeout     = [0m%s seconds\n[1m[34mdescription = [0m%s\n[1m[34m================ request payload start ===============[0m\n%s\n[1m[34m================ request payload end   ===============[0m";
    protected String publishingRequestToNA$str() {
        return publishingRequestToNA;
    }
    @Override
    public final void receivedOrderEvent(final String prefixInfo, final Object orderKey, final Object clientId, final Object dateCreated, final Object orderResultString, final Object orderResult) {
        super.log.logf(FQCN, INFO, null, receivedOrderEvent$str(), prefixInfo, orderKey, clientId, dateCreated, orderResultString, orderResult);
    }
    private static final String receivedOrderEvent = "GOSERVICE000021: [1m[34m[[%s]][0m  received orderEvent\n[1m[34morderKey          = [0m%s\n[1m[34mclientId          = [0m%s\n[1m[34mdateCreated       = [0m%s\n[1m[34morderResultString = [0m%s\n[1m[34morderResult       = [0m%s";
    protected String receivedOrderEvent$str() {
        return receivedOrderEvent;
    }
    @Override
    public final void receivedRequest(final String prefixInfo, final Object parameters, final Object url) {
        super.log.logf(FQCN, INFO, null, receivedRequest$str(), prefixInfo, parameters, url);
    }
    private static final String receivedRequest = "GOSERVICE000022: [1m[34m[[%s]][0m  received Request\n[1m[34mparameters = [0m%s\n[1m[34mfull Url   = [0m%s";
    protected String receivedRequest$str() {
        return receivedRequest;
    }
}
